/**
 * 用户行为数据处理器
 * 用于清洗、分析和提取用户行为模式
 */

const XLSX = require('xlsx');
const { debounce, groupBy, sortBy, filter, map, reduce } = require('lodash');

class UserBehaviorProcessor {
  constructor() {
    this.behaviorPatterns = new Map();
    this.userSessions = new Map();
    this.recommendationRules = this.initializeRules();
  }

  /**
   * 初始化推荐规则
   */
  initializeRules() {
    return {
      // 网关产品核心流程
      gatewayFlow: [
        { action: 'CREATE_DOMAIN', nextRecommended: ['CREATE_SOURCE'], probability: 0.85 },
        { action: 'CREATE_SOURCE', nextRecommended: ['CREATE_SERVICE'], probability: 0.82 },
        { action: 'CREATE_SERVICE', nextRecommended: ['CREATE_ROUTER'], probability: 0.78 },
        { action: 'CREATE_ROUTER', nextRecommended: ['ENABLE_AUTH'], probability: 0.80 },
        { action: 'ENABLE_AUTH', nextRecommended: ['PUBLISH_ROUTER'], probability: 0.75 }
      ],
      
      // 高频操作序列
      frequentPatterns: [
        ['CREATE_DOMAIN', 'CREATE_SOURCE', 'CREATE_SERVICE'],
        ['CREATE_SERVICE', 'CREATE_ROUTER', 'ENABLE_AUTH'],
        ['CREATE_ROUTER', 'PUBLISH_ROUTER'],
        ['ENABLE_AUTH', 'CONFIG_POLICY']
      ],

      // 用户停留时间阈值（毫秒）
      stuckThresholds: {
        'CREATE_SERVICE': 300000, // 5分钟
        'CREATE_ROUTER': 240000,  // 4分钟
        'ENABLE_AUTH': 180000     // 3分钟
      }
    };
  }

  /**
   * 处理Excel数据文件
   */
  processExcelData(filePath) {
    try {
      const workbook = XLSX.readFile(filePath);
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const rawData = XLSX.utils.sheet_to_json(worksheet);
      
      return this.cleanAndStructureData(rawData);
    } catch (error) {
      console.error('处理Excel文件失败:', error);
      throw error;
    }
  }

  /**
   * 数据清洗和结构化
   */
  cleanAndStructureData(rawData) {
    // 1. 过滤有效数据
    const validData = filter(rawData, (row) => {
      return row['客户端时间'] && 
             (row['页面URL（含参数）'] || row['接口地址']) &&
             row['接口是否成功'] !== false;
    });

    // 2. 按时间排序
    const sortedData = sortBy(validData, ['客户端时间']);

    // 3. 提取用户行为序列
    const behaviorSequence = this.extractBehaviorSequence(sortedData);

    // 4. 识别会话边界
    const sessions = this.identifySessions(behaviorSequence);

    return {
      rawData: sortedData,
      behaviorSequence,
      sessions,
      statistics: this.calculateStatistics(sessions)
    };
  }

  /**
   * 提取用户行为序列
   */
  extractBehaviorSequence(data) {
    return map(data, (row) => {
      const pageUrl = row['页面URL（含参数）'] || '';
      const apiName = row['接口地址'] || '';
      const timestamp = row['客户端时间'];

      // 识别行为类型
      const actionType = this.identifyActionType(pageUrl, apiName);
      
      return {
        timestamp,
        actionType,
        pageUrl,
        apiName,
        success: row['接口是否成功'],
        duration: row['接口耗时'] || 0,
        context: this.extractContext(pageUrl, apiName)
      };
    });
  }

  /**
   * 识别行为类型
   */
  identifyActionType(pageUrl, apiName) {
    // 基于URL和API名称识别具体操作
    if (pageUrl.includes('/domain/') || apiName.includes('Domain')) {
      return 'DOMAIN_OPERATION';
    }
    if (pageUrl.includes('/source/') || apiName.includes('Source')) {
      return 'SOURCE_OPERATION';
    }
    if (pageUrl.includes('/service/') || apiName.includes('Service')) {
      return 'SERVICE_OPERATION';
    }
    if (pageUrl.includes('/router/') || apiName.includes('Router')) {
      return 'ROUTER_OPERATION';
    }
    if (pageUrl.includes('/auth/') || apiName.includes('Auth')) {
      return 'AUTH_OPERATION';
    }
    if (apiName.includes('Create')) {
      return 'CREATE_OPERATION';
    }
    if (apiName.includes('Update')) {
      return 'UPDATE_OPERATION';
    }
    if (apiName.includes('Delete')) {
      return 'DELETE_OPERATION';
    }
    
    return 'OTHER_OPERATION';
  }

  /**
   * 提取上下文信息
   */
  extractContext(pageUrl, apiName) {
    const context = {};
    
    // 提取网关ID
    const gatewayMatch = pageUrl.match(/gateway\/([^\/\?]+)/);
    if (gatewayMatch) {
      context.gatewayId = gatewayMatch[1];
    }

    // 提取应用信息
    const appMatch = pageUrl.match(/app-list\/([^\/\?]+)/);
    if (appMatch) {
      context.appId = appMatch[1];
    }

    // 提取名称参数
    const nameMatch = pageUrl.match(/name=([^&]+)/);
    if (nameMatch) {
      context.resourceName = decodeURIComponent(nameMatch[1]);
    }

    return context;
  }

  /**
   * 识别用户会话
   */
  identifySessions(behaviorSequence) {
    const sessions = [];
    let currentSession = null;
    const sessionTimeout = 30 * 60 * 1000; // 30分钟超时

    behaviorSequence.forEach((behavior, index) => {
      const currentTime = behavior.timestamp * 24 * 60 * 60 * 1000; // 转换为毫秒
      
      if (!currentSession || 
          (currentTime - currentSession.lastActivity > sessionTimeout)) {
        // 开始新会话
        if (currentSession) {
          sessions.push(currentSession);
        }
        
        currentSession = {
          sessionId: `session_${Date.now()}_${index}`,
          startTime: currentTime,
          lastActivity: currentTime,
          behaviors: [behavior],
          context: behavior.context
        };
      } else {
        // 继续当前会话
        currentSession.behaviors.push(behavior);
        currentSession.lastActivity = currentTime;
      }
    });

    if (currentSession) {
      sessions.push(currentSession);
    }

    return sessions;
  }

  /**
   * 计算统计信息
   */
  calculateStatistics(sessions) {
    const stats = {
      totalSessions: sessions.length,
      avgSessionDuration: 0,
      actionFrequency: {},
      sequencePatterns: {},
      conversionRates: {}
    };

    // 计算平均会话时长
    const totalDuration = reduce(sessions, (sum, session) => {
      return sum + (session.lastActivity - session.startTime);
    }, 0);
    stats.avgSessionDuration = totalDuration / sessions.length;

    // 统计行为频次
    sessions.forEach(session => {
      session.behaviors.forEach(behavior => {
        stats.actionFrequency[behavior.actionType] = 
          (stats.actionFrequency[behavior.actionType] || 0) + 1;
      });
    });

    // 分析序列模式
    sessions.forEach(session => {
      const sequence = session.behaviors.map(b => b.actionType);
      for (let i = 0; i < sequence.length - 1; i++) {
        const pattern = `${sequence[i]} -> ${sequence[i + 1]}`;
        stats.sequencePatterns[pattern] = 
          (stats.sequencePatterns[pattern] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * 生成推荐建议
   */
  generateRecommendation(currentUserContext) {
    const { lastAction, currentPage, sessionDuration, userType } = currentUserContext;
    
    // 基于规则的推荐
    const ruleBasedRec = this.getRuleBasedRecommendation(lastAction);
    
    // 基于模式的推荐
    const patternBasedRec = this.getPatternBasedRecommendation(currentUserContext);
    
    // 基于用户类型的推荐
    const userTypeRec = this.getUserTypeRecommendation(userType, lastAction);

    return {
      primary: ruleBasedRec,
      alternative: patternBasedRec,
      userSpecific: userTypeRec,
      confidence: this.calculateConfidence(ruleBasedRec, patternBasedRec),
      reasoning: this.generateReasoning(lastAction, ruleBasedRec)
    };
  }

  /**
   * 基于规则的推荐
   */
  getRuleBasedRecommendation(lastAction) {
    const rule = this.recommendationRules.gatewayFlow.find(r => r.action === lastAction);
    if (rule) {
      return {
        actions: rule.nextRecommended,
        probability: rule.probability,
        type: 'rule_based'
      };
    }
    return null;
  }

  /**
   * 基于模式的推荐
   */
  getPatternBasedRecommendation(context) {
    // 这里可以集成机器学习模型或更复杂的模式匹配
    // 暂时返回基于频繁模式的推荐
    const frequentNext = this.findFrequentNextActions(context.lastAction);
    return {
      actions: frequentNext,
      type: 'pattern_based'
    };
  }

  /**
   * 基于用户类型的推荐
   */
  getUserTypeRecommendation(userType, lastAction) {
    const recommendations = {
      'new_user': {
        'CREATE_DOMAIN': ['查看域名配置指南', '设置基础路由规则'],
        'CREATE_SERVICE': ['配置健康检查', '设置负载均衡']
      },
      'experienced_user': {
        'CREATE_ROUTER': ['配置高级路由规则', '启用鉴权策略'],
        'ENABLE_AUTH': ['配置自定义策略', '设置限流规则']
      }
    };

    return recommendations[userType]?.[lastAction] || [];
  }

  /**
   * 计算推荐置信度
   */
  calculateConfidence(ruleRec, patternRec) {
    let confidence = 0.5; // 基础置信度
    
    if (ruleRec && ruleRec.probability) {
      confidence = Math.max(confidence, ruleRec.probability);
    }
    
    if (patternRec && patternRec.actions.length > 0) {
      confidence += 0.2; // 模式匹配加成
    }
    
    return Math.min(confidence, 0.95); // 最高95%置信度
  }

  /**
   * 生成推荐理由
   */
  generateReasoning(lastAction, recommendation) {
    const reasoningTemplates = {
      'CREATE_DOMAIN': '基于85%用户的成功路径，建议您接下来配置来源',
      'CREATE_SOURCE': '大多数用户在配置来源后会立即创建对应的服务',
      'CREATE_SERVICE': '为了让服务生效，建议您创建路由规则',
      'CREATE_ROUTER': '80%的用户会为新路由启用鉴权策略以确保安全'
    };

    return reasoningTemplates[lastAction] || '基于用户行为分析的智能推荐';
  }

  /**
   * 查找频繁的下一步操作
   */
  findFrequentNextActions(currentAction) {
    // 基于历史数据分析，返回最可能的下一步操作
    const patterns = this.recommendationRules.frequentPatterns;
    const nextActions = [];

    patterns.forEach(pattern => {
      const currentIndex = pattern.indexOf(currentAction);
      if (currentIndex >= 0 && currentIndex < pattern.length - 1) {
        nextActions.push(pattern[currentIndex + 1]);
      }
    });

    return [...new Set(nextActions)]; // 去重
  }
}

module.exports = UserBehaviorProcessor;
