const { getESLintConfig } = require('@iceworks/spec');

module.exports = getESLintConfig('react-ts', {
  extends: ['plugin:prettier/recommended'],
  rules: {
    'react/jsx-filename-extension': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    'react-hooks/rules-of-hooks': 'off',
    '@typescript-eslint/indent': 'off',
    'react-hooks/exhaustive-deps': 'off',
    'max-len': 'off',
    '@iceworks/best-practices/recommend-polyfill': 'warn',
    'no-tabs': 'off',
    'react/no-array-index-key': 'off',
    'guard-for-in': 'off',
    'no-await-in-loop': 'off',
    'no-nested-ternary': 'off',
    '@typescript-eslint/no-shadow': 'warn',
    'prefer-const': 'warn',
    'array-callback-return': 'warn',
    '@typescript-eslint/consistent-type-assertions': 'off',
    'no-loop-func': 'warn',
    'no-return-assign': 'warn',
    'react/no-children-prop': 'warn',
    'no-redeclare': 'warn',
    'react/no-unused-prop-types': 'warn',
  },
});
