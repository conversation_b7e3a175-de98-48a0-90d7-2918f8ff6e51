import React from 'react';
import './virtual:windi.css';
// import './utils/ydIntlInitiator';
// import './utils/emitter';
import './utils/globalLib';
import { Route, Switch } from 'react-router-dom';
import GatewayDetail from './pages/$regionId/gateway/$id';
import SaveToken from './components/shared/DevToken';
import './styles/index.less';
import PreEnvSelector from './components/shared/PreEnvSelector';
import { IS_PRE, IS_PRE_OR_LOCAL } from './constants';
import {
  useAuth,
  useMaintaining,
  useMockUser,
  useRegionGray,
  useWhiteList,
} from './components/auth/useAuth';
import Auth from './pages/auth';
import { Loading } from '@ali/cnd';
import NotAllowed from './pages/notAllowed';
import { OpAnnouncement } from './components/shared/OpAnnouncement';
import Maintaining from './pages/maintaining';
import { MockUser } from './components/shared/MockUser';
import { MultiVersionSelector } from './components/shared/MultiVersionSelector';
import { MessengerRegionBar } from './components/shared/MessengerRegionBar';

import MessengerResourceGroupBar from './components/shared/MessengerResourceGroupBar';
import { CommonErrorBoundary } from './components/shared/ErrorFallback';
import AppProvider from './components/app-provider';
import AiGatewayDetail from './pages/$regionId/ai-gateway/$id';
import CachedData from './utils/cacheData';

export default (app, App, history) => {
  window.hashHistory = history;

  return (props) => {
    useRegionGray();
    const { isMockUser } = useMockUser();
    const { authed, setAuthed, loading } = useAuth({ disableErrorDialog: true });
    const { isWhite } = useWhiteList();
    const { isMaintainingWhite } = useMaintaining();

    if (loading) return <Loading fullScreen />;
    if (!isMaintainingWhite) return <Maintaining />;
    if (!isWhite) return <NotAllowed />;

    return (
      <CommonErrorBoundary>
        <AppProvider setAuthed={setAuthed}>
          <MessengerRegionBar />
          <MessengerResourceGroupBar />
          {!CachedData.isEaChannel() && <OpAnnouncement />}
          {process.env.NODE_ENV === 'development' && <SaveToken />}
          {isMockUser && <MockUser />}
          {!authed && <Auth />}
          {authed && (
            <Switch>
              <Route path="/:regionId/gateway/:id" component={GatewayDetail} />
              <Route path="/:regionId/ai-gateway/:id" component={AiGatewayDetail} />

              <Route>
                <App {...props} />
                {IS_PRE_OR_LOCAL && location.pathname && <PreEnvSelector />}
                {IS_PRE && <MultiVersionSelector />}
              </Route>
            </Switch>
          )}
          {/* {process.env.NODE_ENV === 'development' && <Medusa />} */}
        </AppProvider>
      </CommonErrorBoundary>
    );
  };
};
