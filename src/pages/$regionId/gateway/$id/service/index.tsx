import React, { useState, useEffect } from 'react';
import { columns, searchs } from './Configs';
import services from '~/utils/services/microgw_widget';
import { request } from '~/utils/services';
import { getVersionCompare } from '~/utils';
import HealthCheck from './components/HealthCheck';
import { queryDecode } from '~/utils/queryString';
import AddOrEdit from './components/EditForm';
import { intl, Icon, LinkButton, Button, Dialog, Message, CndTable } from '@ali/cnd';
import ServiceMonitor from './components/ServiceMonitor';
import './index.less';
import { MONITOR_METRIC_GROUP } from '~/components/shared/CloudMonitor';
import { SourceTypes } from '~/constants';
import { includes, pickBy } from 'lodash';
import { requestResourceGroupParams } from '~/useHooks/useResourceGroup';
import { get } from 'lodash';

const Service = (props: any) => {
  const {
    detailData,
    history,
    match: {
      params: { id },
    },
    handleTabChange,
  } = props;
  const { GatewayUniqueId, GatewayVersion, gatewayType } = detailData;
  const { quickStart } = queryDecode();
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sourceList, setSourceList] = useState([]);
  const [healthCheckVisible, setHealthCheckVisible] = useState(false);
  const [currentService, setCurrentService] = useState(null);
  const [showEditForm, setShowEditForm] = useState(false);
  const [changeStatus, setChangeStatsus] = useState(false);
  const [showMonitor, setShowMonitor] = useState(false);
  const [serviceMonitorName, setMonitorName] = useState('');
  const [loopEnable, setLoopEnable] = useState(false);

  useEffect(() => {
    handleSearchSourceList();
    if (quickStart) {
      setTimeout(() => {
        handleCreate();
      }, 1000);
    }
  }, []);
  const handleSearchSourceList = async () => {
    const res = await request({
      productCode: 'APIG',
      action: 'ListSources',
    })({
      params: {
        gatewayId: GatewayUniqueId,
        pageNumber: 1,
        pageSize: 50,
        ...requestResourceGroupParams(),
      },
    });
    setSourceList(res?.items || []);
  };
  const fetchServiceData = async (params) => {
    setLoopEnable(false);
    const filterParams = pickBy(params, (item) => item !== '' && item !== undefined);
    const res = await request({
      action: 'ListServices',
    })({
      params: {
        ...filterParams,
        gatewayId: GatewayUniqueId,
        ...requestResourceGroupParams(),
        pageNumber: filterParams.current,
      },
      customErrorHandle: (err, data, callback) => {
        if (err?.data?.code === 'IllegalRequest') {
          setChangeStatsus(true);
        }
        callback();
      },
    });
    setChangeStatsus(false);
    const loopStatus = ['Checking'];
    const loopFlag = (res?.items || []).some((item) => {
      return loopStatus.includes(item?.healthStatus);
    });
    setLoopEnable(loopFlag);
    return {
      data: res?.items || [],
      total: res?.totalSize || 0,
    };
  };
  // 创建
  const handleCreate = () => {
    setCurrentService(null);
    setShowEditForm(true);
  };
  // 跳转至详情
  const handleOpenInfo = (record) => {
    const serviceId = record.serviceId;
    let subTitle = record.name;
    if (record?.sourceType === 'FC3' && record.qualifier) {
      subTitle = `${record.name}/${record?.qualifier}`;
    }
    history.push(
      gatewayType === 'API'
        ? `/${window.regionId}/gateway/${id}/service/detail?region=${window.regionId}&ServiceId=${serviceId}&subTitle=${subTitle}`
        : `/${window.regionId}/ai-gateway/${id}/service/detail?region=${window.regionId}&ServiceId=${serviceId}&subTitle=${subTitle}`,
    );
  };
  // 来源为空，跳还至创建来源
  const handleServiceSource = () => {
    handleTabChange('source');
  };

  // 列表操作项
  // 编辑
  const handleEdit = async (record) => {
    if (
      record.sourceType === 'AI' ||
      record.sourceType === 'AGENT' ||
      record.sourceType === 'CloudFlow'
    ) {
      let res = await getServiceInfo(record);
      setCurrentService({ ...res });
    } else {
      setCurrentService({ ...record });
    }
    setShowEditForm(true);
  };

  // 健康检查配置
  const handleHealthCheck = (record) => {
    // @ts-ignore
    // window?.CN_TRACKER?.send({
    //   type: 'mse-health-config-openDialog',
    // });
    setCurrentService({ ...record });
    setHealthCheckVisible(true);
  };
  // 删除
  const handleDelete = (record) => {
    if (!record?.serviceId) return;
    Dialog.alert({
      title: intl('@ali/widget-edas-microgw::widget.common.delete'),
      content: intl.html('@ali/widget-edas-microgw::widget.common.delete_confirm', {
        name: record?.name,
      }),
      onOk: async () => {
        const res = await services.deleteService({
          customErrorHandle: (err, data, callback) => {
            callback();
          },
          params: { ServiceId: record.serviceId },
        });
        if (res?.code === 'Ok') {
          Message.success(intl('@ali/widget-edas-microgw::widget.common.delete_success'));
          setRefreshIndex(Date.now());
        }
      },
      footerActions: ['ok', 'cancel'],
      okProps: { children: intl('@ali/widget-edas-microgw::widget.common.ok') },
      cancelProps: { children: intl('@ali/widget-edas-microgw::widget.common.cancel') },
    });
  };

  const getServiceInfo = async (record) => {
    return await services.getServiceInfo({
      params: { ServiceId: record.serviceId },
      customErrorHandle: (err, data, callback) => {
        callback();
      },
    });
  };

  const setHealthCheck = (record = {}) => {
    // 配置健康检查之前 判断网关版本号
    const currentVersion = GatewayVersion;
    const destsVersion = '1.2.1';
    const isCheck = getVersionCompare(currentVersion, destsVersion);
    if (!isCheck) {
      Dialog.alert({
        title: intl('mse.microgw.version.upgrade.title'),
        content: (
          <div>
            {intl.html('mse.microgw.service.list.version.upgrade.desc', {
              version: currentVersion,
            })}
          </div>
        ),
      });
      return;
    }
    // setCurrentValue({ ...record });
    // setShowHealthIndex(Date.now());
  };

  const handleMonitor = (record) => {
    setShowMonitor(false);
    setCurrentService({ ...record });
    let sourceType = record.sourceType;
    setMonitorName(
      `outbound|${sourceType === SourceTypes.CloudFlow ? '7443' : get(record, 'ports[0].port')}||${record.serviceFQDN}`,
    );
    setTimeout(() => {
      setShowMonitor(true);
    }, 0);
  };

  return (
    <div style={{ marginTop: 16 }}>
      <CndTable
        search={searchs(gatewayType) as any}
        refreshIndex={refreshIndex}
        fetchData={fetchServiceData}
        columns={
          columns(handleEdit, handleOpenInfo, handleHealthCheck, handleDelete, handleMonitor, gatewayType) as any
        }
        showRefreshButton
        pagination={{
          pageSizeList: [10, 20, 50, 100],
          hideOnlyOnePage: false,
        }}
        loop={{ enable: loopEnable, time: 10000 }}
        operation={
          <Button
            type="primary"
            disabled={
              changeStatus ||
              includes(
                ['Creating', 'Restarting', 'Upgrading', 'Deleting', 'Starting', 'Stopping'],
                detailData?.status,
              )
            }
            onClick={() => handleCreate()}
          >
            {intl('@ali/widget-edas-microgw::widget.service.create')}
          </Button>
        }
        emptyContent={
          <>
            {changeStatus && (
              <span>
                {intl('mse.microgw.servicelist.notstatus.tip')}
                <LinkButton onClick={() => setRefreshIndex(Date.now())}>
                  {intl('mse.common.refresh')}...
                </LinkButton>
              </span>
            )}

            {!changeStatus && sourceList?.length === 0 && (
              <span>
                {intl('mse.micgrow.servicelist.empty')}
                <LinkButton onClick={handleServiceSource}>
                  {intl('mse.add.source.button')}
                </LinkButton>
              </span>
            )}

            {!changeStatus && sourceList?.length > 0 && (
              <span>{intl('mse.micgrow.servicelist.empty.message')}</span>
            )}
          </>
        }
      />

      {healthCheckVisible && (
        <HealthCheck
          {...props}
          serviceInfo={currentService}
          GatewayUniqueId={GatewayUniqueId}
          setRefreshIndex={setRefreshIndex}
          handleClose={() => {
            setHealthCheckVisible(false);
            setCurrentService(null);
          }}
        />
      )}

      {showEditForm && (
        <AddOrEdit
          {...props}
          value={currentService}
          exists={totalCount}
          gatewayId={GatewayUniqueId}
          gatewayType={gatewayType}
          handleOK={() => {
            setShowEditForm(false);
            setRefreshIndex(Date.now());
          }}
          handleClose={() => {
            setShowEditForm(false);
          }}
          handleTabChange={handleTabChange}
        />
      )}
      {/* AI服务监控使用sls，其他使用云监控 */}
      {/* 服务监控 */}
      {showMonitor && (
        <div className="router-table">
          <div className="top-title">
            <div>{`${intl('@ali/widget-edas-microgw::widget.gateway.action.monitor')} (${
              currentService?.name
              }) `}</div>
            <div>
              <Button
                text
                onClick={() => {
                  setShowMonitor(false);
                  setCurrentService(null);
                }}
              >
                <Icon type="close" />
              </Button>
            </div>
          </div>
          <div className="dialog-content">
            <ServiceMonitor
              gatewayId={GatewayUniqueId}
              serviceId={currentService?.serviceId}
              serviceName={serviceMonitorName}
              metricGroup={MONITOR_METRIC_GROUP.service}
              sourceType={currentService?.sourceType}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Service;
