import React, { useEffect, useState, useRef } from 'react';
import PropTypes from 'prop-types';
import services from '~/utils/services/microgw_widget';
import { request } from '~/utils/services';
import {
  SERVICE_NNAME_PATTERN,
  SERVICE_IP_PATTERN,
  SourceTypes,
  DNS_DOMAIN_PATTERN,
} from '~/constants';
import {
  intl,
  Select,
  SlidePanel,
  Form,
  Input,
  Loading,
  Field,
  Message,
  Icon,
  Balloon,
  Grid,
  Button,
  LinkButton,
  useHistory,
} from '@ali/cnd';
import IconButton from '~/components/shared/IconButton';
import { includes, map, concat, forEach, split, get, isEmpty, filter, endsWith } from 'lodash';
import { formatDate, compareVersion } from '~/utils';
import { queryDecode } from '~/utils/queryString';
import NewTransfer from './NewTransfer';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/utils/cacheData';
import FC from './components/fc';
import SaeService from './components/sae';
import DnsService from '~/components/shared/DnsService';
import { MultiLinesMessage } from '~/components/shared/MultiLinesMessage';
import UpgradeGatewayVersion from '~/components/shared/UpgradeGatewayVersion';
import AiModel from './components/AiModel';
import CloudFlow from './components/CloudFlow';
import AgentService from './components/AgentService';
import './index.less';
import { useAliyunProductStatus } from '~/useHooks/useAliyunProductStatus';
import { showProductNotOpenMessage } from '~/components/shared/ProductNotOpen';
import Cookie from '~/utils/cookie';
import { useSetAuthed, useSetIsUseCloudFlowAuthed } from '~/components/app-provider';
import { checkRoleExistenceCloudflow } from '~/components/auth/useAuth';

const { Option } = Select;
const { Row, Col } = Grid;
const TlsModes = {
  DISABLE: 'DISABLE',
  SIMPLE: 'SIMPLE',
  MUTUAL: 'MUTUAL',
};

const aliyunSite = Cookie.get('aliyun_site') || 'CN';
const isIntl = aliyunSite === 'INTL';
const isSaeDisabled = isIntl
  ? includes(window.ALIYUN_CONSOLE_GLOBAL.saeDisabledRegionsIntl, window.regionId)
  : includes(window.ALIYUN_CONSOLE_GLOBAL.saeDisabledRegions, window.regionId);

const EditForm = (props) => {
  const {
    detailData: { version },
    gatewayId,
    handleOK,
    handleClose,
    value,
    defaultSourceType = '',
    gatewayType = 'API',
  } = props;
  const saeRef = useRef(null);
  const aiModelRef = useRef(null);
  const agentServiceRef = useRef(null);
  const cloudFlowRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);
  const [serviceSource, setServiceSource] = useState([]);
  const [namespaces, setNamespaces] = useState([]);
  const namespacesRef = useRef(new Map());
  const [transfer, setTransfer] = useState(false);
  const [ack2NacosServices, setAck2NacosServices] = useState([]);
  const servicesRef = useRef(new Map());
  // VIP DNS tls设置
  const [currentTlsMode, setCurrentTlsMode] = useState(TlsModes.DISABLE);
  const [currentTlsInfo, setCurrentTlsInfo] = useState<any>({});
  const [certificates, setCertificates] = useState([]);
  //DNS Server
  const dnsServiceRef = useRef(null);

  const [submitLoading, setSubmitLoading] = useState(false);
  const [upgradeVisible, setUpgradeVisible] = useState(false);
  const [isProductOpen, setIsProductOpen] = useState(true);
  const setIsUseCloudFlowAuthed = useSetIsUseCloudFlowAuthed();
  const setAuthed = useSetAuthed();
  const history = useHistory();
  const field = Field.useField();
  const { init, validate, setValues, getValue, setValue } = field;
  const domainQuery = queryDecode();
  const { quickStart } = domainQuery;

  const legacy = get(props, 'detailData.legacy');
  const ogrGatewayType = get(props, 'detailData.gatewayType');
  const currentSource = getValue('SourceType');

  const { enable: isSaeOpen } = useAliyunProductStatus({
    product: 'serverless',
    enableFetch: currentSource === SourceTypes.Sae,
  });

  const SOURCEDATA = [
    {
      value: SourceTypes.K8S,
      label: intl('@ali/widget-edas-microgw::widget.service.root_ack'),
      disabled: false,
    },
    {
      value: SourceTypes.Nacos,
      label: intl('@ali/widget-edas-microgw::widget.service.root_mse'),
      disabled: false,
    },
    {
      value: SourceTypes.FC3,
      label: includes(window.ALIYUN_CONSOLE_GLOBAL.fcDisabledRegions, window.regionId) ? (
        <Balloon closable={false} trigger={intl('apigw.headBtn.publish.FunctionComputeFc')}>
          {intl('apigw.components.EditForm.NotOpenYet')}
        </Balloon>
      ) : (
        intl('apigw.headBtn.publish.FunctionComputeFc')
      ),

      disabled: includes(window.ALIYUN_CONSOLE_GLOBAL.fcDisabledRegions, window.regionId),
    },
    window.regionId !== 'cn-qingdao' && {
      value: SourceTypes.Sae,
      label: isSaeDisabled ? (
        <Balloon closable={false} trigger={intl('mse.microgw.source.sae')}>
          {intl('apigw.create-service.SourceStep.NotOpenYet')}
        </Balloon>
      ) : (
        intl('mse.microgw.source.sae')
      ),
      disabled: isSaeDisabled,
    },
    {
      value: SourceTypes.VIP,
      label: intl('@ali/widget-edas-microgw::widget.service.root_fix'),
      disabled: false,
    },
    {
      value: SourceTypes.DNS,
      label: intl('@ali/widget-edas-microgw::widget.service.root_dns'),
      disabled: false,
    },
    ((legacy && ogrGatewayType === 'API') || ogrGatewayType === 'AI') && {
      value: SourceTypes.AI,
      label: intl('apigw.components.EditForm.AiServices'),
      disabled: false,
    },
    ogrGatewayType === 'AI' && {
      value: SourceTypes.AGENT,
      label: intl('apigw.src.constants.AgentService'),
      disabled: false,
    },
    ogrGatewayType === 'API' &&
      includes(window.ALIYUN_CONSOLE_GLOBAL.cloudFlowRegionListVisible, window.regionId) && {
        value: SourceTypes.CloudFlow,
        label: intl('apigw.components.EditForm.CloudWorkflowCloudflow'),
        disabled: false,
      },
  ].filter(Boolean);

  useEffect(() => {
    setIsProductOpen(isSaeOpen);
  }, [isSaeOpen]);

  useEffect(() => {
    setServiceSource(SOURCEDATA);
    if (quickStart) {
      setValue('SourceType', SourceTypes.Nacos);
    } else {
      setValue('SourceType', ogrGatewayType === 'AI' ? SourceTypes.AI : SourceTypes.K8S);
    }
    value && setEchoServiceInfo();
    value && getServiceTlsPolice();
    defaultSourceType && onServiceTypeChange(defaultSourceType);
  }, []);

  useEffect(() => {
    switch (currentSource) {
      case SourceTypes.K8S:
      case SourceTypes.Nacos:
        getK8sOrNacosService();
        break;
      case SourceTypes.FC3:
        setValues({ fc: [{}] });
        break;
      case SourceTypes.DNS:
      case SourceTypes.VIP:
        getEncryptCertificate();
        break;
      default:
        break;
    }
  }, [currentSource]);

  useEffect(() => {
    if (
      getValue('SourceType') !== SourceTypes.DNS ||
      !getValue('Ips') ||
      getValue('mode') === TlsModes.DISABLE
    )
      return;
    const ips = split(getValue('Ips'), ',');
    if (endsWith(get(ips || [], '[0]'), ':443')) {
      setValue('sni', get(ips || [], '[0]')?.slice(0, -4));
    }
  }, [getValue('SourceType'), getValue('Ips'), getValue('mode')]);

  const getServiceTlsPolice = async () => {
    if (!value?.serviceId) return;
    const res = await request({
      productCode: 'APIG',
      action: 'ListPolicies',
    })({
      params: {
        withAttachments: true,
        attachResourceType: 'GatewayService',
        attachResourceId: value?.serviceId,
        gatewayId: gatewayId,
      },
    });
    if (res?.items && Array.isArray(res?.items)) {
      res?.items.forEach((item) => {
        if (item?.className === 'ServiceTls') {
          setCurrentTlsInfo(item);
          const config = JSON.parse(item.config) || {};
          setCurrentTlsMode(config.mode || TlsModes.DISABLE);
          setValues({ ...config });
        }
      });
    }
  };

  const getK8sOrNacosService = async (refreshFlag?) => {
    if (!gatewayId) return;
    setIsLoading(true);
    const res = await services.pullServiceInfo({
      customErrorHandle: (err, data, callback) => {
        setIsLoading(false);
        callback();
      },
      params: {
        GatewayUniqueId: gatewayId,
        SourceType: currentSource,
        importableOnly: true,
      },
    });
    const _namespaces = [];
    forEach(res, (service) => {
      const { Namespace, Services: services = [], NamespaceShowName } = service;
      _namespaces.push({
        label: Namespace,
        value: Namespace,
        NamespaceShowName,
      });
      const _services = [];
      forEach(services, (item) => {
        const { Name, Namespace, GroupName = '' } = item;
        _services.push({
          label: Name,
          value: `${currentSource}-${Name}-${Namespace}-${GroupName}`,
        });
        servicesRef.current.set(`${currentSource}-${Name}-${Namespace}-${GroupName}`, item);
      });
      namespacesRef.current.set(`${Namespace}`, _services);
    });
    setNamespaces(_namespaces);
    if (!value && _namespaces.length) {
      setValues({ Namespace: _namespaces[0].value });
      onAck2NacosSpaceChange(_namespaces[0].value);
    }
    if (refreshFlag && refreshFlag === 'refresh') {
      Message.success(intl('@ali/widget-edas-microgw::widget.service.namespace_refresh_success'));
    }
    setIsLoading(false);
  };

  const getEncryptCertificate = async () => {
    const res: any = await services.getSSLCertificate({
      params: { pageSize: 100, pageNumber: 1 },
    });
    const certs = res?.items || [];
    const _certificates = [];
    certs.forEach((cert) => {
      const { certName, certIdentifier } = cert;
      _certificates.push({
        ...cert,
        label: certName,
        value: certIdentifier,
      });
    });
    _certificates.push({
      label: intl('@ali/widget-edas-microgw::widget.domain.cert_create'),
      value: 'CREATE',
    });
    setCertificates(_certificates);
  };

  const setEchoServiceInfo = async () => {
    setIsLoading(true);
    await getEncryptCertificate();
    const { sourceType, name, addresses = [] } = value;
    let _serviceSource = [];
    if (
      includes(
        [
          SourceTypes.VIP,
          SourceTypes.DNS,
          SourceTypes.AI,
          SourceTypes.AGENT,
          SourceTypes.CloudFlow,
        ],
        sourceType,
      )
    ) {
      _serviceSource = map(SOURCEDATA, (item) => ({
        ...item,
        disabled: sourceType !== item.value,
      }));
    }
    setServiceSource(_serviceSource);
    setValues({ SourceType: sourceType, Name: name, Ips: addresses.join(',') });
    setIsLoading(false);
  };

  const onServiceTypeChange = async (v) => {
    if (v === SourceTypes.CloudFlow) {
      let res = await checkRoleExistenceCloudflow();
      if (!res) {
        setIsUseCloudFlowAuthed(true);
        setAuthed(false);
        return;
      }
    }
    field.reset();
    setNamespaces([]);
    setValues({
      SourceType: v,
      Namespace: '',
      Name: '',
      Ips: '',
      mode: TlsModes.DISABLE,
      ServiceList: [],
    });
    setCurrentTlsMode(TlsModes.DISABLE);
    setAck2NacosServices([]);
  };

  const renderAck2NacosItem = (item) => {
    const { value: Namespace, NamespaceShowName } = item;
    return (
      <div className="space">
        <div className="space-item">
          <span className="space-item-label">
            {intl('@ali/widget-edas-microgw::widget.service.namespace_name')}
          </span>
          <span>{Namespace}</span>
        </div>
        {currentSource === SourceTypes.Nacos && (
          <div className="space-item">
            <span className="space-item-label">
              {intl('@ali/widget-edas-microgw::widget.service.namespace_alias')}
            </span>
            <span style={{ whiteSpace: 'normal' }}>{NamespaceShowName}</span>
          </div>
        )}
      </div>
    );
  };

  const renderCertificateItem = (item) => {
    const { certIdentifier, certName, afterDate, commonName, value } = item;
    return (
      <div>
        {value === 'CREATE' && (
          <div className="cert-icon-button">
            <span className="common-link" style={{ marginLeft: 5, fontWeight: 500 }}>
              <a
                href={`${CachedData.confLink('feature:yundun:cas')}#/overview/${window.regionId}`}
                target="_blank"
              >
                {intl('@ali/widget-edas-microgw::widget.domain.cert_create')}
              </a>
            </span>
            <IconButton type="external-link-alt" />
            <span style={{ color: '#555' }}>
              ({intl('@ali/widget-edas-microgw::widget.domain.cert_upload')})
            </span>
          </div>
        )}

        {value !== 'CREATE' && (
          <div className="cert-1noF0">
            <div className="certItem-1kwjm">
              <span className="certItem-1kwjm-label">
                {intl('@ali/widget-edas-microgw::widget.domain.cert_name')}
              </span>
              <span>{certName}</span>
            </div>
            <div className="certItem-1kwjm">
              <span className="certItem-1kwjm-label">
                {intl('@ali/widget-edas-microgw::widget.domain.cert_id')}
              </span>
              <span>{certIdentifier}</span>
            </div>
            <div className="certItem-1kwjm">
              <span className="certItem-1kwjm-label">
                {intl('@ali/widget-edas-microgw::widget.domain.cert_bind')}
              </span>
              <span>{commonName}</span>
            </div>
            <div className="certItem-1kwjm">
              <span className="certItem-1kwjm-label">
                {intl('@ali/widget-edas-microgw::widget.domain.cert_expire')}
              </span>
              <span>{formatDate(afterDate)}</span>
            </div>
          </div>
        )}
      </div>
    );
  };

  const onAck2NacosSpaceChange = (namespace) => {
    setTransfer(true);
    setAck2NacosServices([]);
    setValues({ ServiceList: [] });
    let _ack2NacosServices = [];
    if (namespace) {
      _ack2NacosServices = namespacesRef.current.get(`${namespace}`);
    }
    setAck2NacosServices(_ack2NacosServices);
    setTransfer(false);
  };

  const onCertificateChange = (_value) => {
    const _certId = _value === 'CREATE' ? '' : _value;
    setValues({ certId: _certId });
  };

  const validateFieldNames = () => {
    let names = [];
    const tlsNames = ['mode', 'certId', 'caCertContent', 'sni'];
    switch (currentSource) {
      case SourceTypes.DNS:
        names = ['SourceType', 'Name', 'Ips', ...tlsNames];
        break;
      case SourceTypes.VIP:
        names = ['SourceType', 'Name', 'Ips', ...tlsNames];
        break;
      case SourceTypes.K8S:
      case SourceTypes.Nacos:
      case SourceTypes.Sae:
        names = ['SourceType', 'Namespace', 'ServiceList'];
        break;
      case SourceTypes.FC3:
        names = ['SourceType', 'fc'];
        break;
      case SourceTypes.AI:
        names = ['SourceType', 'aiModel'];
        break;
      case SourceTypes.AGENT:
        names = ['SourceType', 'agentService'];
        break;
      case SourceTypes.CloudFlow:
        names = ['SourceType', 'cloudFlow'];
        break;
      default:
        break;
    }
    return names;
  };

  const validateFieldParam = (values) => {
    const { SourceType } = values;
    const commonParam = { GatewayUniqueId: gatewayId, SourceType };
    let customParam: any = { ServiceList: [] };
    switch (SourceType) {
      case SourceTypes.DNS:
      case SourceTypes.VIP:
        {
          const { Name, Ips } = values;
          const serviceList =
            SourceType === SourceTypes.DNS
              ? [
                  {
                    name: Name,
                    dnsServers: dnsServiceRef?.current?.getValue() || [],
                    addresses: Ips.split(','),
                  },
                ]
              : [{ name: Name, addresses: Ips.split(',') }];
          const TlsSetting = validateTlsParam(values);
          customParam = { ServiceList: serviceList, TlsSetting };
        }
        break;
      case SourceTypes.K8S:
      case SourceTypes.Nacos:
        {
          const { Namespace = '', ServiceList } = values;
          const serviceList = map(ServiceList, (combineName) => {
            const service = servicesRef.current.get(`${combineName}`);
            const { Name, GroupName = '' } = service;
            return { Name, Namespace, GroupName };
          });
          customParam = { ServiceList: serviceList };
        }
        break;
      case SourceTypes.Sae:
        {
          const serviceList = saeRef?.current?.getSaeFieldValues();
          customParam = { ServiceList: serviceList };
        }
        break;
      case SourceTypes.FC3:
        {
          const { fc } = values;
          let _serviceList = [];
          fc.forEach((item) => {
            _serviceList.push({
              name: item?.name,
              qualifier: item?.qualifier,
            });
          });
          customParam = {
            ServiceList: _serviceList,
          };
        }
        break;
      case SourceTypes.AI:
        {
          const aiModel = aiModelRef?.current?.getValue() || {};
          const {
            name,
            provider,
            protocols,
            address,
            gatewayId,
            apiKeys,
            enableHealthCheck,
            modelMappings,
            dnsServers,
            workspaceId,
            serviceId,
            serviceName,
            endpointType,
            bedrockServiceConfig,
          } = aiModel;
          let aiServiceConfig: any = {
            provider,
            protocols,
            address,
            apiKeys: map(filter(apiKeys, (item) => item.apiKey) || [], (j) => j.apiKey),
            enableHealthCheck,
            modelMappings: map(
              filter(modelMappings, (item) => {
                return item.origin && item.target;
              }) || [],
              (j) => ({
                origin: j.origin,
                target: j.target,
              }),
            ),
          };
          if (provider === 'pai-eas') {
            aiServiceConfig.paiEASServiceConfig = {
              workspaceId,
              serviceName,
              serviceId,
              endpointType,
            };
          }
          if (provider === 'bedrock') {
            aiServiceConfig.bedrockServiceConfig = bedrockServiceConfig;
          }
          if (isEmpty(value)) {
            customParam = {
              serviceConfigs: [
                {
                  name,
                  aiServiceConfig,
                  dnsServers,
                },
              ],
            };
          } else {
            customParam = {
              aiServiceConfig,
              dnsServers,
            };
          }
        }
        break;
      case SourceTypes.AGENT:
        {
          const agentServiceValues = agentServiceRef?.current?.getValue() || {};
          customParam = {
            ...agentServiceValues,
          };
        }
        break;
      case SourceTypes.CloudFlow:
        {
          const cloudFlowValues = cloudFlowRef?.current?.getValue() || {};
          customParam = {
            ...cloudFlowValues,
          };
        }
        break;
      default:
        break;
    }

    return { ...commonParam, ...customParam };
  };

  const validateTlsParam = (values) => {
    const { mode } = values;
    let TlsSetting: any = { mode };
    if (mode === TlsModes.SIMPLE) {
      const { sni = '' } = values;
      TlsSetting = { mode, sni };
    }
    if (mode === TlsModes.MUTUAL) {
      const { certId, caCertContent = '', sni = '' } = values;
      TlsSetting = { mode, certId, caCertContent, sni };
    }
    return TlsSetting;
  };

  const validateIpsContent = (rule, value, callback) => {
    if (!value) {
      callback();
      return;
    }
    forEach(value.split(','), (entry) => {
      if (entry) {
        if (currentSource === SourceTypes.VIP && !SERVICE_IP_PATTERN.test(entry)) {
          callback(
            intl('@ali/widget-edas-microgw::widget.service.validate_ips') +
              intl('apigw.publish.components.serviceTableProps.TheFormatIsIpPort'),
          );
        }
        if (currentSource === SourceTypes.DNS && !DNS_DOMAIN_PATTERN.test(entry)) {
          callback(
            intl('@ali/widget-edas-microgw::widget.service.validate_ips') +
              intl('apigw.publish.components.serviceTableProps.TheFormatIsDomainName.1'),
          );
        }
      }
    });
    callback();
  };

  const handleSubmit = () => {
    new Promise<void>((resolve, reject) => {
      const names = validateFieldNames();
      validate(names, async (errors, values: any) => {
        if (errors) {
          if (
            includes([SourceTypes.AI, SourceTypes.AGENT, SourceTypes.CloudFlow], values?.SourceType)
          ) {
            return reject(errors);
          }
          for (const err in errors) {
            const msg =
              errors[err]['errors'][0] ||
              intl('@ali/widget-edas-microgw::widget.common.params_config_err');
            Message.error(msg);
            return reject(errors);
          }
        }
        if (values?.SourceType === SourceTypes.FC3) {
          const { fc } = values;
          const fcValidate = fc.every((item) => {
            return item.name && item.qualifier;
          });
          if (!fcValidate) {
            Message.error(intl('apigw.components.EditForm.CompleteTheBackendService'));
            return reject();
          }
        }
        if (values?.SourceType === SourceTypes.Sae) {
          const saeFieldValidate = saeRef?.current?.fieldValidate();
          if (!saeFieldValidate) {
            return reject();
          }
        }
        setSubmitLoading(true);
        const params = validateFieldParam(values);
        const { TlsSetting, ...serviceParams } = params;
        if (value) {
          if (!value.serviceId) {
            setSubmitLoading(false);
            return reject();
          }
          let res: any = {};
          if (
            includes([SourceTypes.AI, SourceTypes.AGENT, SourceTypes.CloudFlow], values?.SourceType)
          ) {
            res = await services.updateService({
              customErrorHandle: (err, _, callback) => {
                setSubmitLoading(false);
                reject();
                callback();
              },
              params: { ...serviceParams, Id: value.serviceId },
            });
          } else {
            // 仅固定地址与DNS可编辑
            const { Ips } = values;
            const TlsSetting = validateTlsParam(values);
            const data = {
              GatewayUniqueId: gatewayId,
              Id: value.serviceId,
              IpList: Ips.split(','),
              SourceType: value.sourceType,
              dnsServers: dnsServiceRef?.current?.getValue() || [],
            };
            res = await services.updateService({
              customErrorHandle: (err, _, callback) => {
                setSubmitLoading(false);
                reject();
                callback();
              },
              params: { ...data },
            });
            let policyAction = currentTlsInfo?.policyId
              ? 'UpdateAndAttachPolicy'
              : 'CreateAndAttachPolicy';
            await request({
              productCode: 'APIG',
              action: policyAction,
            })({
              params: {
                policyId: currentTlsInfo?.policyId || '',
              },
              content: {
                attachResourceType: 'GatewayService',
                gatewayId: gatewayId,
                config: JSON.stringify({
                  ...TlsSetting,
                  enable: TlsSetting.mode === TlsModes.DISABLE ? false : true,
                }),
                className: 'ServiceTls',
                attachResourceIds: [value.serviceId],
              },
            });
          }
          setSubmitLoading(false);
          if (res?.responseSuccess) {
            resolve();
            handleOK();
            Message.success(intl('@ali/widget-edas-microgw::widget.common.update_success'));
          } else {
            reject();
          }
        } else {
          const createRes = await services.createService({
            customErrorHandle: (err, data, callback) => {
              setSubmitLoading(false);
              reject();
              callback();
            },
            params: {
              ...serviceParams,
              resourceGroupId: get(props.detailData, 'resourceGroupId'),
            },
          });
          if (
            createRes?.serviceIds &&
            Array.isArray(createRes?.serviceIds) &&
            includes(['VIP', 'DNS'], serviceParams?.SourceType)
          ) {
            const serviceId = createRes?.serviceIds[0];
            if (serviceId) {
              await request({
                productCode: 'APIG',
                action: 'CreateAndAttachPolicy',
              })({
                content: {
                  attachResourceType: 'GatewayService',
                  gatewayId: gatewayId,
                  config: JSON.stringify({
                    ...TlsSetting,
                    enable: TlsSetting.mode === TlsModes.DISABLE ? false : true,
                  }),
                  className: 'ServiceTls',
                  attachResourceIds: [serviceId],
                },
              });
            }
          }
          setSubmitLoading(false);
          if (createRes) {
            resolve();
            handleOK();
            Message.success(intl('@ali/widget-edas-microgw::widget.common.create_success'));
          } else {
            reject();
          }
        }
      });
    });
  };

  const oktextDisable = () => {
    if (includes([SourceTypes.FC3], currentSource)) {
      if (compareVersion(version, '2.0.5') >= 0) {
        return false;
      } else {
        return true;
      }
    }
    if (includes([SourceTypes.CloudFlow], currentSource)) {
      if (compareVersion(version, '2.1.8') >= 0) {
        return false;
      } else {
        return true;
      }
    }
    if (includes([SourceTypes.Sae], currentSource)) {
      if (compareVersion(version, '2.0.8') >= 0) {
        return false;
      } else {
        return true;
      }
    }
    return false;
  };

  return (
    <SlidePanel
      title={
        value
          ? intl('@ali/widget-edas-microgw::widget.service.edit')
          : intl('@ali/widget-edas-microgw::widget.service.create')
      }
      width={880}
      onOk={handleSubmit}
      onClose={handleClose}
      onCancel={handleClose}
      isShowing={true}
      okText={intl('mse.common.ok')}
      cancelText={intl('mse.common.cancel')}
      okProps={{
        disabled: oktextDisable(),
      }}
      isProcessing={submitLoading}
    >
      <Form field={field} labelAlign="left">
        <Row>
          <Col span={16}>
            <Form.Item label={intl('@ali/widget-edas-microgw::widget.service.root')} required>
              <Select
                {...init('SourceType', {
                  initValue:
                    gatewayType == 'AI'
                      ? SourceTypes.AI
                      : defaultSourceType
                        ? defaultSourceType
                        : quickStart
                          ? SourceTypes.Nacos
                          : SourceTypes.K8S,
                  rules: [
                    {
                      required: true,
                      message: intl('@ali/widget-edas-microgw::widget.service.root_require'),
                    },
                  ],

                  props: {
                    onChange: onServiceTypeChange,
                  },
                })}
                dataSource={serviceSource}
                style={{ width: '100%' }}
              />

              {((includes([SourceTypes.K8S], currentSource) && !ack2NacosServices.length) ||
                (includes([SourceTypes.Nacos], currentSource) && !ack2NacosServices.length)) && (
                <div
                  style={{
                    marginBottom: 6,
                    color: '#808080',
                    fontWeight: 'normal',
                  }}
                >
                  {intl('mse.microgw.service.create.source.tip.first')}
                  <LinkButton
                    onClick={() => {
                      props.handleTabChange('source');
                    }}
                  >
                    {intl('mse.register.namespace.from')}
                  </LinkButton>
                  {intl('mse.microgw.service.create.source.tip.third')}
                </div>
              )}

              {!isProductOpen &&
                currentSource === SourceTypes.Sae &&
                showProductNotOpenMessage(SourceTypes.Sae)}
              {includes([SourceTypes.FC3], currentSource) && (
                <MultiLinesMessage
                  lines={[
                    {
                      label: (
                        <span className="f-w-400">
                          {intl('apigw.components.EditForm.FunctionComputeIsSupportedCurrently')}
                        </span>
                      ),
                    },
                  ]}
                />
              )}

              {oktextDisable() && includes([SourceTypes.FC3], currentSource) && (
                <div
                  style={{
                    color: '#e00000',
                    fontWeight: 'normal',
                  }}
                >
                  {intl('apigw.components.EditForm.FcServiceIntegrationHasBeen')}
                  <LinkButton
                    onClick={() => {
                      sessionStorage.setItem('enableUpgradeVersionGateway', 'true');
                      history.push(
                        `/${window.regionId}/gateway/${gatewayId}/detail?region=${window.regionId}`,
                      );
                    }}
                  >
                    {intl('apigw.components.EditForm.UpgradeTheGatewayVersion')}
                  </LinkButton>
                </div>
              )}

              {oktextDisable() && includes([SourceTypes.CloudFlow], currentSource) && (
                <div
                  style={{
                    color: '#e00000',
                    fontWeight: 'normal',
                  }}
                >
                  {intl('apigw.components.EditForm.CloudFlowServiceIntegrationUpgradeTip')}
                  <LinkButton
                    onClick={() => {
                      sessionStorage.setItem('enableUpgradeVersionGateway', 'true');
                      history.push(
                        `/${window.regionId}/gateway/${gatewayId}/detail?region=${window.regionId}`,
                      );
                    }}
                  >
                    {intl('apigw.components.EditForm.UpgradeTheGatewayVersion')}
                  </LinkButton>
                </div>
              )}

              {includes([SourceTypes.Sae], currentSource) && (
                <MultiLinesMessage
                  lines={[
                    {
                      label: (
                        <>
                          {intl('apigw.components.EditForm.SaeApplicationsNeedToBe')}
                          <ExternalLink
                            url={CachedData.confLink('feature:serverless:kubernetes:help')}
                            label={intl('apigw.components.EditForm.KSServiceRegistrationDiscovery')}
                          />
                        </>
                      ),
                    },
                  ]}
                />
              )}

              {oktextDisable() && includes([SourceTypes.Sae], currentSource) && (
                <div
                  style={{
                    marginTop: 4,
                    color: '#e00000',
                    fontWeight: 'normal',
                  }}
                >
                  {intl('apigw.components.EditForm.TheCurrentInstanceVersionIs')}

                  <LinkButton
                    onClick={() => {
                      setUpgradeVisible(true);
                    }}
                    className="mr-2 ml-2"
                  >
                    {intl('apigw.components.EditForm.Upgrade')}
                  </LinkButton>
                  {intl('apigw.components.EditForm.ToOrLater')}
                </div>
              )}
            </Form.Item>
          </Col>
        </Row>
        <Loading visible={isLoading} style={{ width: '100%' }}>
          {/* K8S Nacos */}
          {includes([SourceTypes.K8S, SourceTypes.Nacos], currentSource) && (
            <>
              <Row>
                <Col span={16}>
                  <Form.Item
                    label={
                      <>
                        <span>{intl('@ali/widget-edas-microgw::widget.service.namespace')}</span>
                        &nbsp;&nbsp;
                        {currentSource === SourceTypes.K8S && (
                          <Balloon
                            trigger={<Icon type="exclamation-circle" size="xs" />}
                            align="r"
                            closable={false}
                          >
                            <div
                              style={{
                                color: '#808080',
                                fontWeight: 'normal',
                              }}
                            >
                              {intl('@ali/widget-edas-microgw::widget.service.namespace.message')}
                            </div>
                          </Balloon>
                        )}
                      </>
                    }
                    required
                  >
                    <Select
                      {...init('Namespace', {
                        rules: [
                          {
                            required: true,
                            message: intl(
                              '@ali/widget-edas-microgw::widget.service.namespace_require',
                            ),
                          },
                        ],

                        props: {
                          onChange: onAck2NacosSpaceChange,
                        },
                      })}
                      hasClear
                      showSearch
                      dataSource={namespaces}
                      style={{ width: '100%' }}
                      popupClassName="pop-namespace"
                      itemRender={renderAck2NacosItem}
                      placeholder={intl(
                        '@ali/widget-edas-microgw::widget.service.namespace_placeholder',
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col span={4} style={{ position: 'relative' }}>
                  <Button
                    style={{ position: 'absolute', top: '32px' }}
                    onClick={() => {
                      if (currentSource === SourceTypes.K8S) {
                        getK8sOrNacosService('refresh');
                      }
                      if (currentSource === SourceTypes.Nacos) {
                        getK8sOrNacosService('refresh');
                      }
                    }}
                  >
                    <Icon type="refresh" />
                  </Button>
                </Col>
              </Row>
              <Form.Item label={intl('@ali/widget-edas-microgw::widget.service.list')} required>
                <Loading visible={transfer} className="svc-loading">
                  {currentSource === SourceTypes.Nacos && (
                    <div
                      style={{
                        marginBottom: 6,
                        color: '#808080',
                        fontWeight: 'normal',
                      }}
                    >
                      {intl('@ali/widget-edas-microgw::widget.service.list.message')}
                    </div>
                  )}

                  <NewTransfer
                    {...init('ServiceList', {
                      rules: [
                        {
                          required: true,
                          message: intl('@ali/widget-edas-microgw::widget.service.list_require'),
                        },
                      ],
                    })}
                    handleChecked={(val) => {
                      console.log('----v', val);
                      setValues({
                        ServiceList: val,
                      });
                    }}
                    dataSource={ack2NacosServices}
                    titles={[
                      intl('@ali/widget-edas-microgw::widget.service.list_total'),
                      intl('@ali/widget-edas-microgw::widget.service.list_part'),
                    ]}
                  />
                </Loading>
              </Form.Item>
            </>
          )}
          {/* FC */}
          {includes([SourceTypes.FC3], currentSource) && (
            <Form.Item
              label={intl('apigw.headBtn.publish.BackendServices')}
              required
              validator={(rules, value, callback) => {
                if (value?.length === 0) {
                  callback(intl('mse.microgw.create.service.placeholder'));
                } else if (value?.length > 0) {
                  let flag = true;
                  value?.forEach((item) => {
                    if (!item?.name || !item?.qualifier) {
                      flag = false;
                    }
                  });
                  if (flag) {
                    callback('');
                  } else {
                    callback(intl('mse.microgw.create.service.placeholder'));
                  }
                } else {
                  callback('');
                }
              }}
            >
              <FC name="fc" gatewayId={gatewayId} currentService={value} />
            </Form.Item>
          )}
          {/* Sae */}
          {includes([SourceTypes.Sae], currentSource) && (
            <SaeService
              setIsProductOpen={setIsProductOpen}
              name="sae"
              ref={saeRef}
              gatewayId={gatewayId}
              currentService={value}
            />
          )}
          {/* 固定地址 DNS */}
          {includes([SourceTypes.VIP, SourceTypes.DNS], currentSource) && (
            <>
              <Form.Item
                label={intl('@ali/widget-edas-microgw::widget.service.name')}
                required
                help={intl('apigw.components.EditForm.TheNameIsUniqueAnd')}
              >
                <Input
                  {...init('Name', {
                    rules: [
                      {
                        required: true,
                        message: intl('@ali/widget-edas-microgw::widget.service.name_require'),
                      },
                      {
                        pattern: SERVICE_NNAME_PATTERN,
                        message: intl('apigw.components.EditForm.TheNameIsUniqueAnd'),
                      },
                    ],
                  })}
                  disabled={value ? true : false}
                  placeholder={intl('apigw.components.EditForm.EnterAServiceName')}
                  maxLength={64}
                  minLength={1}
                  showLimitHint
                  style={{ width: '100%' }}
                />
              </Form.Item>
              <Form.Item label={intl('@ali/widget-edas-microgw::widget.service.ips')} required>
                <Input.TextArea
                  {...init('Ips', {
                    rules: [
                      {
                        required: true,
                        message: intl('@ali/widget-edas-microgw::widget.service.ips_require'),
                      },
                      {
                        validator: validateIpsContent,
                      },
                    ],
                  })}
                  placeholder={
                    (currentSource === SourceTypes.DNS &&
                      intl(
                        'apigw.create-service.ServiceStep.CreateVipService.TheFormatIsDnsDomain',
                      )) ||
                    (currentSource === SourceTypes.VIP &&
                      intl('apigw.components.EditForm.TheFormatIsIpPort'))
                  }
                  autoHeight={{ minRows: 5, maxRows: 10 }}
                  style={{ width: '100%' }}
                />

                <div
                  style={{
                    color: '#888888',
                    marginBottom: 8,
                    marginTop: 4,
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <Icon type="warning" style={{ color: '#FFD813', marginRight: '4px' }} size="xs" />
                  <span>{intl('apigw.components.EditForm.IfYouSpecifyAnInternet')}</span>
                  <ExternalLink
                    url={CachedData.confLink('feature:nat', {
                      regionId: window.regionId,
                    })}
                    label={intl('apigw.components.EditForm.NatGateway')}
                  />
                </div>
              </Form.Item>
            </>
          )}
          {/* 固定地址、DNS tls配置 */}
          {includes([SourceTypes.DNS, SourceTypes.VIP], currentSource) && (
            <>
              <Form.Item label={intl('@ali/widget-edas-microgw::widget.service.ssl.tls')} required>
                <Select
                  {...init('mode', {
                    initValue: TlsModes.DISABLE,
                    rules: [
                      {
                        required: true,
                        message: intl('@ali/widget-edas-microgw::widget.service.ssl.tls_validate'),
                      },
                    ],

                    props: {
                      onChange: (v) => setCurrentTlsMode(v),
                    },
                  })}
                  style={{ width: '100%' }}
                >
                  <Option value="DISABLE">
                    {intl('@ali/widget-edas-microgw::widget.service.ssl.tls_disable')}
                  </Option>
                  <Option value="SIMPLE">
                    {intl('@ali/widget-edas-microgw::widget.service.ssl.tls_simple')}
                  </Option>
                  <Option value="MUTUAL">
                    {intl('@ali/widget-edas-microgw::widget.service.ssl.tls_mutual')}
                  </Option>
                </Select>
              </Form.Item>
              {currentTlsMode === TlsModes.MUTUAL && (
                <>
                  <Form.Item
                    label={intl('@ali/widget-edas-microgw::widget.service.ssl.cert')}
                    required
                    hidden={!(currentTlsMode === TlsModes.MUTUAL)}
                  >
                    <Select
                      {...init('certId', {
                        rules: [
                          {
                            required: true,
                            message: intl(
                              '@ali/widget-edas-microgw::widget.service.ssl.cert_validate',
                            ),
                          },
                        ],

                        props: {
                          onChange: onCertificateChange,
                        },
                      })}
                      showSearch
                      style={{ width: '100%' }}
                      placeholder={intl(
                        '@ali/widget-edas-microgw::widget.service.ssl.cert_placeholder',
                      )}
                      dataSource={certificates}
                      popupClassName="pop-menu-cert"
                      itemRender={renderCertificateItem}
                    />
                  </Form.Item>
                  <Form.Item label={intl('@ali/widget-edas-microgw::widget.service.ssl.key')}>
                    <Input.TextArea {...init('caCertContent')} style={{ width: '100%' }} rows={4} />
                  </Form.Item>
                </>
              )}

              {includes([TlsModes.MUTUAL, TlsModes.SIMPLE], currentTlsMode) && (
                <Form.Item label={intl('@ali/widget-edas-microgw::widget.service.ssl.sni')}>
                  <div
                    style={{
                      marginBottom: 6,
                      color: '#808080',
                      fontWeight: 'normal',
                    }}
                  >
                    {intl('mse.gateway.service.sni_tip')}
                  </div>
                  <Input {...init('sni')} />
                </Form.Item>
              )}
            </>
          )}
          {includes([SourceTypes.DNS, SourceTypes.VIP], currentSource) &&
            currentTlsMode !== TlsModes.DISABLE && (
              <Message type="warning">
                <div>
                  <div className="mb-8">{intl('apigw.gateway.service.port.validate.confirm')}</div>
                  <ul className="leading-5">
                    <li>{intl('apigw.gateway.service.dns.port.validate.https')}</li>
                    <li>{intl('apigw.gateway.service.dns.port.validate.ftps')}</li>
                    <li>{intl('apigw.gateway.service.dns.port.validate.smtp')}</li>
                  </ul>
                </div>
              </Message>
            )}
          {/* DNS服务器 */}
          {currentSource === SourceTypes.DNS && (
            <DnsService
              ref={dnsServiceRef}
              value={value}
              columns={[
                {
                  dataIndex: 'value',
                  cell: ({ value, onChange }) => (
                    <Input
                      style={{ width: '100%' }}
                      onChange={(e) => {
                        onChange(e);
                      }}
                      value={value}
                      trim
                    />
                  ),
                  colSpan: 24,
                },
              ]}
            />
          )}
          {includes([SourceTypes.AI], currentSource) && (
            <AiModel
              {...init('aiModel', {
                // @ts-ignore
                autoValidate: false,
                initValue: {},
                rules: [
                  {
                    validator: async (rule, value: any, callback) => {
                      const errors = await aiModelRef?.current?.validate();
                      if (errors) {
                        callback(errors);
                      } else {
                        callback();
                      }
                    },
                  },
                ],
              } as any)}
              ref={aiModelRef}
              type={isEmpty(value) ? 'create' : 'edit'}
              curData={isEmpty(value) ? {} : value}
              gatewayId={gatewayId}
            />
          )}
          {includes([SourceTypes.AGENT], currentSource) && (
            <AgentService
              {...init('agentService', {
                // @ts-ignore
                autoValidate: false,
                initValue: {},
                rules: [
                  {
                    validator: async (rule, value: any, callback) => {
                      const errors = await agentServiceRef?.current?.validate();
                      if (errors) {
                        callback(errors);
                      } else {
                        callback();
                      }
                    },
                  },
                ],
              } as any)}
              ref={agentServiceRef}
              type={isEmpty(value) ? 'create' : 'edit'}
              curData={isEmpty(value) ? {} : value}
              gatewayId={gatewayId}
              currentSource={SourceTypes.AGENT}
            />
          )}
          {includes([SourceTypes.CloudFlow], currentSource) && (
            <CloudFlow
              {...init('cloudFlow', {
                // @ts-ignore
                autoValidate: false,
                rules: [
                  {
                    validator: async (rule, value: any, callback) => {
                      const errors = await cloudFlowRef?.current?.validate();
                      if (errors) {
                        callback(errors);
                      } else {
                        callback();
                      }
                    },
                  },
                ],
              } as any)}
              ref={cloudFlowRef}
              type={isEmpty(value) ? 'create' : 'edit'}
              curData={isEmpty(value) ? {} : value}
              gatewayId={gatewayId}
            />
          )}
        </Loading>
      </Form>
      {upgradeVisible && (
        <UpgradeGatewayVersion
          currentInstance={props.detailData}
          handleOk={() => {
            setUpgradeVisible(false);
            Message.success({
              content: intl('apigw.components.EditForm.TheInstanceUpgradeTakesAbout'),
              duration: 6000,
            });
            handleClose();
          }}
          handleClose={() => {
            setUpgradeVisible(false);
          }}
          fromPage="gatewayList"
        />
      )}
    </SlidePanel>
  );
};

EditForm.propTypes = {
  // @ts-ignore
  value: PropTypes.shape(),
  handleOK: PropTypes.func,
  handleClose: PropTypes.func,
};

export default EditForm;
