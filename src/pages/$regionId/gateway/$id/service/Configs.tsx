import React from 'react';
import Actions, { LinkButton } from '@alicloud/console-components-actions';
import EndpointsShow from './components/EndpointsShow';
import { includes } from 'lodash';
import { formatDate } from '~/utils';
import { intl, Instance, Copy, Button, Balloon, Dialog } from '@ali/cnd';
import CustomStatus from '~/components/shared/CustomStatus';
import { SourceTypes, NA, SourceTypeZh } from '~/constants';
import CachedData from '~/utils/cacheData';

export const columns = (
  handleEdit,
  handleOpenInfo,
  handleHealthCheck,
  handleDelete,
  handleMonitor,
  gatewayType,
) => {
  const showMoreIps = (ips) => {
    Dialog.show({
      title: intl('@ali/widget-edas-microgw::widget.service.ips'),
      content: ips.map((ip) => {
        return (
          <div style={{ marginBottom: 4 }}>
            <Copy text={ip}>{ip}</Copy>
          </div>
        );
      }),
      style: { width: 480 },
      footerActions: ['cancel'],
      cancelProps: { children: intl('mse.common.cancel') },
    });
  };
  return [
    {
      key: 'name',
      title: intl('@ali/widget-edas-microgw::widget.service.name'),
      dataIndex: 'name',
      lock: 'left',
      width: 220,
      cell: (value, index, record) => (
        <Instance
          link={{
            value:
              record.sourceType === SourceTypes.FC3 && record.qualifier
                ? `${record.name} / ${record.qualifier}`
                : record.name,
            onClick: () => {
              handleOpenInfo(record);
            },
          }}
          truncateProps={{
            type: 'width',
            threshold: 180,
            tooltipMaxWidth: 400,
            popupStyle: {
              wordBreak: 'break-all',
            },
          }}
        />
      ),
    },
    {
      key: 'Monitor',
      title: intl('@ali/widget-edas-microgw::widget.gateway.action.monitor'),
      dataIndex: 'Monitor',
      width: 100,
      cell: (value, index, record) => {
        return (
          <div>
            <Balloon.Tooltip
              trigger={
                <div
                  style={{ cursor: 'pointer', marginRight: 10 }}
                  onClick={() => {
                    handleMonitor(record);
                  }}
                >
                  <img src="https://img.alicdn.com/imgextra/i4/O1CN01gfTvqj1neivKbKg1I_!!6000000005115-55-tps-16-16.svg" />
                </div>
              }
            >
              {intl('mse.register.monitor')}
            </Balloon.Tooltip>
          </div>
        );
      },
    },
    {
      key: 'healthStatus',
      title: intl('@ali/widget-edas-microgw::widget.service.health'),
      dataIndex: 'healthStatus',
      width: 160,
      cell: (value, index, record) => {
        const { healthStatus, unhealthyEndpoints = [] } = record;
        const healthCheck = record?.healthCheck?.enable;
        let healthType = '';
        let healthText = '';
        switch (healthStatus) {
          // 健康
          case 'Health':
          case 'Healthy':
            healthType = 'success';
            healthText = intl('@ali/widget-edas-microgw::widget.service.health.health');
            break;
          case 'Unhealthy':
            // 异常
            healthType = 'error';
            healthText = intl('@ali/widget-edas-microgw::widget.service.health.unhealthy');
            break;
          case 'Unknown':
            // 未知
            healthType = 'warning';
            healthText = intl('@ali/widget-edas-microgw::widget.service.health.unknown');
            break;
          case 'Checking':
            // 检查中
            healthType = 'loading';
            healthText = intl('mse.common.checking');
            break;
          case 'None':
            healthType = 'stop';
            healthText = intl('apigw.id.service.Configs.NotSupported');
        }
        return (
          <div style={{ display: 'flex', minWidth: 100 }}>
            <div>
              {healthCheck && (
                <>
                  {value === 'Unhealthy' && (
                    <Balloon
                      trigger={
                        <div>
                          <CustomStatus type={healthType} statuText={healthText} size="small" />
                        </div>
                      }
                      triggerType="hover"
                      align="r"
                    >
                      <div>
                        <h3>{intl('mse.microgw.service.health.unhealthy_tip_title')}</h3>
                        <p>
                          {intl.html('mse.microgw.service.health.unhealthy_tip_message', {
                            total: unhealthyEndpoints.length || NA,
                          })}
                        </p>
                        <div style={{ marginBottom: 10 }}>
                          <EndpointsShow endpoints={unhealthyEndpoints} />
                        </div>
                      </div>
                      <div>
                        <Button
                          type="primary"
                          onClick={() => {
                            window.open(
                              CachedData.confLink('feature:service:error:check', {
                                product:
                                  gatewayType === 'AI' ? 'ai-gateway' : 'cloud-native-api-gateway',
                              }),
                              '_blank',
                            );
                          }}
                        >
                          {intl('@ali/widget-edas-microgw::widget.service.health_check_link')}
                        </Button>
                      </div>
                    </Balloon>
                  )}

                  {value !== 'Unhealthy' && (
                    <CustomStatus type={healthType} statuText={healthText} size="small" />
                  )}
                </>
              )}

              {!healthCheck && (
                <span>{intl('@ali/widget-edas-microgw::widget.service.health_check_close')}</span>
              )}
            </div>
          </div>
        );
      },
    },
    {
      key: 'addresses',
      title: intl('@ali/widget-edas-microgw::widget.service.ips'),
      dataIndex: 'addresses',
      width: 220,
      cell: (value, index, record) => {
        if (
          includes(['AI'], record?.sourceType) &&
          record?.aiServiceConfig?.provider === 'pai-eas'
        ) {
          return NA;
        }
        if (value && value.length) {
          const ips = [];
          for (let i = 0; i < value.length; i++) {
            if (i >= 2) break;
            const ip = value[i];
            ips.push(
              <div>
                <Copy text={ip}>{ip}</Copy>
              </div>,
            );
          }
          return (
            <div>
              <div>{ips}</div>
              {value?.length > 2 && (
                <div>
                  <LinkButton onClick={() => showMoreIps(value)}>
                    {intl('ahas_sentinel.systemGuard.viewAll')}
                  </LinkButton>
                </div>
              )}
            </div>
          );
        }
        return NA;
      },
    },
    {
      key: 'ports',
      title: intl('@ali/widget-edas-microgw::widget.route.port'),
      dataIndex: 'ports',
      width: 100,
      cell: (value, index, record) => {
        if (includes(['VIP', 'DNS', 'FC3', SourceTypes.CloudFlow], record?.sourceType)) {
          return NA;
        }
        if (
          includes(['AI'], record?.sourceType) &&
          record?.aiServiceConfig?.provider === 'pai-eas'
        ) {
          return NA;
        }
        if (value && value.length) {
          const ports = [];
          for (let i = 0; i < value.length; i++) {
            if (i >= 2) break;
            const port = value[i]?.port;
            ports.push(port);
          }
          if (ports.length > 2) {
            return (
              <div>
                <span>{`${ports.join(',')},`}</span>
                <Balloon
                  trigger={
                    <Button type="primary" text style={{ marginLeft: 6 }}>
                      ...
                    </Button>
                  }
                  align="r"
                >
                  {`${value.join(',')}`}
                </Balloon>
              </div>
            );
          } else {
            return <span>{`${ports.join(',')}`}</span>;
          }
        }
        return <span>-</span>;
      },
    },
    {
      key: 'sourceType',
      title: intl('@ali/widget-edas-microgw::widget.service.root'),
      dataIndex: 'sourceType',
      width: 160,
      cell: (value) => <div>{SourceTypeZh[value]}</div>,
    },
    {
      key: 'namespace',
      title: intl('@ali/widget-edas-microgw::widget.common.namespace'),
      dataIndex: 'namespace',
      width: 200,
      cell: (value) => (
        <div>
          <span>{value || NA}</span>
        </div>
      ),
    },
    {
      key: 'createTimestamp',
      title: intl('mse.common.create.time'),
      dataIndex: 'createTimestamp',
      width: 200,
      cell: (value) => formatDate(value) || NA,
    },
    {
      key: 'operations',
      title: intl('@ali/widget-edas-microgw::widget.common.operating'),
      lock: 'right',
      width: 220,
      cell: (value, index, record) => {
        return (
          // @ts-ignore
          <Actions threshold={2}>
            {/* 服务来源为VIP(固定地址)、DNS时可编辑 */}
            <LinkButton
              disabled={
                !includes(
                  [SourceTypes.VIP, SourceTypes.DNS, SourceTypes.AI, SourceTypes.AGENT],
                  record.sourceType,
                )
              }
              onClick={() => handleEdit(record)}
              visible={!includes([SourceTypes.FC3, SourceTypes.CloudFlow], record.sourceType)}
            >
              {intl('@ali/widget-edas-microgw::widget.common.edit')}
            </LinkButton>
            <LinkButton
              onClick={() => {
                handleHealthCheck(record);
              }}
              visible={!includes([SourceTypes.FC3, SourceTypes.CloudFlow], record.sourceType)}
            >
              {intl('mse.microgw.service.healthcheck')}
            </LinkButton>
            <LinkButton
              key="detail"
              onClick={() => handleOpenInfo(record)}
              visible={
                !includes(
                  [SourceTypes.FC3, SourceTypes.AI, SourceTypes.CloudFlow],
                  record.sourceType,
                )
              }
            >
              {intl('@ali/widget-edas-microgw::widget.service.policy')}
            </LinkButton>
            <LinkButton onClick={() => handleDelete(record)}>
              {intl('mse.common.delete')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];
};

export const searchs = (gatewayType) => {
  let dataSource = [
    { value: '', label: intl('@ali/widget-edas-microgw::widget.common.all') },
    {
      value: SourceTypes.K8S,
      label: intl('@ali/widget-edas-microgw::widget.service.root_ack'),
    },
    {
      value: SourceTypes.Nacos,
      label: intl('@ali/widget-edas-microgw::widget.service.root_mse'),
    },
    {
      value: SourceTypes.FC3,
      label: intl('@ali/widget-edas-microgw::widget.service.root_fc'),
    },
    {
      value: SourceTypes.Sae,
      label: intl('mse.microgw.source.sae'),
    },
    {
      value: SourceTypes.VIP,
      label: intl('@ali/widget-edas-microgw::widget.service.root_fix'),
    },
    {
      value: SourceTypes.DNS,
      label: intl('@ali/widget-edas-microgw::widget.service.root_dns'),
    },
    {
      value: SourceTypes.AI,
      label: 'AI',
    },
    gatewayType === 'AI' && {
      value: SourceTypes.AGENT,
      label: intl('apigw.src.constants.AgentService'),
    },
    gatewayType !== 'AI' &&
      includes(window.ALIYUN_CONSOLE_GLOBAL.cloudFlowRegionListVisible, window.regionId) && {
        value: SourceTypes.CloudFlow,
        label: intl('apigw.components.EditForm.CloudWorkflowCloudflow'),
      },
  ].filter(Boolean);

  return {
    defaultDataIndex: 'name',
    defaultSelectedDataIndex: 'name',
    options: [
      {
        label: intl('@ali/widget-edas-microgw::widget.service.name'),
        dataIndex: 'name',
        templateProps: {
          placeholder: intl('mse.microgw.service.list.search.placeholder'),
        },
        template: 'input',
      },
      {
        label: intl('@ali/widget-edas-microgw::widget.service.root'),
        dataIndex: 'sourceType',
        templateProps: {
          dataSource: dataSource,
        },
        template: 'select',
      },
    ],
  };
};
