import React, { useEffect, useState, useMemo } from 'react';
import AppLayout from '~/containers/AppLayout';
import { useParams } from '@ali/xconsole/hooks';
import { intl, Tab, Truncate, useHistory, Button, Icon } from '@ali/cnd';
import services from '~/utils/services';
import { queryDecode } from '~/utils/queryString';
import { get, cloneDeep, find, includes, isEmpty, first } from 'lodash';
import Detail from '~/components/api-manage/apiAi/detail';
import PoliciesPlugins from '~/components/api-manage/apiAi/policiesPlugins';
import UserGuide from '~/components/api-manage/apiAi/userGuide';
import Statistics from '~/components/api-manage/apiAi/statistics';
import Logs from '~/components/api-manage/apiAi/logs';
import CreateOrUpdateApiSidePanel from '~/components/api-manage/createApi/create-actions/CreateAiApiSidePanel';
import { API_CREATE_TYPE } from '~/track/api';
import ExtendBalloon from '~/components/shared/ExtendBalloon';
import DeleteApiAction from '~/components/api-manage/apiList/DeleteApiAction';
import { API_PUBLISH_STATUS } from '~/constants/apiManage';
import useApiRetry from '~/components/api-manage/apiList/useApiRetry';
import { AIModelDebug } from '~/components/api-manage/apiAi/AIModelDebug';
import { CREATE_API_TYPE } from '~/components/api-manage/createApi';
import ConsumerAuth from '~/components/api-manage/apiAi/consumerAuth';
import RouteList from '~/components/ai-gateway/llm-api/route';
import PubLishStatusText from '~/components/ai-gateway/components/PublishStatus';
const Index = (props) => {
  const {
    aiScene = CREATE_API_TYPE.AI,
    match: {
      params: { id },
    },
    detailData: gatewayData,
  } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [httpApiInfo, setHttpApiInfo] = useState({});
  const [currentTab, setCurrentTab] = useState('');
  const [showEdit, setShowEdit] = useState(false);
  const [debugVisible, setDebugVisible] = useState(false);
  const history = useHistory();
  const { apiId, id: gatewayId } = useParams<any>();
  const query: any = queryDecode();
  const { tabKey } = query;

  const GetHttpApi = async () => {
    let data = {
      httpApiId: apiId,
    };
    try {
      const result = await services.GetHttpApi({
        params: { ...data },
      });
      // const environments = get(result, 'environments', []);
      const firstDeployConfig: any = first(get(result, 'deployConfigs') || []) || {};
      const _environment =
        find(get(result, 'environments', []), {
          environmentId: firstDeployConfig?.environmentId,
        }) || {};
      const retryValidated = retryValidate(_environment);
      setHttpApiInfo(result);
      if (retryValidated) {
        onRetry();
      } else {
        clearTime();
      }
    } catch (error) {
      setHttpApiInfo({});
    }
  };

  const deployStatus = useMemo(() => {
    const environmentId = get(httpApiInfo, 'deployConfigs[0].environmentId', '');
    const environments = get(httpApiInfo, 'environments', []);
    const _environment: any =
      find(environments, {
        environmentId,
      }) || {};
    const deployStatus = _environment?.deployStatus;
    return deployStatus;
  }, [JSON.stringify(httpApiInfo)]);

  const { clearTime, onRetry, retryValidate } = useApiRetry({
    refreshIndex,
    fetchData: GetHttpApi,
  });

  useEffect(() => {
    if (tabKey) {
      setCurrentTab(tabKey);
    } else {
      setCurrentTab('detail');
    }
  }, [tabKey]);

  useEffect(() => {
    GetHttpApi();
  }, [apiId, refreshIndex]);

  const handleTabChange = (val) => {
    setCurrentTab(val);
    let jumpUrl = gatewayId
      ? `/${window.regionId}/gateway/${gatewayId}`
      : `/${window.regionId}/api-manage`;
    history.push(
      aiScene === CREATE_API_TYPE.LLM
        ? `/${window.regionId}/ai-gateway/${id}/llm-api/${apiId}?region=${window.regionId}&tabKey=${val}`
        : `${jumpUrl}/api-ai/${apiId}/?region=${window.regionId}&tabKey=${val}`,
    );
  };

  const initBreadcrumbs = useMemo(() => {
    if (aiScene === CREATE_API_TYPE.LLM) {
      return [
        {
          to: `/${window.regionId}/ai-gateway`,
          text: intl('apigw.components.overview.ResourceList.InstanceManagement'),
        },
        {
          to: `/${window.regionId}/ai-gateway/${id}/detail`,
          text: gatewayData?.Name,
        },
      ];
    } else {
      let navBreadcrumb = [
        {
          text: 'AI API',
          to: `/${window.regionId}/api-manage/api-list`,
        },
      ];

      if (gatewayId && gatewayData?.Name) {
        navBreadcrumb = [
          {
            to: `/${window.regionId}/gateway`,
            text: intl('apigw.components.overview.ResourceList.InstanceManagement'),
          },
          {
            text: gatewayData.Name,
            to: `/${window.regionId}/gateway/${gatewayId}/detail`,
          },
          {
            text: 'API',
            to: `/${window.regionId}/gateway/${gatewayId}/api-list`,
          },
        ];
      }
      return navBreadcrumb;
    }
  }, [aiScene]);

  return (
    <AppLayout
      breadcrumbs={[
        ...(initBreadcrumbs as any),
        {
          text: get(httpApiInfo, 'name') || '',
          to: null,
        },
      ]}
      className={gatewayId && aiScene === CREATE_API_TYPE.AI && 'gateway-api-manage-layout'}
      hasBackArrow
      gatewayType={aiScene === CREATE_API_TYPE.AI ? 'API' : 'AI'}
      onBackArrowClick={() =>
        history.push(
          aiScene === CREATE_API_TYPE.LLM
            ? `/${window.regionId}/ai-gateway/${id}/llm-api?region=${window.regionId}`
            : `/${window.regionId}/${
                gatewayId ? `gateway/${gatewayId}` : 'api-manage'
              }/api-list?region=${window.regionId}&tabKey=${gatewayId ? 'all' : 'ai'}`,
        )
      }
      title={
        <div className="align-center">
          <Truncate type="width" threshold={300} align="t" className="mr-8">
            {get(httpApiInfo, 'name') || ''}
          </Truncate>
          {!isEmpty(httpApiInfo) && (
            <PubLishStatusText
              deployStatus={deployStatus}
              deployErrorMessage={get(httpApiInfo, 'environments[0].deployErrorMessage')}
            />
          )}
        </div>
      }
      titleExtra={
        <div>
          <Button
            className="mr-8"
            disabled={includes(
              [
                API_PUBLISH_STATUS.NOT_PUBLISHED,
                API_PUBLISH_STATUS.PUBLISHING,
                API_PUBLISH_STATUS.BEING_OFFLINE,
              ],

              deployStatus,
            )}
            type="primary"
            onClick={() => setDebugVisible(true)}
          >
            {intl('@ali/widget-edas-microgw::widget.route.test')}
          </Button>
          <Button
            className="mr-8"
            disabled={includes(
              [
                API_PUBLISH_STATUS.NOT_PUBLISHED,
                API_PUBLISH_STATUS.PUBLISHING,
                API_PUBLISH_STATUS.BEING_OFFLINE,
              ],

              deployStatus,
            )}
            onClick={() => {
              setShowEdit(true);
            }}
          >
            {API_PUBLISH_STATUS.FAILED === deployStatus
              ? intl('apigw.api-ai.$apiId.Publish')
              : intl('apigw.api-ai.$apiId.Edit')}
          </Button>
          <ExtendBalloon
            trigger={
              <DeleteApiAction
                httpApiId={get(httpApiInfo, 'httpApiId')}
                apiType={aiScene}
                setRefreshIndex={() => {
                  history.push(
                    aiScene === 'LLM'
                      ? `/${window.regionId}/ai-gateway/${id}/llm-api?region=${window.regionId}`
                      : `/${window.regionId}/api-manage/api-list?region=${window.regionId}&tabKey=ai`,
                  );
                }}
              >
                <Button
                  type="primary"
                  warning
                  disabled={includes(
                    [
                      API_PUBLISH_STATUS.NOT_PUBLISHED,
                      API_PUBLISH_STATUS.PUBLISHING,
                      API_PUBLISH_STATUS.BEING_OFFLINE,
                    ],

                    deployStatus,
                  )}
                >
                  {intl('apigw.api-manage.apiList.ApiListTableProps.Delete')}
                </Button>
              </DeleteApiAction>
            }
            isShow={false}
          >
            {''}
          </ExtendBalloon>
        </div>
      }
    >
      <div id="ai-tab-content">
        <Tab
          size="small"
          shape="wrapped"
          activeKey={currentTab}
          onChange={handleTabChange}
          unmountInactiveTabs
          contentStyle={{
            paddingTop: 16,
            height:
              get(httpApiInfo, 'type') === 'AI' && gatewayId ? 'calc(100vh - 200px )' : 'auto',
          }}
        >
          <Tab.Item key={'detail'} title={intl('apigw.api-ai.$apiId.ApiDetails')}>
            <Detail httpApiInfo={httpApiInfo} setRefreshIndex={setRefreshIndex} aiScene={aiScene} />
          </Tab.Item>
          <Tab.Item key={'policies-plugins'} title={intl('apigw.aiapi.tabs.policyplugin')}>
            <PoliciesPlugins
              httpApiInfo={httpApiInfo}
              setRefreshIndex={setRefreshIndex}
              deployStatus={deployStatus}
            />
          </Tab.Item>
          {get(httpApiInfo, 'type') === 'LLM' && (
            <Tab.Item
              key={'consumer-auth'}
              title={intl('apigw.api-ai.apiId.ConsumerCertification')}
            >
              <ConsumerAuth
                httpApiInfo={httpApiInfo}
                setRefreshIndex={setRefreshIndex}
                aiScene={aiScene}
              />
            </Tab.Item>
          )}
          <Tab.Item key={'user-guide'} title={intl('apigw.aiapi.tabs.usage')}>
            <UserGuide httpApiInfo={httpApiInfo} />
          </Tab.Item>
          <Tab.Item key={'statistics'} title={intl('apigw.aiapi.tabs.statistics')}>
            <Statistics httpApiInfo={httpApiInfo} setRefreshIndex={setRefreshIndex} />
          </Tab.Item>
          <Tab.Item key={'logs'} title={intl('mse.common.log.name')}>
            <Logs httpApiInfo={httpApiInfo} setRefreshIndex={setRefreshIndex} />
          </Tab.Item>
          {get(httpApiInfo, 'type') === 'LLM' && (
            <Tab.Item key={'route'} title={intl('apigw.components.api-manage.RouteList')}>
              <RouteList
                httpApiInfo={httpApiInfo}
                refreshIndex={refreshIndex}
                gatewayId={id}
                deployStatus={deployStatus}
                retryValidate={retryValidate}
              />
            </Tab.Item>
          )}
        </Tab>
        {showEdit && (
          <CreateOrUpdateApiSidePanel
            {...{
              type: 'edit',
              apiScene: aiScene,
              setRefreshIndex,
              apiId,
              httpApiInfo,
              setVisible: () => {
                setShowEdit(false);
              },
            }}
          />
        )}
        {debugVisible && (
          <AIModelDebug
            visible={debugVisible}
            onClose={() => setDebugVisible(false)}
            httpApiInfo={httpApiInfo}
            apiScene={aiScene}
            gatewayId={gatewayId}
          />
        )}
      </div>
    </AppLayout>
  );
};

export default Index;
