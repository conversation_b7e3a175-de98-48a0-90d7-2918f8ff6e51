import React, { useEffect, useState, useRef } from 'react';
import { useParams } from '@ali/xconsole/hooks';
import { Button, intl, Tab, Truncate, useHistory } from '@ali/cnd';
import { get } from 'lodash';
import services from '~/utils/services';
import AppLayout from '~/containers/AppLayout';
import BasicInfo from '~/components/consumer-manage/consumer-basic-infor';
import Auth from '~/components/consumer-manage/consumer-auth';
// import CreateConsumerSlide from '~/components/consumer-manage/components/CreateConsumerSlide';
// import { CREATE_OR_EDIT } from '~/components/consumer-manage/components/CreateConsumerSlide/Authentication';
import DeleteConsumer from '~/components/consumer-manage/consumer-list/DeleteConsumer';
import Status from '~/components/shared/Status';
import AiGatewayConsumerAuth from '~/components/consumer-manage/consumer-auth/AiGatewayConsumerAuth';

const Index = ({ gatewayType = 'API' }) => {
  const { id } = useParams<any>();
  const history = useHistory();
  const [currentTab, setCurrentTab] = useState('basicInfo');
  const [currentDetail, setCurrentDetail] = useState<any>({});

  useEffect(() => {
    getConsumer();
  }, []);

  const getConsumer = async () => {
    try {
      const result: any = await services.GetConsumer({
        params: { consumerId: id },
      });
      setCurrentDetail(result || {});
      return result;
    } catch (error) {
      return {};
    }
  };

  const handleTabChange = (val) => {
    setCurrentTab(val);
    history.push(
      gatewayType === 'API'
        ? `/${window.regionId}/consumer-manage/${id}?region=${window.regionId}&tabKey=${val}`
        : `/${window.regionId}/ai-gateway-consumer-manage/${id}?region=${window.regionId}&tabKey=${val}`,
    );
  };

  const goToListPage = () => {
    history.push(
      gatewayType === 'API'
        ? `/${window.regionId}/consumer-manage`
        : `/${window.regionId}/ai-gateway-consumer-manage`,
    );
  };

  return (
    <AppLayout
      gatewayType={gatewayType}
      breadcrumbs={[
        {
          text: intl('apigw.consumer-manage.$id.Consumers'),
          to:
            gatewayType === 'API'
              ? `/${window.regionId}/consumer-manage`
              : `/${window.regionId}/ai-gateway-consumer-manage`,
        },
        {
          text: `${currentDetail?.name || ''}`,
        },
      ]}
      hasBackArrow
      onBackArrowClick={goToListPage}
      title={
        <div className="align-center">
          <Truncate type="width" threshold={300} align="t">
            {currentDetail?.name || ''}
          </Truncate>
          <div style={{ fontSize: 12, display: 'inline-block', marginLeft: 8, height: 40 }}>
            <Status
              value={get(currentDetail, 'enable', false)}
              dataSource={[
                {
                  label: intl('apigw.consumer-manage.$id.Enabled'),
                  value: true,
                  iconType: 'check_fill',
                  type: 'success',
                },
                {
                  label: intl('apigw.consumer-manage.$id.Disabled'),
                  value: false,
                  type: 'minus_fill',
                },
              ]}
            />
          </div>
        </div>
      }
      titleExtra={
        <div>
          <DeleteConsumer
            className="ml-8"
            isButton
            consumer={currentDetail}
            onRefresh={goToListPage}
          />
        </div>
      }
    >
      <Tab
        size="medium"
        shape="wrapped"
        activeKey={currentTab}
        onChange={handleTabChange}
        unmountInactiveTabs
        contentClassName={'mt-16'}
      >
        <Tab.Item key={'basicInfo'} title={intl('apigw.consumer-manage.$id.BasicInformation')}>
          <BasicInfo
            currentDetail={currentDetail}
            onRefresh={getConsumer}
            gatewayType={gatewayType}
          />
        </Tab.Item>
        <Tab.Item
          key={'consumerAuth'}
          title={intl('apigw.consumer-manage.$id.ConsumerAuthorization')}
        >
          {gatewayType === 'API' ? (
            <Auth currentDetail={currentDetail} consumerId={id} gatewayType={gatewayType} />
          ) : (
            <Tab size="medium" unmountInactiveTabs contentClassName={'mt-16'}>
              <Tab.Item key={'llm'} title={'Model API'}>
                <AiGatewayConsumerAuth
                  currentDetail={currentDetail}
                  consumerId={id}
                  gatewayType={gatewayType}
                  resourceType={'LLM'}
                />
              </Tab.Item>
              <Tab.Item key={'mcp'} title={'MCP'}>
                <AiGatewayConsumerAuth
                  currentDetail={currentDetail}
                  consumerId={id}
                  gatewayType={gatewayType}
                  resourceType={'MCP'}
                />
              </Tab.Item>
              <Tab.Item key={'agent'} title={'Agent API'}>
                <AiGatewayConsumerAuth
                  currentDetail={currentDetail}
                  consumerId={id}
                  gatewayType={gatewayType}
                  resourceType={'Agent'}
                />
              </Tab.Item>
            </Tab>
          )}
        </Tab.Item>
      </Tab>
    </AppLayout>
  );
};

export default Index;
