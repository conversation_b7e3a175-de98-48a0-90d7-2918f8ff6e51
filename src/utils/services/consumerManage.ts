import request from './request';
import { get, forEach, isEmpty } from 'lodash';

const formatApiKeyIdentityConfig = (formatType = 'output', data) => {
  if (isEmpty(data)) return {};
  if (formatType === 'output') {
    let apikeySource = get(data, '[0].apikeySource');
    let type = get(data, '[0].type');
    let credentials = [];
    forEach(data, (item) => {
      const { type, apikeySource, ...rest } = item;
      if (!isEmpty(rest)) credentials.push(rest);
    });
    return {
      apiKeyIdentityConfig: {
        apikeySource,
        type,
        credentials,
      },
    };
  } else {
    const credentials = get(data, 'credentials', []);
    let apikeySource = get(data, 'apikeySource');
    let type = get(data, 'type');
    let apiKeyIdentityConfigs = [];
    if (isEmpty(credentials)) {
      return {
        apiKeyIdentityConfigs: [{ apikeySource, type }],
      };
    } else {
      forEach(credentials, (item) => {
        apiKeyIdentityConfigs.push({
          type,
          apikeySource,
          ...item,
        });
      });
      return {
        apiKeyIdentityConfigs,
      };
    }
  }
};

const ListConsumers = request({
  method: 'GET',
  product: 'APIG',
  action: 'ListConsumers',
});

const CreateConsumer = request({
  method: 'POST',
  product: 'APIG',
  action: 'CreateConsumer',
  requestInterceptor: (requestParams) => {
    const { apiKeyIdentityConfigs = [], ...rest } = get(requestParams, 'content');
    return {
      content: {
        ...formatApiKeyIdentityConfig('output', apiKeyIdentityConfigs),
        ...rest,
      },
    };
  },
});

const UpdateConsumer = request({
  method: 'PUT',
  product: 'APIG',
  action: 'UpdateConsumer',
  requestInterceptor: (requestParams) => {
    const { apiKeyIdentityConfigs = [], ...rest } = get(requestParams, 'content');
    return {
      params: {
        ...requestParams.params,
      },
      content: {
        ...formatApiKeyIdentityConfig('output', apiKeyIdentityConfigs),
        ...rest,
      },
    };
  },
});

const GetConsumer = request({
  method: 'GET',
  product: 'APIG',
  action: 'GetConsumer',
  responseInterceptor(res) {
    const { apiKeyIdentityConfig, ...rest } = res;
    //注意: 因接口数据结构变更，统一过滤掉 apiKeyIdentityConfig 字段，按照 apiKeyIdentityConfigs 字段处理
    return {
      ...rest,
      ...formatApiKeyIdentityConfig('in', apiKeyIdentityConfig),
    };
  },
});

const DeleteConsumer = request({
  method: 'DELETE',
  product: 'APIG',
  action: 'DeleteConsumer',
});

const ListConsumerAuthorizationRules = request({
  method: 'GET',
  product: 'APIG',
  action: 'ListConsumerAuthorizationRules',
});

const QueryConsumerAuthorizationRules = request({
  method: 'GET',
  product: 'APIG',
  action: 'QueryConsumerAuthorizationRules',
});

const GetConsumerAuthorizationRule = request({
  method: 'GET',
  product: 'APIG',
  action: 'GetConsumerAuthorizationRule',
});

const DeleteConsumerAuthorizationRule = request({
  method: 'GET',
  product: 'APIG',
  action: 'DeleteConsumerAuthorizationRule',
});

const RemoveConsumerAuthorizationRule = request({
  method: 'GET',
  product: 'APIG',
  action: 'RemoveConsumerAuthorizationRule',
});

const BatchDeleteConsumerAuthorizationRule = request({
  method: 'GET',
  product: 'APIG',
  action: 'BatchDeleteConsumerAuthorizationRule',
});
const CreateConsumerAuthorizationRule = request({
  method: 'POST',
  product: 'APIG',
  action: 'CreateConsumerAuthorizationRule',
});

const AddConsumerAuthorizationRule = request({
  method: 'POST',
  product: 'APIG',
  action: 'AddConsumerAuthorizationRule',
});

const CreateConsumerAuthorizationRules = request({
  method: 'POST',
  product: 'APIG',
  action: 'CreateConsumerAuthorizationRules',
});

const UpdateConsumerAuthorizationRule = request({
  method: 'PUT',
  product: 'APIG',
  action: 'UpdateConsumerAuthorizationRule',
});
const UpdateAuthorizationRule = request({
  method: 'POST',
  product: 'APIG',
  action: 'UpdateAuthorizationRule',
});
export default {
  ListConsumers,
  CreateConsumer,
  UpdateConsumer,
  GetConsumer,
  DeleteConsumer,
  ListConsumerAuthorizationRules,
  QueryConsumerAuthorizationRules,
  GetConsumerAuthorizationRule,
  DeleteConsumerAuthorizationRule,
  RemoveConsumerAuthorizationRule,
  BatchDeleteConsumerAuthorizationRule,
  CreateConsumerAuthorizationRule,
  AddConsumerAuthorizationRule,
  CreateConsumerAuthorizationRules,
  UpdateConsumerAuthorizationRule,
  UpdateAuthorizationRule
};
