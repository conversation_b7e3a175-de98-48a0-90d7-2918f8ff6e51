import AES from '@ali/aes-tracker';
import AESPluginEvent from '@ali/aes-tracker-plugin-event';
import AESPluginAPI from '@ali/aes-tracker-plugin-api';
import CachedData from './cacheData';
import { noop } from 'lodash';

const env = () =>
  process.env.NODE_ENV === 'development'
    ? 'dev'
    : window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre'
      ? 'pre'
      : 'prod';
export const AES_CONFIG = {
  pid: 'tF6mNc',
  user_type: 6,
  env,
  username: config?.ACCOUNT_NAME,
  uid: config?.CURRENT_PK || config?.MAIN_ACCOUNT_PK,
};
/**
 * AEM 自定义事件上报设置
 */
const config = window.ALIYUN_CONSOLE_CONFIG;
export const aes = CachedData.isEaChannel() ? noop : new AES(AES_CONFIG);
export const sendEvent = CachedData.isEaChannel() ? noop : aes.use(AESPluginEvent);
export const sendApi =CachedData.isEaChannel() ? noop : aes.use(AESPluginAPI).sendApi;
