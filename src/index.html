<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta name="aplus-core" content="aplus.js" />
    <script>
      if ('function' === typeof define && define.amd) {
        window.tmp_define = define.amd;
        delete define.amd;
      }
    </script>
    <script
      crossorigin="anonymous"
      src="//g.alicdn.com/code/lib/??react/16.14.0/umd/react.production.min.js,react-dom/16.14.0/umd/react-dom.production.min.js,prop-types/15.7.2/prop-types.js"
    ></script>
    <% if (__dev__) { %>
    <script
      crossorigin="anonymous"
      src="//g.alicdn.com/code/lib/??react/16.14.0/umd/react.development.min.js,react-dom/16.14.0/umd/react-dom.development.min.js,prop-types/15.7.2/prop-types.js"
    ></script>
    <% } %>
    <script>
      window.ReactDOM = window.ReactDom || window.ReactDOM;
      window.ReactDom = window.ReactDOM;
      React.PropTypes = window.PropTypes;
      window.Component = React.Component;
    </script>

    <title>API Gateway</title>
    <script nonce="W39KS1q9gTjiXWJyKrGy">
      var ONE_CONSOLE_TOOL = {
        extend: function (o, n) {
          if (!o) o = {};
          if (!n) n = {};
          for (var p in n) {
            o[p] = n[p];
          }
          return o;
        }
      };
    </script>
    <script>
      (function (w, d, s, q, i) {
        w[q] = w[q] || [];
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s);
        j.async = true;
        j.id = 'beacon-aplus';
        j.setAttribute('exparams', 'userid=&aplus&sidx=aplusSidex&ckx=aplusCkx');
        j.src = '//g.alicdn.com/alilog/mlog/aplus_v2.js';
        j.crossorigin = 'anonymous';
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'aplus_queue');
    </script>
  <script
    src="https://oneconsole.oss-cn-shanghai.aliyuncs.com/dev-new-one-mcms/apigw/apigw_zh-cn.js?timestamp=1748502652831"
    charset="utf-8" nonce="EPuBXVSFAT5L3Pkqbpo9"></script>
  <script nonce="EPuBXVSFAT5L3Pkqbpo9">
    var aliyunConsoleI18nMessage = {};
    aliyunConsoleI18nMessage = ONE_CONSOLE_TOOL.extend(window["apigw_zh-cn"], aliyunConsoleI18nMessage);
  </script>
  <script nonce="EPuBXVSFAT5L3Pkqbpo9">
    var ALIYUN_CONSOLE_I18N_MESSAGE =
      aliyunConsoleI18nMessage;
  </script>
    <% if (__dev__) { %>
    <!-- 以下只在开发环境生效，千万不要写出 这个 if 判断，线上具体配置以 Viper 上的配置为主-->
    <script>
      function setCookie(c) {
        let cs = (c && c.split(';')) || [];
        cs.map((s) => {
          document.cookie = s;
        });
      }
      var ALIYUN_CONSOLE_CONFIG = {
        LANG: 'zh',
        LOCALE: 'zh_cn',
        portalType: 'one',
        SEC_TOKEN: localStorage.getItem('sec_token'),
        CHANNEL_FEATURE_STATUS: {},
        // CHANNEL: 'EA134',
        VERSION_GRAY_CONFIG: {
          // enabled: true,
          // type: 'region',
        },
        CHANNEL_LINKS: {
          'feature:overview:QuickStart': 'https://www.aliyun.com',
          'feature:overview:OpenAPI': 'https://www.aliyun.com',
          'feature:overview:SDK': 'https://www.aliyun.com',
          'feature:overview:RecentNews:more': 'https://www.aliyun.com',
          'feature:overview:HighlightPractices:more': 'https://www.aliyun.com',
          'feature:consumer:policy:help': 'https://www.aliyun.com',
          "feature:api:help": "https://help.aliyun.com/zh/api-gateway/cloud-native-api-gateway/user-guide/api-management",
          "feature:gateway:help": "https://help.aliyun.com/zh/api-gateway/cloud-native-api-gateway/user-guide/gateway-management",
          "feature:plugin:help": "https://help.aliyun.com/zh/api-gateway/cloud-native-api-gateway/user-guide/plug-in-management",
          "feature:environment:help": "https://help.aliyun.com/zh/api-gateway/cloud-native-api-gateway/user-guide/environmental-management",
          "feature:domain:help": "https://help.aliyun.com/zh/api-gateway/cloud-native-api-gateway/user-guide/domain-name-management",
          "feature:alarm:help": "https://help.aliyun.com/zh/api-gateway/cloud-native-api-gateway/user-guide/alarm-management",
          "feature:migration:help": "https://help.aliyun.com/zh/api-gateway/cloud-native-api-gateway/user-guide/gateway-migration",
          'feature:http2Config:help':
            'https://help.aliyun.com/zh/api-gateway/cloud-native-api-gateway/user-guide/modifying-gateway-parameters',
          'feature:gateway:common:buy':
            'https://pre-valid-common-buy.aliyun.com/?commodityCode=apigateway_nativepost_public_cn&regionId={regionId}',
          'feature:gateway:nativepost:upgrade':
            'https://pre-valid-common-buy.aliyun.com/?commodityCode=apigateway_nativepost_public_cn&orderType=UPGRADE&instanceId={gatewayId}',
          'feature:gateway:nativepost:convert':
            'https://pre-valid-common-buy.aliyun.com/?commodityCode=apigateway_nativepost_public_cn&orderType=CONVERT&instanceId={gatewayId}&convertCommodityCode=apigateway_nativepre_public_cn#/convert',
          'feature:gateway:nativepre:upgrade':
            'https://pre-valid-common-buy.aliyun.com/?commodityCode=apigateway_nativepre_public_cn&orderType=UPGRADE&instanceId={gatewayId}',
          'feature:gateway:nativepre:convert':
            'https://pre-valid-common-buy.aliyun.com/?commodityCode=apigateway_nativepre_public_cn&orderType=CONVERT&instanceId={gatewayId}&convertCommodityCode=apigateway_nativepost_public_cn#/convert',
          'feature:gateway:nativepre:renew':
            'https://pre-valid-common-buy.aliyun.com/?commodityCode=apigateway_nativepre_public_cn&orderType=RENEW&instanceId={gatewayId}',
          'feature:search:engine:open': 'https://common-buy.aliyun.com/?commodityCode=alibababcp_ecosearch_public_cn'
        },
        CHANNEL_FEATURE_STATUS: {
          'oss:noRegionNotSupport:tip': {
            status: true,
          },
          //
        },
        REGIONS: [
          {
            "name": "华南1（深圳）",
            "physicalList": [
              {
                "id": "cn-shenzhen"
              }
            ],
            "regionId": "cn-shenzhen",
            "zoneList": []
          },
          {
            "name": "华北2（北京）",
            "physicalList": [
              {
                "id": "cn-beijing"
              }
            ],
            "regionId": "cn-beijing",
            "zoneList": []
          },
          {
            "name": "华北6（乌兰察布）",
            "physicalList": [
              {
                "id": "cn-wulanchabu"
              }
            ],
            "regionId": "cn-wulanchabu",
            "zoneList": []
          },
          {
            "name": "日本（东京）",
            "physicalList": [
              {
                "id": "ap-northeast-1"
              }
            ],
            "regionId": "ap-northeast-1",
            "zoneList": []
          },
          {
            "name": "西南1（成都）",
            "physicalList": [
              {
                "id": "cn-chengdu"
              }
            ],
            "regionId": "cn-chengdu",
            "zoneList": []
          },
          {
            "name": "华东2（上海）",
            "physicalList": [
              {
                "id": "cn-shanghai"
              }
            ],
            "regionId": "cn-shanghai",
            "zoneList": []
          },
          {
            "name": "华东 2 金融云",
            "physicalList": [
              {
                "id": "cn-shanghai-finance-1"
              }
            ],
            "regionId": "cn-shanghai-finance-1",
            "zoneList": []
          },
          {
            "name": "中国香港",
            "physicalList": [
              {
                "id": "cn-hongkong"
              }
            ],
            "regionId": "cn-hongkong",
            "zoneList": []
          },
          {
            "name": "新加坡",
            "physicalList": [
              {
                "id": "ap-southeast-1"
              }
            ],
            "regionId": "ap-southeast-1",
            "zoneList": []
          },
          {
            "name": "德国（法兰克福）",
            "physicalList": [
              {
                "id": "eu-central-1"
              }
            ],
            "regionId": "eu-central-1",
            "zoneList": []
          },
          {
            "name": "印度尼西亚（雅加达）",
            "physicalList": [
              {
                "id": "ap-southeast-5"
              }
            ],
            "regionId": "ap-southeast-5",
            "zoneList": []
          },
          {
            "name": "美国（弗吉尼亚）",
            "physicalList": [
              {
                "id": "us-east-1"
              }
            ],
            "regionId": "us-east-1",
            "zoneList": []
          },
          {
            "name": "华北3（张家口）",
            "physicalList": [
              {
                "id": "cn-zhangjiakou"
              }
            ],
            "regionId": "cn-zhangjiakou",
            "zoneList": []
          },
          {
            "name": "美国（硅谷）",
            "physicalList": [
              {
                "id": "us-west-1"
              }
            ],
            "regionId": "us-west-1",
            "zoneList": []
          },
          {
            "name": "华东1（杭州）",
            "physicalList": [
              {
                "id": "cn-hangzhou"
              }
            ],
            "regionId": "cn-hangzhou",
            "zoneList": []
          }
        ],
        FEATURE_STATUS: {
          'gateway:custom:clb:visible': true, //自定义入口可见
          'api:swagger:customURL:visible': true, //自定义url导入API可见性
        },
      };
      var ALIYUN_CONSOLE_GLOBAL = {
        "eaChannel": [
          {
            "EA134": {
              "host": "idptcloud01",
              "feature": {
                "aiEnable": false
              }
            }
          }
        ],
        'widget-edas-microgw': {
          OPEN_ROUTER_REGION: ['cn-hangzhou'],
        },
        fcDisabledRegions: ['cn-hangzhou'],
        "channelRegions": {
          "FINANCE": [
            {
              "id": "cn-shanghai-finance-1",
              "name": "apigw.config.constants.EastChinaFinancialCloud"
            }
          ]
        },
        WIDGET_CONSOLE_CONFIG: {
          edasmicrogw: {
            isUsePopApi: true,
            productName: 'mse',
            id: '@ali/widget-edas-microgw',
            version: '1.6.581',
          },
        },
        preTestEnvs: [
          {
            "label": "开发环境",
            "value": "dev",
            "ip": {
              "apig": ""
            }
          },
          {
            "label": "测试环境",
            "value": "test",
            "ip": {
              "apig": "**************:36457"
            }
          },
          {
            "label": "梧同专用环境",
            "value": "wutong",
            "ip": {
              "apig": "*************:17502"
            }
          },
          {
            "label": "聪言专用环境",
            "value": "congyan",
            "ip": {
              "apig": "*************:23318"
            }
          },
          {
            "label": "十眠专用环境",
            "value": "shimian",
            "ip": {
              "apig": "*************:16078"
            }
          }
        ],
        regionList: [],
        MICROGW_OPEN_SERVICE_REGION: {
          CN: [
            'cn-hangzhou',
            'cn-shanghai',
            'cn-beijing',
            'cn-shenzhen',
            'cn-zhangjiakou',
            'cn-qingdao',
            'eu-central-1',
            'cn-hongkong',
            'ap-southeast-1',
            'us-west-1',
            'us-east-1',
            'cn-shanghai-finance-1',
            'ap-southeast-5',
            'ap-northeast-1',
            'ap-southeast-3',
            'cn-shenzhen-finance-1',
            'me-central-1',
            'cn-north-2-gov-1',
            'cn-beijing-finance-1',
            'cn-chengdu',
            'cn-guangzhou',
            'cn-wulanchabu',
            'ap-south-1',
          ],
          INTL: [
            'cn-hangzhou',
            'cn-shanghai',
            'cn-beijing',
            'cn-shenzhen',
            'cn-zhangjiakou',
            'eu-central-1',
            'cn-hongkong',
            'ap-southeast-1',
            'us-west-1',
            'us-east-1',
            'cn-shanghai-finance-1',
            'ap-southeast-5',
            'ap-northeast-1',
            'ap-southeast-3',
            'cn-shenzhen-finance-1',
            'me-central-1',
            'cn-north-2-gov-1',
            'cn-beijing-finance-1',
            'cn-chengdu',
            'cn-guangzhou',
            'cn-wulanchabu',
            'ap-south-1',
          ],
        },
        practices: [
          {
            title: '根据业务需求，定义API的访问路径。',
            href: 'https://www.aliyun.com',
          },
          {
            title: '添加和配置插件，以实现认证、限流、熔断等功能。',
            href: 'https://www.aliyun.com',
          },
          {
            title: '将API路由与后端微服务进行映射，实现API调用的转发。',
            href: 'https://www.aliyun.com',
          },
          {
            title: '查看API的运行状态和性能指标，及时发现并解决问题。',
            href: 'https://www.aliyun.com',
          },
        ],
      };
      var RISK_INFO = {
        GETUA: function () {
          return 'mock-collina-ua';
        },
        UMID: 'aliyun_umid',
      };
      setCookie(localStorage.getItem('cookie'));
    </script>
    <% }%>
  </head>
  <body>
    <div id="app"></div>
  <script>
    var WIDGET_CONSOLE_CONFIG = window.ALIYUN_CONSOLE_GLOBAL.WIDGET_CONSOLE_CONFIG;
  </script>
  <script>
    function getPageId(url) {
      if (!url) return;
      var hash = new URL(url).hash;
      var arr = hash.split('?');
      if (arr && arr.length) {
        var u = arr[0];
        u = u.replace(/\/cn-\w+/g, '/cn-region');
        u = u.replace(/\/gw-\w+/g, '/gw-id');
        var apiTypeUrls = [
          '/api-manage/api-rest',
          '/api-manage/api-http',
          '/api-manage/api-ingress',
          '/api-manage/api-websocket',
          '/plugin-manage/',
        ];
        for (var i = 0; i < apiTypeUrls.length; ++i) {
          var detailUrl = apiTypeUrls[i];
          var detailIndex = u.indexOf(detailUrl);
          if (detailIndex !== -1) {
            u = u.substring(0, detailIndex + detailUrl.length) + '/id';
          }
        }
        if (arr.length > 1) {
          var search = arr[1];
          search = search.replace(/=cn-\w+/g, '=cn-region');
          u = u + '?' + search;
        }
        return u;
      }
      return hash;
    }
    (function (d) {
      var t = d.createElement('script');
      t.type = 'text/javascript';
      t.async = true;
      t.src = '//g.alicdn.com/cm-design/mw-vendor/1.6.9/aem-3.1.0.js';
      t.onload = function () {
        window.aes = new AES({
          pid: 'tF6mNc',
          user_type: 6,
          uid: window.ALIYUN_CONSOLE_CONFIG.CURRENT_PK || '',
          username: window.ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK || '',
          env: window.ALIYUN_CONSOLE_GLOBAL.environment === 'pre' ? 'pre' : 'prod',
        });
        window.AESPluginAutologConfig = {
          exposure: 'auto',
        };
        window.AEMPluginInstances = [
          aes.use(AESPluginPV, {
            autoPV: true,
            enableHash: true,
            getPageId,
          }),
          aes.use(AESPluginEvent, window.AESPluginEvent || {}),
          aes.use(AESPluginPerf, window.AESPluginPerf || {}),
          aes.use(AESPluginLongTask, window.AESPluginLongTask || {}),
          aes.use(AESPluginBlank, window.AESPluginBlank || {}),
          aes.use(AESPluginEventTiming, window.AESPluginEventTiming || {}),
          aes.use(AESPluginAutolog, window.AESPluginAutolog || {}),
          aes.use(AESPluginJSError, window.AESPluginJSError || {}),
          aes.use(AESPluginAPI, window.AESPluginAPI || {}),
          aes.use(AESPluginEmogine, {
            plugin_emogine_auto_reset_with_hash: true,
          }),
        ];
      };
      setTimeout(function () {
        d.getElementsByTagName('body')[0].appendChild(t);
      }, 1000);
    })(document);
  </script>
</html>
