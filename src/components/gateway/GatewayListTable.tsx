import { Button, CndTable, intl, useHistory } from '@ali/cnd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import {
  isGatewayNotReady,
  isGatewayNotRelease,
} from '~/pages/$regionId/gateway/utils/transformer';
import services from '~/utils/services';
import { GatewayListColumns } from './GatewayListColumns';
import { GatewayListSearch } from './GatewayListSearch';
import CachedData from '~/utils/cacheData';
import { changeRegion, compareVersion, isRegionGrayEnabled } from '~/utils/index';
import { RegionGuidance } from '@alicloud/console-components-pro/dist';
import '@alicloud/console-components-pro/dist/index.css';
import { getAllRegionResources } from '~/utils/services/overview';
import { GATEWAY_LIST_CLICK_TYPE, trackGatewayListClickEvent } from '~/track';
import { find, isEmpty, map, get } from 'lodash';
import BatchTagEditorAction from './gateway-action/BatchTagEditorAction';
import BatchUntagEditorAction from './gateway-action/BatchUntagEditorAction';
import {
  useGetRamAuthStatus,
  useResourceGroupId,
  useSetRamAuthInterfaceStatus,
} from '../app-provider';
import { AUTH_INTERFACE } from '../app-provider/authInterface';
import BaseAuthMessage from '../shared/AuthMessage/BaseAuthMessage';
import { queryEncode, queryDecodeHash, changeQuery } from '~/utils/queryString';

export interface GatewayListTableProps {
  /**
   * 释放实例方法
   * @param record
   * @returns
   */
  releaseInstance: (record) => void;
  /**查看监控 */
  handleOpenMonitor: (record) => void;
  /**手动升级 */
  handleUpgradeVersion: (record) => void;
  handleDefaultAlertRule: (record) => void;
  handleElasticExpansion: (record) => void;
  handleWafPolicy: (record) => void;
  gatewayType: string;
}

export const GatewayListTable = forwardRef((props: GatewayListTableProps, ref) => {
  const [refreshIndex, setRefreshIndex] = useState(0);
  const history = useHistory();
  const [isLoop, setIsLoop] = useState(false);
  const [regionList, setRegionList] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [tags, setTags] = useState([]);
  const authDispatch = useSetRamAuthInterfaceStatus();
  const resourceAuthStatus = useGetRamAuthStatus(AUTH_INTERFACE.GET_RESOURCE_OVERVIEW);

  const resourceGroupId = useResourceGroupId();

  useEffect(() => {
    setRefreshIndex(refreshIndex + 1);
  }, [resourceGroupId]);

  useEffect(() => {
    getAllRegionResources(authDispatch, props.gatewayType).then(
      ({ APIGatewayTotalCount = {}, AIGatewayTotalCount = {} }) => {
        let data = props.gatewayType === 'API' ? APIGatewayTotalCount : AIGatewayTotalCount;
        const list = Object.entries(data)
          .map(([id, count]) => ({
            id,
            count,
          }))
          .filter((item) => +item.count > 0);
        setRegionList(list);
      },
    );
  }, []);

  useImperativeHandle(ref, () => {
    return {
      fetchData: () => setRefreshIndex(Date.now()),
    };
  });
  const fetchData = async (params) => {
    setIsLoop(false);
    const { keyword, gatewayId } = params;
    params = {
      ...params,
      gatewayId: gatewayId?.trim() || undefined,
      keyword: keyword?.trim() || undefined,
      pageNumber: params.current,
      gatewayType: props.gatewayType,
      tag: isEmpty(tags)
        ? undefined
        : map(tags, (tag) => ({ key: tag.tagKey, value: tag.tagValue })),
    };
    const res = await services.getGatewayList({
      customErrorHandle: (err, data, callback) => {
        if (!(get(err, 'code') === 'Forbidden.AccessDenied' && CachedData.isSubAccount())) {
          callback();
        }
        return { items: [], totalSize: 0 };
      },
      params,
    });
    const items = res?.items || [];
    let Result = [];
    let creatingItems = [];
    items.forEach((gateWayItem) => {
      if (
        ['Creating', 'Restarting', 'Upgrading', 'Deleting', 'Starting', 'Stopping'].includes(
          gateWayItem.status,
        )
      ) {
        creatingItems.push(gateWayItem);
      }
      Result.push({
        ...gateWayItem,
        visitMethodCollection: handleInternetSlb(gateWayItem),
        upgrade:
          gateWayItem.targetVersion &&
          compareVersion(gateWayItem.targetVersion, gateWayItem.version) === 1,
        actionLinksConfig: handleActionLinksDisabled(gateWayItem),
      });
    });
    if (creatingItems.length > 0) {
      setIsLoop(true);
    } else {
      setIsLoop(false);
    }
    return {
      data: Result,
      total: res?.totalSize || 0,
      // data: [],
      // total: 0,
    };
  };

  const handleInternetSlb = (record) => {
    const { loadBalancers = [] } = record;
    let InternetSlb = [];
    let Slb = [];
    if (loadBalancers && loadBalancers.length > 0) {
      loadBalancers.forEach((item) => {
        const ports = item?.ports || [];
        const portList = [];
        ports.forEach((p) => {
          if (p?.port && p?.protocol !== 'UDP') {
            portList.push(p.port);
          }
        });
        const newItem = {
          SlbId: item?.loadBalancerId,
          SlbPort: portList.join(',') || '',
          SlbIp: item?.address,
          GatewaySlbStatus: item?.status,
          SlbSpec: item?.loadBalancerSpec,
        };
        if (item?.addressType === 'Internet') {
          InternetSlb.push(newItem);
        }
        if (item?.addressType === 'Intranet') {
          Slb.push(newItem);
        }
      });
    }
    if (!InternetSlb.length && !Slb.length) return [];
    // 公网
    const internetCollect = [];
    InternetSlb.forEach((publicSlb) => {
      const { SlbIp, SlbPort } = publicSlb;
      if (!SlbIp) return;
      const internetURI = `${SlbIp}/${SlbPort}`;
      internetCollect.push(internetURI);
    });

    // 私网
    const innernetCollect = [];
    Slb.forEach((privateSlb) => {
      const { SlbIp, SlbPort } = privateSlb;
      if (!SlbIp) return;
      const innernetURI = `${SlbIp}/${SlbPort}`;
      innernetCollect.push(innernetURI);
    });

    const AccessNetwork = [];
    if (internetCollect && internetCollect.length) {
      internetCollect.forEach((internet) => {
        AccessNetwork.push(
          `${internet}(${intl('@ali/widget-edas-microgw::widget.gateway.access_internet')})`,
        );
      });
    }
    if (innernetCollect && innernetCollect.length) {
      innernetCollect.forEach((innernet) => {
        AccessNetwork.push(
          `${innernet}(${intl('@ali/widget-edas-microgw::widget.gateway.access_innernet')})`,
        );
      });
    }
    return AccessNetwork;
  };

  const handleActionLinksDisabled = (record) => {
    const { gatewayId, status, chargeType, elasticEnabled } = record;
    const isShowBusinessLink = true;
    const actionLinks = [
      {
        operateName: intl('apigw.components.overview.ResourceList.ApiManagement'),
        // isDisabled: isGatewayNotReady(status),
        visible: props.gatewayType === 'API',
        callback: () => {
          history.push(`/${window.regionId}/gateway/${gatewayId}/api-list`);
        },
      },

      {
        operateName: intl('apigw.components.gateway.GatewayListTable.Upgrade'),
        isDisabled: status !== 'Running' || elasticEnabled,
        visible: isShowBusinessLink,
        balloonTip: intl('apigw.components.gateway.GatewayListTable.AutomaticScalingIsEnabledFor'),
        callback: () => {
          let upgradeUrl = '';
          if (chargeType === 'POSTPAY') {
            upgradeUrl = CachedData.confLink(
              get(props, 'gatewayType', 'API') === 'API'
                ? 'feature:gateway:nativepost:upgrade'
                : 'feature:ai:gateway:nativepost:upgrade',
              {
                gatewayId,
              },
            );
          }
          if (chargeType === 'PREPAY') {
            upgradeUrl = CachedData.confLink(
              get(props, 'gatewayType', 'API') === 'API'
                ? 'feature:gateway:nativepre:upgrade'
                : 'feature:ai:gateway:nativepre:upgrade',
              {
                gatewayId,
              },
            );
          }
          trackGatewayListClickEvent({ type: GATEWAY_LIST_CLICK_TYPE.upgrade });
          upgradeUrl && (upgradeUrl += `&regionId=${window.regionId}`);
          upgradeUrl && window.open(upgradeUrl, '_blank');
        },
      },
      {
        operateName: intl('apigw.components.gateway.GatewayListTable.Downgrade'),
        isDisabled: status !== 'Running' || elasticEnabled,
        visible: isShowBusinessLink,
        balloonTip: intl('apigw.components.gateway.GatewayListTable.AutomaticScalingIsEnabledFor'),
        callback: () => {
          let upgradeUrl = '';
          if (chargeType === 'POSTPAY') {
            upgradeUrl = CachedData.confLink(
              get(props, 'gatewayType', 'API') === 'API'
                ? 'feature:gateway:nativepost:downgrade'
                : 'feature:ai:gateway:nativepost:downgrade',
              {
                gatewayId,
              },
            );
          }
          if (chargeType === 'PREPAY') {
            upgradeUrl = CachedData.confLink(
              get(props, 'gatewayType', 'API') === 'API'
                ? 'feature:gateway:nativepre:downgrade'
                : 'feature:ai:gateway:nativepre:downgrade',
              {
                gatewayId,
              },
            );
          }
          trackGatewayListClickEvent({ type: GATEWAY_LIST_CLICK_TYPE.downgrade });
          upgradeUrl && (upgradeUrl += `&regionId=${window.regionId}`);
          upgradeUrl && window.open(upgradeUrl, '_blank');
        },
      },
      {
        operateName: intl('apigw.components.gateway.GatewayListTable.TransferToSubscription'),
        isDisabled: status !== 'Running',
        visible: isShowBusinessLink && chargeType === 'POSTPAY',
        callback: () => {
          const convertUrl = CachedData.confLink(
            get(props, 'gatewayType', 'API') === 'API'
              ? 'feature:gateway:nativepost:convert'
              : 'feature:ai:gateway:nativepost:convert',
            {
              gatewayId,
            },
          );
          trackGatewayListClickEvent({ type: GATEWAY_LIST_CLICK_TYPE.toPrePay });
          convertUrl && window.open(convertUrl, '_blank');
        },
      },
      {
        operateName: intl('apigw.components.gateway.GatewayListTable.TransferToPayAsYou'),
        isDisabled: status !== 'Running',
        visible: isShowBusinessLink && chargeType === 'PREPAY',
        callback: () => {
          const convertUrl = CachedData.confLink(
            get(props, 'gatewayType', 'API') === 'API'
              ? 'feature:gateway:nativepre:convert'
              : 'feature:ai:gateway:nativepre:convert',
            {
              gatewayId,
            },
          );
          trackGatewayListClickEvent({ type: GATEWAY_LIST_CLICK_TYPE.toPostPay });
          convertUrl && window.open(convertUrl, '_blank');
        },
      },
      {
        operateName: intl('apigw.components.gateway.GatewayListTable.Renew'),
        isDisabled: status !== 'Running',
        visible: isShowBusinessLink && chargeType === 'PREPAY',
        callback: () => {
          const renewUrl = CachedData.confLink(
            get(props, 'gatewayType', 'API') === 'API'
              ? 'feature:gateway:nativepre:renew'
              : 'feature:ai:gateway:nativepre:renew',
            {
              gatewayId,
            },
          );
          trackGatewayListClickEvent({ type: GATEWAY_LIST_CLICK_TYPE.renew });
          renewUrl && window.open(renewUrl, '_blank');
        },
      },
      {
        operateName: intl('@ali/widget-edas-microgw::widget.gateway.action.clearn'),
        isDisabled: isGatewayNotRelease(status),
        visible: chargeType !== 'PREPAY',
        callback: () => {
          props.releaseInstance(record);
          trackGatewayListClickEvent({ type: GATEWAY_LIST_CLICK_TYPE.release });
        },
      },
      {
        operateName: intl('mse.set_default_alarm.title'),
        isDisabled: false,
        visible: true,
        callback: () => {
          props.handleDefaultAlertRule(record);
        },
      },
      {
        operateName: intl('@ali/widget-edas-microgw::widget.instance.expansion.title'),
        isDisabled: false,
        visible: props.gatewayType === 'API' && record.spec !== 'apigw.dev.x1',
        callback: () => {
          props.handleElasticExpansion(record);
        },
      },
      {
        operateName: record.wafEnable
          ? intl('@ali/widget-edas-microgw::widget.gateway.action.waf_close')
          : intl('apigw.components.gateway.GatewayListTable.EnableWafProtection'),
        isDisabled: false,
        visible: record.wafSupported,
        callback: () => {
          props.handleWafPolicy(record);
        },
      },
    ];

    const links = [];
    actionLinks.forEach((item) => {
      if (item.visible) {
        links.push(item);
      }
    });
    return links;
  };
  const onTagSelect = (t) => {
    const exist = find(tags, (item) => item.tagKey === t.tagKey);
    const newTags = exist
      ? map(tags, (item) => {
        if (item.tagKey === t.tagKey) {
          return { ...item, tagValue: t.tagValue };
        }
        return item;
      })
      : [...tags, t];
    handleChangeTag(newTags);
  };

  const handleChangeTag = (v) => {
    setTags(v);
    setRefreshIndex(Date.now());
  };

  const selection = () => {
    return (
      <>
        <BatchTagEditorAction
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          setRefreshIndex={setRefreshIndex}
        />

        <BatchUntagEditorAction
          setSelectedRowKeys={setSelectedRowKeys}
          selectedRowKeys={selectedRowKeys}
          setRefreshIndex={setRefreshIndex}
        />
      </>
    );
  };

  return (
    <CndTable
      fetchData={fetchData}
      primaryKey="gatewayId"
      selection={selection}
      columns={GatewayListColumns({ ...props, setRefreshIndex, history }) as any}
      // @ts-ignore
      loop={{ enable: isLoop, time: 5000, showLoading: false }}
      showRefreshButton
      refreshIndex={refreshIndex}
      rowSelection={{
        selectedRowKeys: map(selectedRowKeys, ({ gatewayId }) => gatewayId),
        onChange(selected, records) {
          setSelectedRowKeys(records);
        },
      }}
      search={
        GatewayListSearch({
          handleChangeTag,
          tags,
          onTagSelect,
        }) as any
      }
      emptyContent={
        resourceAuthStatus ? (
          <RegionGuidance
            currentRegion={window.regionId}
            onRegionClick={(regionId) => {
              changeRegion(regionId);
              if (isRegionGrayEnabled()) {
                changeQuery({ region: regionId }, () => {
                  const newParams = queryEncode(queryDecodeHash() as any); // key1=value1&key2=value2
                  const newHash = window.location.href.split('#')[1]?.split?.('?')?.[0]; //hash部分不包含参数
                  const newHref = isRegionGrayEnabled()
                    ? `${window.location.origin}/?region=${window.regionId}`
                    : `${window.location.origin}`;
                  window.location.href = `${newHref}#${newHash}?${newParams}`;
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                });
              } else {
                window.location.href = `${location.origin}/#/${regionId}/gateway`;
              }
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            }}
            regionList={regionList}
          >
            <span>{intl('apigw.components.gateway.GatewayListTable.ThereAreNoInstancesIn')}</span>
          </RegionGuidance>
        ) : (
          <BaseAuthMessage authType={AUTH_INTERFACE.GET_RESOURCE_OVERVIEW} type="text" />
        )
      }
      operation={
        <Button
          type="primary"
          onClick={() => {
            const createUrl = CachedData.confLink(
              props.gatewayType === 'API'
                ? 'feature:gateway:common:buy'
                : 'feature:ai:gateway:common:buy',
              {
                regionId: CachedData.getCurrentRegionId(),
              },
            );
            window.open(createUrl, '_blank');
          }}
        >
          {intl('@ali/widget-edas-microgw::widget.gateway.create')}
        </Button>
      }
      pagination={{
        pageSizeList: [10, 20, 50, 100],
        hideOnlyOnePage: false,
      }}
    />
  );
});
