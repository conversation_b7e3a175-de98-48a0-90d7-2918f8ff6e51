import React, { useEffect, useState, useMemo } from 'react';
import AppLayout from '~/containers/AppLayout';
import { useParams } from '@ali/xconsole/hooks';
import { intl, Tab, Truncate, useHistory, Button, Icon } from '@ali/cnd';
import services from '~/utils/services';
import { queryDecode } from '~/utils/queryString';
import { get, find, includes, isEmpty, first } from 'lodash';
import Detail from '~/components/api-manage/apiAi/detail';
import CreateOrUpdateApiSidePanel from '~/components/api-manage/createApi/create-actions/CreateOrUpdateApiSidePanel';
import ExtendBalloon from '~/components/shared/ExtendBalloon';
import DeleteApiAction from '~/components/api-manage/apiList/DeleteApiAction';
import { API_PUBLISH_STATUS } from '~/constants/apiManage';
import useApiRetry from '~/components/api-manage/apiList/useApiRetry';
import { CREATE_API_TYPE } from '~/components/api-manage/createApi';
import RouteList from './route/RouteList';
import ConsumerAuth from './consumer-auth';
import PubLishStatusText from '../../components/PublishStatus';
// import { useConsumerPolicyInfo } from '~/components/api-manage/createApi/create-actions/hook/usePolicyHook';
const Index = (props) => {
  const {
    match: {
      params: { id },
    },
    detailData: gatewayData,
  } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [httpApiInfo, setHttpApiInfo] = useState({});
  const [currentTab, setCurrentTab] = useState('');
  const [showEdit, setShowEdit] = useState(false);
  const history = useHistory();
  const { apiId } = useParams<any>();
  const query: any = queryDecode();
  const { tabKey } = query;

  const GetHttpApi = async () => {
    let data = {
      httpApiId: apiId,
    };
    try {
      const result = await services.GetHttpApi({
        params: { ...data },
      });
      // const environments = get(result, 'environments', []);
      const firstDeployConfig: any = first(get(result, 'deployConfigs') || []) || {};
      const _environment =
        find(get(result, 'environments', []), {
          environmentId: firstDeployConfig?.environmentId,
        }) || {};
      const retryValidated = retryValidate(_environment);
      setHttpApiInfo(result);
      if (retryValidated) {
        onRetry();
      } else {
        clearTime();
      }
    } catch (error) {
      setHttpApiInfo({});
    }
  };

  const deployStatus = useMemo(() => {
    const environmentId = get(httpApiInfo, 'deployConfigs[0].environmentId', '');
    const environments = get(httpApiInfo, 'environments', []);
    const _environment: any =
      find(environments, {
        environmentId,
      }) || {};
    const deployStatus = _environment?.deployStatus;
    return deployStatus;
  }, [JSON.stringify(httpApiInfo)]);

  const { clearTime, onRetry, retryValidate } = useApiRetry({
    refreshIndex,
    fetchData: GetHttpApi,
  });

  // const { consumerPolicy, refreshPolicy } = useConsumerPolicyInfo({
  //   attachResourceType: 'HttpApi',
  //   attachResourceId: apiId,
  //   gatewayId: get(gatewayData, 'gatewayId'),
  // });

  useEffect(() => {
    if (tabKey) {
      setCurrentTab(tabKey);
    } else {
      setCurrentTab('route');
    }
  }, [tabKey]);

  useEffect(() => {
    GetHttpApi();
  }, [apiId, refreshIndex]);

  const handleTabChange = (val) => {
    setCurrentTab(val);
    history.push(
      `/${window.regionId}/ai-gateway/${id}/agent-api/${apiId}?region=${window.regionId}&tabKey=${val}`,
    );
  };

  const initBreadcrumbs = useMemo(() => {
    return [
      {
        to: `/${window.regionId}/ai-gateway`,
        text: intl('apigw.components.overview.ResourceList.InstanceManagement'),
      },
      {
        to: `/${window.regionId}/ai-gateway/${id}/detail`,
        text: gatewayData?.Name,
      },
    ];
  }, []);

  return (
    <AppLayout
      breadcrumbs={[
        ...(initBreadcrumbs as any),
        {
          text: get(httpApiInfo, 'name') || '',
          to: null,
        },
      ]}
      hasBackArrow
      gatewayType={'AI'}
      onBackArrowClick={() =>
        history.push(`/${window.regionId}/ai-gateway/${id}/agent-api?region=${window.regionId}`)
      }
      title={
        <div className="align-center">
          <Truncate type="width" threshold={300} align="t" className="mr-8">
            {get(httpApiInfo, 'name') || ''}
          </Truncate>
          {!isEmpty(httpApiInfo) && (
            <PubLishStatusText
              deployStatus={deployStatus}
              deployErrorMessage={get(httpApiInfo, 'environments[0].deployErrorMessage')}
            />
          )}
        </div>
      }
      titleExtra={
        <div>
          <Button
            type="primary"
            className="mr-8"
            disabled={includes(
              [
                API_PUBLISH_STATUS.NOT_PUBLISHED,
                API_PUBLISH_STATUS.PUBLISHING,
                API_PUBLISH_STATUS.BEING_OFFLINE,
              ],

              deployStatus,
            )}
            onClick={() => {
              setShowEdit(true);
            }}
          >
            {API_PUBLISH_STATUS.FAILED === deployStatus
              ? intl('apigw.api-ai.$apiId.Publish')
              : intl('apigw.api-ai.$apiId.Edit')}
          </Button>
          <DeleteApiAction
            httpApiId={get(httpApiInfo, 'httpApiId')}
            apiType={CREATE_API_TYPE.Agent}
            setRefreshIndex={() => {
              history.push(
                `/${window.regionId}/ai-gateway/${id}/agent-api?region=${window.regionId}`,
              );
            }}
          >
            <Button
              type="primary"
              warning
              disabled={includes(
                [
                  API_PUBLISH_STATUS.NOT_PUBLISHED,
                  API_PUBLISH_STATUS.PUBLISHING,
                  API_PUBLISH_STATUS.BEING_OFFLINE,
                ],

                deployStatus,
              )}
            >
              {intl('apigw.api-manage.apiList.ApiListTableProps.Delete')}
            </Button>
          </DeleteApiAction>
        </div>
      }
    >
      <div id="ai-tab-content">
        <Tab
          size="small"
          shape="wrapped"
          activeKey={currentTab}
          onChange={handleTabChange}
          unmountInactiveTabs
          contentStyle={{
            paddingTop: 16,
          }}
        >
          <Tab.Item key={'route'} title={intl('apigw.components.api-manage.RouteList')}>
            <RouteList
              httpApiInfo={httpApiInfo}
              refreshIndex={refreshIndex}
              gatewayId={id}
              deployStatus={deployStatus}
              retryValidate={retryValidate}
            />
          </Tab.Item>
          <Tab.Item key={'detail'} title={intl('apigw.api-ai.$apiId.ApiDetails')}>
            <Detail
              httpApiInfo={httpApiInfo}
              setRefreshIndex={setRefreshIndex}
              aiScene={CREATE_API_TYPE.Agent}
            />
          </Tab.Item>

          <Tab.Item key={'consumer-auth'} title={intl('apigw.api-ai.apiId.ConsumerCertification')}>
            <ConsumerAuth httpApiInfo={httpApiInfo} setRefreshIndex={setRefreshIndex} />
          </Tab.Item>
        </Tab>
        {showEdit && (
          <CreateOrUpdateApiSidePanel
            {...{
              apiType: CREATE_API_TYPE.Agent,
              apiScene: CREATE_API_TYPE.Agent,
              type: 'edit',
              setRefreshIndex,
              environmentInfo: get(httpApiInfo, 'environments[0]'),
              apiId,
              setVisible: () => {
                setShowEdit(false);
              },
            }}
          />
        )}
      </div>
    </AppLayout>
  );
};

export default Index;
