import React from 'react';
import { intl, Truncate, Icon, Button, Dialog } from '@ali/cnd';
import { includes } from 'lodash';
import ExtendBalloon from '~/components/shared/ExtendBalloon';
import { API_PUBLISH_STATUS } from '~/constants/apiManage';
const PubLishStatusText = ({ deployStatus, deployErrorMessage }) => {
  const publishStatus = (status) => {
    let obj = {
      name: '',
      iconType: '',
      color: '#808080',
      bgColor: '#F6F6F6',
      tips: '',
    };
    switch (status) {
      case API_PUBLISH_STATUS.NOT_PUBLISHED:
        obj.name = intl('apigw.api-ai.$apiId.NotPublished');
        break;
      case API_PUBLISH_STATUS.PUBLISHING:
        obj.name = intl('apigw.api-ai.$apiId.Publishing');
        obj.iconType = 'loading';
        break;
      case API_PUBLISH_STATUS.PUBLISHED:
        obj.name = intl('apigw.api-ai.$apiId.Published');
        break;
      case API_PUBLISH_STATUS.PUBLISHED_WITH_CHANGES:
        obj.name = intl('apigw.api-ai.$apiId.PublishedButModified');
        break;
      case API_PUBLISH_STATUS.FAILED:
        obj.name = intl('apigw.api-ai.$apiId.PublishingFailed');
        obj.color = '#D50B16';
        obj.bgColor = '#FFEDEC';
        obj.tips = intl('apigw.api-ai.$apiId.AiApiPublishingFailedPlease');
        break;
      case API_PUBLISH_STATUS.BEING_OFFLINE:
        obj.name = intl('apigw.api-ai.$apiId.Offline');
        obj.iconType = 'loading';
        break;
      case API_PUBLISH_STATUS.OFFLINE_FAILED:
        obj.name = intl('apigw.api-ai.$apiId.OfflineFailed');
        obj.color = '#D50B16';
        obj.bgColor = '#FFEDEC';
        obj.tips = intl('apigw.api-ai.$apiId.AiApiFailedToBe');
        break;
      case API_PUBLISH_STATUS.NOT_DEPLOYED:
        //下线中未发布不显示状态
        break;
      default:
        obj.name = intl('apigw.api-ai.$apiId.EnvironmentException');
        obj.color = '#D50B16';
        obj.bgColor = '#FFEDEC';
        break;
    }
    return obj;
  };

  let statusObj = publishStatus(deployStatus);

  const style = {
    borderRadius: 2,
    color: statusObj.color,
    background: statusObj.bgColor,
    lineHeight: '20px',
    height: 20,
    padding: '0 8px',
    fontSize: 12,
    display: 'flex',
    alignItems: 'center',
  };

  const onShowErrorMessage = (errorMessage) => {
    Dialog.show({
      title: intl('apigw.api.ViewErrorDetails.title'),
      content: (
        <pre
          style={{
            whiteSpace: 'pre-wrap',
            minHeight: '50px',
            maxHeight: '300px',
            overflow: 'auto',
          }}
        >
          {errorMessage.replace(/\\n/g, '\n')}
        </pre>
      ),
      footerActions: [],
    });
  };

  if (!deployStatus) {
    return (
      <div style={style}>
        {statusObj.iconType && <Icon className="mr-2" size={12} type={statusObj.iconType}></Icon>}
        <Truncate showTooltip type="width" threshold={58}>
          {statusObj.name}
        </Truncate>
      </div>
    );
  }
  return includes(
    [
      API_PUBLISH_STATUS.PUBLISHING,
      API_PUBLISH_STATUS.FAILED,
      API_PUBLISH_STATUS.BEING_OFFLINE,
      API_PUBLISH_STATUS.OFFLINE_FAILED,
    ],

    deployStatus,
  ) ? (
    <>
      <ExtendBalloon
        trigger={
          <div style={style}>
            {statusObj.iconType && (
              <Icon className="mr-2" size={12} type={statusObj.iconType}></Icon>
            )}
            <Truncate showTooltip type="width" threshold={58}>
              {statusObj.name}
            </Truncate>
          </div>
        }
        isShow={includes(
          [API_PUBLISH_STATUS.FAILED, API_PUBLISH_STATUS.OFFLINE_FAILED],
          deployStatus,
        )}
      >
        <div>{statusObj.tips || ''}</div>
      </ExtendBalloon>
      {deployStatus === API_PUBLISH_STATUS.FAILED && deployErrorMessage && (
        <Button
          text
          className="ml-4"
          type="primary"
          onClick={() => onShowErrorMessage(deployErrorMessage)}
        >
          {intl('apigw.api.ViewErrorDetails')}
        </Button>
      )}
    </>
  ) : includes([API_PUBLISH_STATUS.NOT_DEPLOYED], deployStatus) ? (
    <Icon className="ml-2" size={12} type="loading"></Icon>
  ) : (
    <div></div>
  );
};

export default PubLishStatusText;
