import React, { useEffect, useRef, useState } from 'react';
import {
  Select,
  Input,
  Button,
  CndStatus,
  Icon,
  Message,
  intl,
  Tag,
  Notification,
  Loading,
} from '@ali/cnd';
import CodeMirrorEditor from '~/components/shared/CodeMirrorEditor';
import services from '~/utils/services';
import { fetchSse } from '@ali/xconsole-fetch-sse';
import PopSSE from '~/mcp-sdk/pop-sse';
// @ts-ignore
import { Client } from '@modelcontextprotocol/sdk/dist/esm/client/index';
import DynamicForm from './dynamic-form';
import { isEmpty, find, get, map, flatten } from 'lodash';
// @ts-ignore
// import { ListToolsResultSchema } from "@modelcontextprotocol/sdk/dist/esm/client/types.js";
// import { Client } from "@modelcontextprotocol/sdk/client/index.js";

const URL_OPTIONS = [
  {
    label: 'SSE',
    value: 'sse',
  },
];

interface MCPInspectorProps {
  authEnabled?: boolean;
  urls: [string, string];
  sseUrls: {
    url: string;
    networkType?: string;
  }[];
  serverId: string;
  envId: string;
  tools: any[];
  isMcpHosting: boolean;
}


function MCPInspector(props: MCPInspectorProps) {
  const { urls, serverId, envId, sseUrls, isMcpHosting } = props;
  const [url, setUrl] = useState(urls[0]);
  const [client, setClient] = useState<Client | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [connected, setConnected] = useState(false);
  const connectedRef = useRef(false);
  const [tools, setTools] = useState<any[]>([]);
  const [currentTool, setCurrentTool] = useState<any>(null);
  const [callToolResult, setCallToolResult] = useState<string>('');
  const [submitting, setSubmitting] = useState(false);
  const [reconnecting, setReconnecting] = useState(false);
  const [connectedFailed, setConnectedFailed] = useState(false);
  const connectedFailedRef = useRef(false);
  const [headerName, setHeaderName] = useState('');
  const [authEnabled, setAuthEnabled] = useState(props.authEnabled);
  const [authLoading, setAuthLoading] = useState(true);
  const [authList, setAuthList] = useState([]);
  const [authListLoading, setAuthListLoading] = useState(true);
  const [currentAuth, setCurrentAuth] = useState();
  const [tokens, setTokens] = useState([]);
  const [currentToken, setCurrentToken] = useState('');
  const canceledRef = useRef(false);
  const retryCountRef = useRef(0);
  const sessionIdRef = useRef('');
  const getConsumerAuthList = () => {
    setAuthListLoading(true);
    const promises = map(['MCP', 'MCPTool'], (type) => {
      return getAllConsumerAuthList(type);
    });
    Promise.all(promises)
      .then((res) => {
        const _res = flatten(res);
        setAuthList(_res);
        setAuthListLoading(false);
      })
      .catch(() => {
        setAuthList([]);
        setAuthListLoading(false);
      });
  };
  const getAllConsumerAuthList = async (type?: string) => {
    const res = await services.QueryConsumerAuthorizationRules({
      params: {
        pageNumber: 1,
        pageSize: 100,
        environmentId: envId,
        resourceTypes: type,
        resourceId: type === 'MCP' ? serverId : undefined,
        parentResourceId: type !== 'MCP' ? serverId : undefined,
      },
    });

    return (res?.items || []).map((v) => ({
      label: v?.consumerInfo?.name,
      value: v?.consumerInfo.consumerId,
    }))
  };

  const getConsumerTokens = async (currentAuth) => {
    if (!currentAuth) return;
    const res = await services.GetConsumer({
      params: {
        consumerId: currentAuth,
      },
    });
    setTokens(
      (res?.apiKeyIdentityConfigs || []).map((v) => ({ label: v.apikey, value: v.apikey })),
    );
  };

  const getAuthInfo = async () => {
    setAuthLoading(true);
    const { items = [] } = await services.ListAssociatePolicies({
      params: {
        withAttachments: true,
        attachResourceId: serverId,
        attachResourceType: 'GatewayRoute',
        environmentId: envId,
        pageSize: 100,
      },
    });
    const policyData = find(items, (item) => {
      return (
        item.className === 'Authentication' &&
        !isEmpty(find(item?.attachments, { attachResourceType: 'GatewayRoute' }))
      );
    });
    if (!isEmpty(policyData)) {
      const config = JSON.parse(get(policyData, 'config', ''));
      const enabled = get(config, 'enable', false);
      if (!enabled) {
        connect();
      } else {
        getConsumerAuthList();
      }
      setAuthEnabled(enabled);
    } else {
      connect();
      setAuthEnabled(false);
    }
    setAuthLoading(false);
  };

  const connect = async () => {
    try {
      canceledRef.current = false;

      setReconnecting(true);
      const _client = new Client(
        {
          name: "mcp-inspector",
          version: "1.0.0",
        },
        {
          capabilities: {
            sampling: {},
            roots: {
              listChanged: true,
            },
          },
        },
      );
      const popSSE = new PopSSE({
        url,
        headerName,
        bearerToken: currentToken,
        onError: (err) => {
          if (err.name === 'AbortError') {
            return;
          }
          setSubmitting(false);
          setReconnecting(false);
          setConnectedFailed(true);
          connectedFailedRef.current = true;
          disconnect();
        },
        onFinish: () => {
          if (canceledRef.current) {
            return;
          }
          disconnect();
          if (retryCountRef.current < 3) {
            connect();
            retryCountRef.current += 1;
            return;
          } else {
            setConnectedFailed(true);
            connectedFailedRef.current = true;
          }
        }
      });

      await _client.connect(popSSE);
      sessionIdRef.current = popSSE.getSessionId();

      setConnected(true);
      connectedRef.current = true;
      setReconnecting(false);
      setConnectedFailed(false);
      connectedFailedRef.current = false;

      _client.onerror = (message) => {
        setSubmitting(false);
        if (message.data === 'SSE connection not established') {
          disconnect();
          if (retryCountRef.current < 3) {
            connect();
            retryCountRef.current += 1;
          }
        }
      };

      _client.onclose = () => {
        setSubmitting(false);
        disconnect();
      };


      setClient(_client);
      listTools(_client);
    } catch (error) {
      console.log(error, 'asd...onerror by onfinish');
      // todo: to tip: the connect timeout
      setConnectedFailed(true);
      connectedFailedRef.current = true;
      setReconnecting(false);
      setConnected(false);
      connectedRef.current = false;
    }
  };

  const disconnect = () => {
    client?.close();
    // clearInterval(timerRef.current);
    setClient(null);
    setConnected(false);
    connectedRef.current = false;
    setReconnecting(false);
  };

  const cancelConnect = () => {
    canceledRef.current = true;
    disconnect();
  };

  const listTools = async (obj?) => {
    const res = obj ? await obj.listTools() : await client?.listTools();
    if (!isEmpty(res?.tools)) {
      Notification.open({
        content: intl('apigw.tools.get.success'),
        type: 'success',
        duration: 5000,
      });
    }
    setTools(res?.tools || []);
  };

  const handleAuthChoose = (v) => {
    setCurrentAuth(v);
  };

  useEffect(() => {
    if (currentAuth) {
      getConsumerTokens(currentAuth);
    }
  }, [currentAuth]);

  useEffect(() => {
    // connect();
    getAuthInfo();
    return () => {
      disconnect();
    };
  }, []);

  useEffect(() => {
    if (typeof props.authEnabled === 'boolean') {
      setAuthEnabled(props.authEnabled);
      if (props.authEnabled === true) {
        getConsumerAuthList();
      }
    }
  }, [props.authEnabled]);

  useEffect(() => {
    if (authEnabled === false) {
      setAuthList([]);
      setTokens([]);
      setCurrentAuth(null);
      setCurrentToken(null);
    }
  }, [authEnabled]);

  return (
    <div>
      <section style={{ padding: 16 }}>
        <h4>{intl('apigw.connection.message')}</h4>
        <div style={{ display: 'flex', gap: 16 }}>
          <div style={{ flex: 2 }}>
            <span>URL</span>
            <div style={{ display: 'flex' }}>
              <Select
                style={{ width: 150 }}
                defaultValue={URL_OPTIONS[0].value}
                dataSource={URL_OPTIONS}
                onChange={(v) => {
                  if (v === 'sse') {
                    setUrl(urls[1]);
                  } else {
                    setUrl(urls[0]);
                  }
                }}
              />
              <Select value={url} style={{ width: 400 }} onChange={setUrl}>
                {sseUrls.map((url) => {
                  return (
                    <Select.Option
                      style={{ display: 'flex', alignItems: 'center' }}
                      value={url.url}
                    >
                      {url.url}
                      {url.networkType === 'Internet' ? (
                        <Tag style={{ marginLeft: 4 }}>
                          <span
                            style={{ display: 'inline-flex', alignItems: 'center', height: '100%' }}
                          >
                            {intl('apigw.mcp-server.mcp-server-details.ForTestingOnly')}
                          </span>
                        </Tag>
                      ) : null}
                    </Select.Option>
                  );
                })}
              </Select>
            </div>
          </div>
          <div style={{ flex: 1, visibility: authEnabled ? 'visible' : 'hidden' }}>
            <span>{intl('apigw.src.sidebar.Consumers')}</span>
            <div style={{ display: 'flex', gap: 4 }}>
              <Loading style={{ width: '100%' }} visible={authListLoading}>
                <Select
                  value={currentAuth}
                  dataSource={authList}
                  onChange={handleAuthChoose}
                  style={{ width: '100%' }}
                />
              </Loading>
              <Button onClick={getConsumerAuthList}>
                <Icon type="refresh" />
              </Button>
            </div>
          </div>
          <div style={{ flex: 1, visibility: authEnabled ? 'visible' : 'hidden' }}>
            <span>{intl('apigw.api.key.auth')}</span>
            <div style={{ display: 'flex', gap: 4 }}>
              <Select
                value={currentToken}
                dataSource={tokens}
                onChange={setCurrentToken}
                style={{ width: '100%' }}
              />
              <Button onClick={() => getConsumerTokens(currentAuth)}>
                <Icon type="refresh" />
              </Button>
            </div>
          </div>
        </div>
        {props.tools.length > 0 || isMcpHosting ? (
          <div style={{ display: 'flex', gap: 16, alignItems: 'center', marginTop: 16 }}>
            {connected ? (
              <Button type="primary" onClick={disconnect}>
                {intl('apigw.disconnect')}
              </Button>
            ) : (
              <Button type="primary" loading={reconnecting} onClick={connect}>
                {reconnecting ? intl('apigw.connecting') : intl('apigw.connect')}
              </Button>
            )}
            {reconnecting && (
              <Button onClick={cancelConnect}>
                {intl('apigw.components.ExpansionPanel.Cancel')}
              </Button>
            )}
            {connected ? (
              <CndStatus type="success" label={intl('apigw.connected')} />
            ) : (
              <CndStatus type="wait" label={intl('apigw.disconnected')} />
            )}
            {connectedFailed && (
              <Message type="error">{intl('apigw.mcp.connection.failed')}</Message>
            )}
          </div>
        ) : (
          <Message style={{ marginTop: 8 }} type="warning">
            {intl('apigw.tools.empty')}
          </Message>
        )}
      </section>
      <section style={{ display: 'flex', minHeight: 400, borderTop: '1px solid #E5e5e5' }}>
        <div style={{ flex: 1, padding: 16, minWidth: '50%', maxWidth: '50%' }}>
          <h3>
            {intl('apigw.tools')}
            {tools.length ? `(${tools.length})` : ''}
          </h3>
          <div style={{ padding: '0 ' }}>
            <ul>
              {tools.map((tool) => (
                <li
                  style={{
                    display: 'flex',
                    borderBottom: '1px solid #E5e5e5',
                    padding: '8px 16px',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    setCurrentTool(tool);
                  }}
                >
                  <div>{tool.name}</div>
                  <div>{tool.description}</div>
                </li>
              ))}
            </ul>
            <Button
              style={{ marginTop: 16 }}
              disabled={!connected}
              onClick={() => {
                listTools();
              }}
            >
              {intl('apigw.tools.get')}
            </Button>
          </div>
        </div>
        <div
          style={{
            flex: 1,
            padding: 16,
            borderLeft: '1px solid #E5e5e5',
            minWidth: '50%',
            maxWidth: '50%',
          }}
        >
          {currentTool ? (
            <div>
              <h3>{currentTool.name}</h3>
              <p>{currentTool.description}</p>
              <DynamicForm
                disabled={!connected}
                schema={currentTool.inputSchema}
                submitting={submitting}
                onSubmit={async (values) => {
                  setSubmitting(true);
                  try {
                    const res = await client?.callTool({
                      name: currentTool.name,
                      arguments: values,
                    });
                    if (res?.isError === false) {
                      const cur = res.content?.[0];
                      if (cur && cur.type === 'text') {
                        setCallToolResult(cur.text);
                      }
                    } else {
                      Message.error(intl('apigw.call.tool.failed'));
                    }
                    setSubmitting(false);
                  } catch (error) {
                    setSubmitting(false);
                  }
                }}
              />
            </div>
          ) : (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                width: '100%',
              }}
            >
              <img
                style={{ width: '100%', maxWidth: 160 }}
                src="https://img.alicdn.com/imgextra/i1/O1CN01zBc8We1DSWAY2EmEH_!!6000000000215-55-tps-340-251.svg"
                alt=""
              />
              <div style={{ textAlign: 'center' }}>
                <div>{intl('apigw.please.choose.tool')}</div>
                <span>{intl('apigw.please.choose.tool.desc')}</span>
              </div>
            </div>
          )}
        </div>
      </section>
      <section style={{ padding: 16, borderTop: '1px solid #E5e5e5' }}>
        <h4>{intl('apigw.running.result')}</h4>
        <Loading style={{ width: "100%", height: "100%" }} visible={submitting}>
          <CodeMirrorEditor readOnly lineWrapping={false} value={callToolResult} />
        </Loading>
      </section>
    </div>
  );
}

export default MCPInspector;
