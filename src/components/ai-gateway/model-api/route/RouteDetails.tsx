import React from 'react';
import { intl, Tag, Truncate } from '@ali/cnd';
import BasicInfo from '~/components/shared/BasicInfo';
import { get, map, includes } from 'lodash';
import { modelNameEmpty, NA } from '~/constants';
import { formatDate } from '~/utils';
import RouteMatchRuleInfo from '~/components/api-manage/interfaceList/components/api-operations-slide/api-route-info/RouteMatchRuleInfo';
import { AI_API_BACKENDSCENE } from '~/constants/apiManage';
import Status from '~/components/shared/Status';
import ExternalLink from '~/components/shared/ExternalLink';
import { CREATE_API_TYPE } from '~/components/api-manage/createApi';
const { Group: TagGroup } = Tag;

const Index = (props) => {
  const { resourceData, gatewayId, isDefaultRoute, httpApiInfo } = props;
  const backendScene = get(resourceData, 'backend.scene');
  const serviceConfigs = isDefaultRoute
    ? get(httpApiInfo, 'deployConfigs[0].serviceConfigs', [])
    : get(resourceData, 'backend.services', []);
  const aiFallbackConfig = get(resourceData, 'aiFallbackConfig');

  return (
    <div>
      <BasicInfo
        textDescClass="line-height-24"
        title={intl('apigw.apiAi.detail.BasicInformation')}
        items={[
          {
            text: intl('widget.route.name'),
            value: (
              <Truncate
                type="width"
                style={{ width: '95%' }}
                threshold={'auto'}
                align="t"
                popupClassName="truncate-content-inline"
                popupStyle={{ wordBreak: 'break-all' }}
              >
                {get(resourceData, 'name', NA) || NA}
              </Truncate>
            ),
          },
          {
            text: intl('mse.common.create.time'),
            value: (
              <div className="align-center">
                {formatDate(get(resourceData, 'createTimestamp')) || NA}
              </div>
            ),
          },
          {
            text: intl('mse.microgw.create.router.desc.name'),
            value: (
              <Truncate
                type="width"
                threshold={'auto'}
                align="t"
                className="full-width"
                popupClassName="truncate-content-inline"
                popupStyle={{ wordBreak: 'break-all' }}
              >
                {get(resourceData, 'description', NA) || NA}
              </Truncate>
            ),
          },
        ]}
        sizePerRow={2}
      />

      <hr className="mt-24 mb-24" />

      <RouteMatchRuleInfo
        title={intl('apigw.components.api-operations-slide.ResourceDetails.MatchingRules')}
        curData={resourceData}
      />

      <hr className="mt-24 mb-24" />
      <BasicInfo
        title={intl('apigw.components.api-operations-policy.Backend')}
        items={[
          {
            text: intl('apigw.apiAi.detail.ServiceModel'),
            value: AI_API_BACKENDSCENE[backendScene]?.key || NA,
          },
          {
            text: 'Fallback',
            value: (
              <Status
                value={aiFallbackConfig?.enable || false}
                dataSource={[
                  {
                    label: intl('apigw.apiAi.detail.Enabled'),
                    value: true,
                    iconType: 'check_fill',
                    type: 'success',
                  },
                  {
                    label: intl('apigw.apiAi.detail.NotEnabled'),
                    value: false,
                    type: 'minus_fill',
                  },
                ]}
              />
            ),
            hidden: get(httpApiInfo, 'type') !== CREATE_API_TYPE.LLM,
          },
          {
            text: intl('apigw.apiAi.detail.ServiceList'),
            value: (
              <div>
                <TagGroup>
                  {map(serviceConfigs || [], (item, index) => {
                    return (
                      <Tag type="normal" key={index} style={{ margin: '2px' }}>
                        {(backendScene === AI_API_BACKENDSCENE?.SingleService?.value ||
                          backendScene === AI_API_BACKENDSCENE?.MultiServiceByRatio?.value) && (
                          <>
                            {item.name}
                            {backendScene === AI_API_BACKENDSCENE?.MultiServiceByRatio?.value && (
                              <span>
                                {intl('apigw.envAndBackendServices.backendServices.columns.Weight')}
                                {item.weight || 0}
                              </span>
                            )}
                            {!includes([CREATE_API_TYPE.Agent], get(httpApiInfo, 'type')) && (
                              <span className="color-gray">{` (${intl('apigw.components.apimanage.apiai.detail.modelName')}${
                                item.modelName || modelNameEmpty
                              })`}</span>
                            )}

                            <ExternalLink
                              label={''}
                              url={`/#/${window.regionId}/ai-gateway/${gatewayId}/service/detail?ServiceId=${item.serviceId}&subTitle=${item.name}`}
                            />
                          </>
                        )}

                        {backendScene === AI_API_BACKENDSCENE?.MultiServiceByModelName?.value && (
                          <>
                            {item.name}
                            <span className="color-gray">{` (${intl('apigw.components.apimanage.apiai.detail.rules')}${
                              item.modelNamePattern
                            })`}</span>
                            <ExternalLink
                              label={''}
                              url={`/#/${window.regionId}/ai-gateway/${gatewayId}/service/detail?ServiceId=${item.serviceId}&subTitle=${item.name}`}
                            />
                          </>
                        )}
                      </Tag>
                    );
                  })}
                </TagGroup>
              </div>
            ),
          },
          {
            text: intl('apigw.apiAi.detail.FallbackService'),
            hidden: !aiFallbackConfig?.enable,
            value: (
              <div>
                <TagGroup>
                  {map(aiFallbackConfig?.serviceConfigs || [], (item, index) => {
                    return (
                      <Tag type="normal" key={index} style={{ margin: '2px' }}>
                        <>
                          {item.name}
                          <span className="color-gray">{` (${intl('apigw.components.apimanage.apiai.detail.modelName')}${
                            item.passThroughModelName ? modelNameEmpty : item.targetModelName || NA
                          })`}</span>
                          <ExternalLink
                            label={''}
                            url={`/#/${window.regionId}/ai-gateway/${gatewayId}/service/detail?ServiceId=${item.serviceId}&subTitle=${item.name}`}
                          />
                        </>
                      </Tag>
                    );
                  })}
                  <span className="ml-4 color-gray">
                    {intl('apigw.apiAi.detail.FallbackDescendingExecution')}
                  </span>
                </TagGroup>
              </div>
            ),
          },
        ]}
        sizePerRow={2}
      />
    </div>
  );
};
export default Index;
