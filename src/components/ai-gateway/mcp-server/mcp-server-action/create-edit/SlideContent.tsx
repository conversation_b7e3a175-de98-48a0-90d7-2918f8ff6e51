import { intl, Switch } from '@ali/cnd';
import React, { forwardRef, useImperativeHandle, useEffect, useState } from 'react';
import { Field, Form, Input, Message, Radio } from '@ali/cnd';
import { map, get, isEmpty, isEqual, find, cloneDeep } from 'lodash';
import services from '~/utils/services';
import DomainListForm from '~/components/api-manage/createApi/create-actions/components/DomainListForm';
import BasicConsumerAuth from '~/components/policy/consumer/BasicConsumerAuth';
import Service from './backend-service/Service';
const FormItem = Form.Item;

type SlideContentProps = {
  curData: any;
  type: string;
  onRefresh?: any;
  gatewayInfo?: any;
  httpApiId: string;
  policyInfo?: any;
  envInfo: any;
  gatewayType: string;
};

const FORM_LAYOUT: any = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelTextAlign: 'left',
};

const FORM_LAYOUT_ITEM: any = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
  labelTextAlign: 'left',
};

const SlideContent = forwardRef((props: SlideContentProps, ref) => {
  const {
    curData,
    type = 'create',
    onRefresh,
    gatewayInfo,
    httpApiId,
    envInfo,
    gatewayType,
  } = props;
  const [policyInfo, setPolicyInfo] = useState({});
  const [httpRewritePolicyInfo, setHttpRewritePolicyInfo] = useState({});
  const field = Field.useField({
    onChange(name, value) {
      if (name === 'name') {
        setValue('match.path.value', `/mcp-servers/${value}`);
      }
    },
    parseName: true,
    values: {
      match: {
        path: {
          type: 'Prefix',
        },
      },
      backendConfig: {
        scene: 'SingleService',
      },
      useScene: 'httpToMcp',
      protocol: 'HTTP',
      mcpStatisticsEnable: true,
    },
  });
  const { init, validate, setValue, getValue, setValues } = field;

  useEffect(() => {
    if (!isEmpty(curData)) {
      const {
        name,
        description,
        domainInfos,
        backend: backendConfig,
        match,
        mcpServerInfo,
      } = cloneDeep(curData);
      const mcpRouteConfig = get(mcpServerInfo, 'mcpRouteConfig', {});
      const transport =
        get(mcpRouteConfig, 'protocol') !== 'HTTP' ? get(mcpRouteConfig, 'protocol') : undefined;
      setValues({
        name,
        description: description.trim(),
        customDomainIds: map(domainInfos, (item) => get(item, 'domainId')),
        backendConfig,
        match,
        ...{
          transport: transport,
          protocol: get(mcpRouteConfig, 'protocol', 'HTTP') === 'HTTP' ? 'HTTP' : 'MCP',
          mcpStatisticsEnable: get(mcpRouteConfig, 'mcpStatisticsEnable', false),
          useScene:
            get(mcpRouteConfig, 'protocol', 'HTTP') === 'HTTP' ? 'httpToMcp' : 'mcpDirectProxy',
          exposedUriPath: get(mcpRouteConfig, 'exposedUriPath'),
        },
      });
      getConsumerAuthConfig();
    }
  }, [JSON.stringify(curData)]);

  const getConsumerAuthConfig = async () => {
    const res = await services.ListPolicies({
      params: {
        withAttachments: true,
        attachResourceType: 'GatewayRoute',
        attachResourceId: get(curData, 'routeId'),
        gatewayId: get(gatewayInfo, 'gatewayId'),
      },
    });
    const authInfo = find(get(res, 'items', []), {
      className: 'Authentication',
    });

    const httpRewriteInfo = find(get(res, 'items', []), {
      className: 'HttpRewrite',
    });
    setPolicyInfo(authInfo);
    setHttpRewritePolicyInfo(httpRewriteInfo);

    try {
      const authConfig = JSON.parse(get(authInfo, 'config', '{}'));
      setValues({
        authenticationType: get(authConfig, 'authenticationType'),
        enable: get(authConfig, 'enable'),
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  const submit = () => {
    return new Promise<void>((resolve, reject) => {
      validate(async (errors, values: any) => {
        if (errors) {
          reject();
          return;
        }
        const {
          enable,
          authenticationType,
          customDomainIds = [],
          protocol,
          mcpStatisticsEnable,
          transport,
          exposedUriPath,
          host,
          useScene,
          ...rest
        } = values;
        const data = {
          ...rest,
          domainIds: customDomainIds,
          backendConfig: {
            ...get(values, 'backendConfig', {}),
            services: map(get(values, 'backendConfig.services', []), (item) => ({
              serviceId: get(item, 'serviceId'),
            })),
          },
          mcpRouteConfig: {
            protocol: protocol === 'HTTP' ? protocol : transport,
            ...(protocol === 'HTTP'
              ? {}
              : {
                  exposedUriPath,
                }),
            mcpStatisticsEnable: useScene !== 'mcpDirectProxy' ? mcpStatisticsEnable : undefined,
          },
        };

        try {
          // const envInfo = await getEnvironmentInfo();
          const routeInfo =
            type === 'create'
              ? await services.CreateHttpApiRoute({
                  params: {
                    httpApiId: httpApiId,
                  },
                  content: {
                    ...data,
                    environmentId: get(envInfo, 'environmentId'),
                  },
                  customErrorHandle: (err, data, callback) => {
                    callback();
                    throw new Error(err);
                  },
                })
              : await services.UpdateHttpApiRoute({
                  params: {
                    httpApiId: httpApiId,
                    routeId: get(curData, 'routeId'),
                  },
                  content: {
                    ...data,
                    environmentId: get(envInfo, 'environmentId'),
                  },
                  customErrorHandle: (err, data, callback) => {
                    callback();
                    throw new Error(err);
                  },
                });

          if (get(routeInfo, 'routeId')) {
            enable &&
              (await handlePolicy(
                get(routeInfo, 'routeId'),
                { enable, authenticationType },
                get(envInfo, 'environmentId'),
              ));
            protocol === 'MCP' &&
              (await handleHttpRewritePolicy(
                get(routeInfo, 'routeId'),
                {
                  enable: true,
                  pathType: 'Regex',
                  pattern: `/mcp-servers/${get(values, 'name')}(/|$)(.*)`,
                  substitution: '/\\2',
                  ...(host ? { host: host } : {}),
                },
                get(envInfo, 'environmentId'),
              ));
          } else {
            const { enable: orgEnable, authenticationType: orgAuthenticationType } = JSON.parse(
              get(policyInfo, 'config', '{}'),
            );
            !isEqual(
              { enable, authenticationType },
              { enable: orgEnable, authenticationType: orgAuthenticationType },
            ) &&
              (await handlePolicy(
                get(curData, 'routeId'),
                {
                  enable,
                  authenticationType: !enable ? orgAuthenticationType : authenticationType,
                },
                get(envInfo, 'environmentId'),
              ));
            get(curData, 'deployConfigs[0].services[0].serviceId') !==
              get(values, 'backendConfig.services[0].serviceId') &&
              protocol === 'MCP' &&
              (await handleHttpRewritePolicy(
                get(curData, 'routeId'),
                {
                  enable: true,
                  pathType: 'Regex',
                  pattern: `/mcp-servers/${get(values, 'name')}(/|$)(.*)`,
                  substitution: '/\\2',
                  ...(host ? { host: host } : {}),
                },
                get(envInfo, 'environmentId'),
              ));
          }
          const deployResponse = await services.DeployHttpApi({
            params: {
              httpApiId: httpApiId,
            },
            content: {
              routeId: get(routeInfo, 'routeId', get(curData, 'routeId')),
            },
            customErrorHandle: (err, data, callback) => {
              callback();
              throw new Error(err);
            },
          });

          if (deployResponse?.code === 'Ok') {
            Message.success(
              <span className="router-submit-success">
                {intl('gateway.env.manage.publish.success')}
              </span>,
            );
          }
          resolve();
          onRefresh();
        } catch (error) {
          reject();
        }
      });
    });
  };
  const handlePolicy = async (routeId, config, environmentId) => {
    const content = {
      attachResourceType: 'GatewayRoute',
      gatewayId: get(gatewayInfo, 'gatewayId'),
      config: JSON.stringify(config),
      className: 'Authentication',
      attachResourceIds: [routeId],
      environmentId,
    };
    try {
      const policyRes = isEmpty(policyInfo)
        ? await services.CreateAndAttachPolicy({ content })
        : await services.UpdateAndAttachPolicy({
            params: {
              policyId: get(policyInfo, 'policyId'),
            },
            content: content,
          });

    } catch (error) { }
  };

  const handleHttpRewritePolicy = async (routeId, config, environmentId) => {
    const content = {
      attachResourceType: 'GatewayRoute',
      gatewayId: get(gatewayInfo, 'gatewayId'),
      config: JSON.stringify(config),
      className: 'HttpRewrite',
      attachResourceIds: [routeId],
      environmentId,
    };
    try {
      const httpRewritePolicyRes = isEmpty(httpRewritePolicyInfo)
        ? await services.CreateAndAttachPolicy({ content })
        : await services.UpdateAndAttachPolicy({
            params: {
              policyId: get(httpRewritePolicyInfo, 'policyId'),
            },
            content: content,
          });

      console.log('httpRewritePolicyRes', httpRewritePolicyRes);
    } catch (error) {}
  };

  useImperativeHandle(ref, () => ({ submit }));

  return (
    <Form field={field} {...FORM_LAYOUT}>
      <FormItem
        label={intl('apigw.mcp-server-action.create-edit.SlideContent.Name')}
        required
        help={intl('apigw.mcp-server-action.create-edit.SlideContent.TheNameIsUniqueAnd')}
      >
        <Input
          disabled={type === 'edit'}
          {...init('name', {
            rules: [
              {
                required: true,
                message: intl('apigw.mcp-server-action.create-edit.SlideContent.TheNameOfTheMcp'),
              },
              {
                pattern: /^(?=.{1,64}$)[a-z0-9]+(?:[.-][a-z0-9]+)*$/,
                message: intl('apigw.mcp-server-action.create-edit.SlideContent.TheNameOfTheMcp.1'),
              },
            ],
          })}
          placeholder={intl('apigw.mcp-server-action.create-edit.SlideContent.EnterAServiceName')}
          trim
        />
      </FormItem>
      <FormItem label={intl('apigw.mcp-server-action.create-edit.SlideContent.UseScene')} required>
        <Radio.Group
          {...init('useScene', {
            props: {
              onChange: (value) => {
                setValue('protocol', value === 'httpToMcp' ? 'HTTP' : 'MCP');
                if (value === 'mcpDirectProxy') {
                  setValue(`transport`, 'SSE');
                }
              },
            },
          })}
          disabled={type === 'edit'}
          dataSource={[
            {
              label: intl('apigw.mcp-server-action.create-edit.SlideContent.HttpToMcp'),
              value: 'httpToMcp',
            },
            {
              label: intl('apigw.mcp-server-action.create-edit.SlideContent.McpToMcp'),
              value: 'mcpDirectProxy',
            },
          ]}
        />
      </FormItem>
      <FormItem
        label={intl('apigw.headBtn.publish.BackendServices')}
        labelTextAlign="left"
        required
      >
        <Service gatewayInfo={gatewayInfo} field={field} type={type} />
      </FormItem>

      {getValue('protocol') === 'HTTP' && (
        <FormItem
          label={intl('apigw.components.CreateConsumerSlide.SlideContent.Description')}
          required
        >
          <Input.TextArea
            {...init('description', {
              rules: [
                {
                  required: true,
                  message: intl(
                    'apigw.mcp-server-action.create-edit.SlideContent.TheDescriptionCannotBeEmpty',
                  ),
                },
              ],
            })}
            placeholder={intl(
              'apigw.components.CreateConsumerSlide.SlideContent.EnterADescription',
            )}
            maxLength={255}
            showLimitHint
          />
        </FormItem>
      )}
      <FormItem
        label={
          <div className="mt-18" style={{ position: 'relative' }}>
            <span className="color-error" style={{ position: 'absolute', left: '-10px' }}>
              *
            </span>
            {intl('apigw.mcp-server-action.create-edit.SlideContent.McpAccessPoint')}
          </div>
        }
      >
        <Form
          field={field}
          {...FORM_LAYOUT_ITEM}
          style={{ background: '#F6F6F6', padding: '12px 16px' }}
        >
          <FormItem
            label={intl('apigw.mcp-server-action.create-edit.SlideContent.DomainName')}
            required
          >
            <DomainListForm
              field={field}
              type={type}
              httpApiInfo={curData}
              gatewayType={gatewayType}
            />
          </FormItem>

          <FormItem
            label={intl('apigw.mcp-server-action.create-edit.SlideContent.PathSse')}
            hidden={!(getValue('protocol') === 'HTTP' || getValue('transport') === 'SSE')}
          >
            <Input
              value={
                getValue('name')
                  ? getValue('protocol') === 'HTTP'
                    ? `/mcp-servers/${getValue('name')}/sse`
                    : `/mcp-servers/${getValue('name')}${getValue('exposedUriPath') || ''}`
                  : ''
              }
              disabled
              style={{ width: '100%' }}
              placeholder={intl(
                'apigw.mcp-server-action.create-edit.SlideContent.PathSseRuleMcpServers',
              )}
            />
          </FormItem>

          <FormItem
            label={intl('apigw.mcp-server-action.create-edit.SlideContent.PathStreamableHttp')}
            style={{ margin: '0' }}
            hidden={!(getValue('protocol') === 'MCP' && getValue('transport') === 'StreamableHTTP')}
          >
            <Input
              value={
                getValue('name')
                  ? `/mcp-servers/${getValue('name')}${getValue('exposedUriPath') || ''}`
                  : ''
              }
              disabled
              style={{ width: '100%' }}
              placeholder={intl(
                'apigw.mcp-server-action.create-edit.SlideContent.ThePathStreamableHttpRule',
              )}
            />
          </FormItem>

          <FormItem
            label={intl('apigw.mcp-server-action.create-edit.SlideContent.PathStreamableHttp')}
            style={{ margin: '0' }}
            hidden={getValue('protocol') === 'MCP'}
          >
            <Input
              {...init('match.path.value')}
              disabled
              style={{ width: '100%' }}
              placeholder={intl(
                'apigw.mcp-server-action.create-edit.SlideContent.ThePathStreamableHttpRule',
              )}
            />
          </FormItem>
        </Form>
      </FormItem>

      <BasicConsumerAuth
        field={field}
        authTypes={['Apikey']}
        enableLabel={intl('apigw.mcp-server-action.create-edit.SlideContent.ConsumerCertification')}
        authTypeLabel={intl(
          'apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.AuthenticationMethod',
        )}
      />
      {getValue('useScene') !== 'mcpDirectProxy' && (
        <FormItem label={intl('apigw.mcp-server-action.create-edit.SlideContent.McpMonitor')}>
          <div className="flex">
            <FormItem>
              <Switch style={{ marginTop: 6 }} name="mcpStatisticsEnable" />
            </FormItem>
            <span className="ml-8 mb-16 mt-8 flex-1">
              {intl('apigw.mcp-server-action.create-edit.SlideContent.McpMonitor.Tip')}
            </span>
          </div>
        </FormItem>
      )}
    </Form>
  );
});

export default SlideContent;
