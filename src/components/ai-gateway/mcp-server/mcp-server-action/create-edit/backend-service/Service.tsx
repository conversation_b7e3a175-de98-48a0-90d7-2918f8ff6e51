import React from 'react';
import { intl, Select, Form, Grid, Input } from '@ali/cnd';
import { get } from 'lodash';
import ServiceSelect from '~/components/api-manage/createApi/create-actions/components/ServiceSelect';
import { CREATE_API_TYPE } from '~/components/api-manage/createApi';

const { Row, Col } = Grid;
const FormItem = Form.Item;

const Service = ({ gatewayInfo, field, type }) => {
  const { init, getValue, setValue } = field;
  return (
    <Form labelTextAlign="left" field={field}>
      <Row
        wrap
        style={
          getValue('protocol') === 'MCP' ? { background: '#F6F6F6', padding: '12px 16px' } : {}
        }
      >
        <Col key={'serviceId'} style={{ padding: '4px 4px' }} span={12}>
          <FormItem
            label={intl('apigw.publish.components.serviceTableProps.ServiceName')}
            style={{ marginBottom: 0 }}
          >
            <ServiceSelect
              {...(init(`backendConfig.services[0].serviceId`, {
                rules: [
                  {
                    required: true,
                    message: intl('apigw.ai.mcp.service.select.required'),
                  },
                ],
                props: {
                  onChange: (value, _, item) => {
                    if (item.sourceType === 'DNS') {
                      setValue('host', get(item, 'addresses[0]', '')?.split(':')[0]);
                    } else {
                      setValue('host', '');
                    }
                  },
                },
              }) as any)}
              dataItem={getValue(`backendConfig.services[0].serviceId`)}
              environmentInfo={{ gatewayInfo: gatewayInfo }}
              type="services"
              backendScene="SingleService"
              aiScene={CREATE_API_TYPE.LLM}
            />
          </FormItem>
        </Col>

        <Col key={'protocol'} style={{ padding: '4px 4px' }} span={12}>
          <FormItem
            label={intl('apigw.components.TraceConfig.ServiceAgreement')}
            style={{ marginBottom: 0 }}
          >
            <Select
              {...init(`protocol`, {
                props: {
                  onChange: (value) => {
                    if (value === 'MCP') {
                      setValue(`transport`, 'SSE');
                    }
                  },
                },
              })}
              disabled={true}
              className="full-width"
              dataSource={[
                { label: 'HTTP', value: 'HTTP' },
                { label: 'MCP', value: 'MCP' },
              ]}
            />
          </FormItem>
        </Col>
        {getValue('protocol') === 'MCP' && (
          <Col key={'transport'} style={{ padding: '4px 4px' }} span={12}>
            <FormItem label={'MCP Transport'} style={{ marginBottom: 0 }}>
              <Select
                {...init(`transport`)}
                className="full-width"
                disabled={type === 'edit'}
                dataSource={[
                  { label: 'SSE', value: 'SSE' },
                  { label: 'Streamable HTTP', value: 'StreamableHTTP' },
                ]}
              />
            </FormItem>
          </Col>
        )}
        {getValue('protocol') === 'MCP' && (
          <Col key={'McpPath'} style={{ padding: '4px 4px' }} span={12}>
            <FormItem
              label={intl('apigw.api-manage.interfaceList.ApiOperationsSearch.Path')}
              style={{ marginBottom: 0 }}
            >
              <Input {...init(`exposedUriPath`)} className="full-width" trim />
            </FormItem>
          </Col>
        )}
      </Row>
    </Form>
  );
};

export default Service;
