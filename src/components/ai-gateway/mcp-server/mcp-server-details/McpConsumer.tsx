import { intl, Loading, Tab } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { get, find, isEmpty } from 'lodash';
import ApiConsumerTable from '~/components/consumer-manage/consumer-auth/api-consumer-auth/ApiConsumerTable';
import ConsumerAuthConfig from '~/components/consumer-manage/consumer-auth/api-consumer-auth/ConsumerAuthConfig';
import services from '~/utils/services';

const McpConsumer = (props) => {
  const {
    serverId,
    envId,
    apiId,
    gatewayInfo,
    refreshIndex: refreshKey,
    onEnabled,
    mcpTools,
  } = props;
  const [loading, setLoading] = useState(false);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [authPolicy, setAuthPolicy] = useState({});
  useEffect(() => {
    getListPolicies();
  }, [refreshIndex, refreshKey]);

  const getListPolicies = async () => {
    setLoading(true);
    const { items = [] } = await services.ListAssociatePolicies({
      params: {
        withAttachments: true,
        attachResourceId: serverId,
        attachResourceType: 'GatewayRoute',
        environmentId: envId,
        pageSize: 100,
      },
    });
    const policyData = find(items, (item) => {
      return (
        item.className === 'Authentication' &&
        !isEmpty(find(item?.attachments, { attachResourceType: 'GatewayRoute' }))
      );
    });
    if (!isEmpty(policyData)) {
      const config = JSON.parse(get(policyData, 'config', ''));
      setAuthPolicy({
        authConfig: {
          authType: get(config, 'authenticationType'),
        },
        enableAuth: get(config, 'enable'),
        policyId: get(policyData, 'policyId'),
      });
    } else {
      setAuthPolicy({});
    }
    setLoading(false);
  };

  return (
    <div>
      <Loading visible={loading} className="full-width">
        <ConsumerAuthConfig
          onEnabled={onEnabled}
          curData={{ resourceId: serverId, apiId, ...authPolicy }}
          onRefresh={(resourceId) => {
            setRefreshIndex(refreshIndex + 1);
          }}
          attachResourceType={'MCP'}
          gatewayId={get(gatewayInfo, 'gatewayId')}
          environmentId={envId}
          apiType="MCP"
        />
      </Loading>

      <hr className="mt-16 mb-16" />
      <h2 className="f-w-500 fz-14 mb-14">
        {intl('apigw.components.api-operations-consumer.Consumers')}
      </h2>
      <Tab size="medium" unmountInactiveTabs contentClassName={'mt-16'}>
        <Tab.Item key={'MCP'} title={'MCP'}>
          <ApiConsumerTable
            currentGatewayInfo={gatewayInfo}
            currentEnvId={envId}
            attachResourceType={'MCP'}
            attachResourceId={serverId}
            parentResourceId={apiId}
            apiType="MCP"
            mcpTools={mcpTools}
          />
        </Tab.Item>
        <Tab.Item
          key={'MCPTool'}
          title={intl('apigw.mcp-server.mcp-server-details.McpConsumer.McpTools')}
        >
          <ApiConsumerTable
            currentGatewayInfo={gatewayInfo}
            currentEnvId={envId}
            attachResourceType={'MCPTool'}
            attachResourceId={serverId}
            parentResourceId={apiId}
            apiType="MCP"
            mcpTools={mcpTools}
          />
        </Tab.Item>
      </Tab>
    </div>
  );
};
export default McpConsumer;
