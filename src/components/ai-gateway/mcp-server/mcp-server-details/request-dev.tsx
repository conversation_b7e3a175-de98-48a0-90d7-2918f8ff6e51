import React, { useState, useRef, useEffect } from 'react';
import { intl, Card, Button, useParams } from '@ali/cnd';
import Debug from '~/components/api-manage/router/debug';
import CodeMirrorEditor, { MergeEditor } from '~/components/shared/CodeMirrorEditor';
import { routerInfoTransformer } from '~/pages/$regionId/gateway/utils/transformer';
import { CREATE_API_TYPE } from '~/components/api-manage/createApi';
import { isEmpty, concat, last, slice, size, get } from 'lodash';
import { ContentTypeKey } from '~/components/api-manage/interfaceList/components/api-operations-debug/RequestParamsTab';
import { CircleIcon } from './circleIcon';
interface RequestDevProps {
  mcpServerInfo: object;
  isCreateing: boolean;
  defaultValue: string;
  onMergeChange: (newValue: string) => void;
}

function RequestDev(props: RequestDevProps) {
  const { mcpServerInfo = {}, isCreateing, defaultValue, onMergeChange }: any = props;
  const debugRefs = useRef<any>(null);
  const abortRef = useRef({ isAbort: false });
  const [requestLoading, setRequestLoading] = useState(false);
  const [debugLoading, setdebugLoading] = useState(true);
  const [request, updateRequest] = useState<any>({});
  const [response, updateResponse] = useState<any>({});
  const [GenerateMCPToolsLoading, setGenerateMCPToolsLoading] = useState(false);
  const [dialogs, setDialogs] = useState([]);
  const [mcpToolsDefinition, setMcpToolsDefinition] = useState('');
  const { apiId, id: gatewayId } = useParams<any>();

  const onSendRequest = () => {
    debugRefs.current.routeDebug();
  };

  const toJsonParse = (jsonStr) => {
    let parsed;
    try {
      return (parsed = JSON.parse(jsonStr));
    } catch (e) {
      return {}; // 如果解析失败，则返回原始字符串
    }
  };

  useEffect(() => {
    let { assistantContent = '{}' } = last(dialogs) || {};
    setMcpToolsDefinition(get(toJsonParse(assistantContent), 'choices[0].delta.content', ''));
  }, [JSON.stringify(dialogs)]);

  const objectToQueryParamsString = (obj) => {
    const entries = Object.entries(obj);
    const lines = entries.map(([key, value]) => `- ${key}: ${value}`);
    return lines;
  };

  const _request = () => {
    let headers = objectToQueryParamsString(JSON.parse(request.Header || '{}'));
    let queryParams = objectToQueryParamsString(JSON.parse(request?.Query || '{}'));
    const contentTypeMapper = {
      FORM: ContentTypeKey.form,
      JSON: ContentTypeKey.json,
      MULTIPART: ContentTypeKey.multipart,
    };
    return `
<request>
<path>
${request?.Path || ''}
</path>

<headers>
${headers.join('\n')}
</headers>

<queryParams>
${queryParams.join('\n')}
</queryParams>

<body>
  <contentType>${contentTypeMapper[request.BodyType] || ''}</contentType>
  <content>
  {
      "attachResourceIds": [
          ${mcpServerInfo?.routeId}
      ],
      "enable": true
  }
  </content>
</body>
</request>`;
  };

  const _response = `
      <response>{${JSON.stringify(response?.data)}}</response>
  `;
  const onGenerateMCPTools = async () => {
    setDialogs([]);
    setGenerateMCPToolsLoading(true);
    const requestParams = {
      agentName: 'mcpYamlGen',
      incrementalOutput: true,
      bizParams: {
        request: _request(),
        response: _response,
      },
    };
    await streamRequest(requestParams);
  };
  const streamRequest = (requestParams: any) => {
    return fetch('/tool/sse/get.json', {
      method: 'POST',
      mode: 'same-origin',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        product: 'APIG',
        action: 'InvokeAIAgent',
        region: window.regionId,
        content: JSON.stringify(requestParams),
        sec_token: window.ALIYUN_CONSOLE_CONFIG.SEC_TOKEN,
      }),
    })
      .then(function (res) {
        return res.json();
      })
      .then(async function (result) {
        const headers = result.data.headers;
        const raw =
          headers['Content-Type'] == 'application/json'
            ? result.data.body
            : headers['Content-Type'] == 'application/x-www-form-urlencoded'
              ? new URLSearchParams(JSON.parse(result.data.body))
              : null;
        const myHeaders = new Headers();
        Object.entries(headers).forEach((d) => myHeaders.append(d[0], d[1] as string));
        const aiResponse = (await fetch(result.data.uri.replace(/\/$/, '') + result.data.path, {
          method: 'POST',
          headers: myHeaders,
          body: raw,
          redirect: 'follow',
        })) as any;
        console.log('stream requestId ------ ', aiResponse?.headers?.get('x-acs-request-id'));
        const reader = aiResponse.body.getReader();
        if (abortRef?.current?.isAbort === true) {
          try {
            await reader.cancel(); // 尝试取消读取
          } catch (error) {
            console.error('Error cancelling stream:', error);
          }
        }
        let trunk: any;

        while (!abortRef?.current?.isAbort && !(trunk = await reader.read()).done) {
          const decoder = new TextDecoder();
          // console.log('===', decoder.decode(trunk.value));
          const chunk = decoder.decode(trunk.value);
          // 将 chunk 按行分割，因为每一行可能是一个独立的事件
          const lines = chunk.split('\n');
          for (const line of lines) {
            if (line.startsWith('data:') && !line.startsWith('data:[DONE]')) {
              try {
                const data = JSON.parse(line.substring(5));
                const body = data.body;
                if (body) {
                  const decodedBody = body;
                  try {
                    let _decodedBody = JSON.parse(decodedBody);
                    let responseContent = _decodedBody?.choices?.[0]?.delta?.content || '';
                    //@ts-ignore
                    setDialogs((pre) => {
                      let _last: any = last(pre);
                      let _decodedBodyLast = JSON.parse(_last.assistantContent);
                      let lastContent = _decodedBodyLast?.choices?.[0]?.delta?.content || '';
                      if (_decodedBody.choices[0])
                        _decodedBody.choices[0].delta.content = lastContent + responseContent;
                      else
                        _decodedBody.choices[0] = {
                          delta: {
                            content: lastContent + responseContent,
                          },
                        };
                      return concat(slice(pre, 0, size(pre) - 1), {
                        content: lastContent,
                        stream: true,
                        assistantContent: JSON.stringify(_decodedBody),
                      });
                    });
                  } catch (error) {
                    //服务错误时
                    //@ts-ignore
                    setDialogs((pre) => {
                      return [
                        ...pre,
                        {
                          content: decodedBody,
                          stream: true,
                          assistantContent: decodedBody,
                        },
                      ];
                    });
                  }
                }
              } catch (error) {
                console.error('Error parsing data:', error);
                //@ts-ignore
                setDialogs((pre) => {
                  return [
                    ...pre,
                    {
                      content: '',
                      stream: true,
                      assistantContent: '',
                    },
                  ];
                });
              }
            }
          }
        }
        setGenerateMCPToolsLoading(false);
      });
  };

  return (
    <div>
      <Card contentHeight="100%">
        <div style={{ display: 'flex', gap: 16 }}>
          <div style={{ width: '50%' }}>
            <Debug
              aiScene={CREATE_API_TYPE.LLM}
              isShowRight={false}
              gatewayId={gatewayId}
              apiId={apiId}
              routerInfo={routerInfoTransformer(mcpServerInfo)}
              ref={debugRefs}
              setDebugLoading={(value) => setdebugLoading(value)}
              sendValueToFather={setRequestLoading}
              updateRequest={updateRequest}
              updateResponse={updateResponse}
            />
          </div>
          <div style={{ width: '50%' }}>
            <CodeMirrorEditor
              language={'json'}
              lineWrapping
              isLint={false}
              height="500px"
              readOnly
              value={JSON.stringify(response, null, 2)}
            />
          </div>
        </div>
        <Button onClick={onSendRequest} loading={requestLoading} disabled={debugLoading}>
          {intl('apigw.components.api-operations-debug.OperationDebug.SendRequest')}
        </Button>
      </Card>
      <Button
        className="mt-16"
        type="primary"
        disabled={isEmpty(response)}
        onClick={onGenerateMCPTools}
        loading={GenerateMCPToolsLoading}
      >
        {intl('apigw.mcp-server.mcp-server-details.request-dev.GenerateAnMppToolBased')}
      </Button>
      <div style={{ marginTop: 12 }}>
        {!isCreateing ? (
          <div>
            <div style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 8 }}>
              <CircleIcon />
              {intl('apigw.mcp-server.mcp-server-details.request-dev.StepUpdateAndConfirmThe')}
            </div>
            <div style={{ padding: 12 }}>
              <div style={{ display: 'flex', gap: 12, marginBottom: 8 }}>
                <div style={{ flex: 1 }}>
                  {intl('apigw.mcp-server.mcp-server-details.request-dev.ListOfCurrentMcpsTools')}
                </div>
                <div style={{ flex: 1 }}>
                  {intl('apigw.mcp-server.mcp-server-details.request-dev.TheFinalListOfMcp')}
                </div>
              </div>
              <MergeEditor
                originalValue={defaultValue}
                changedValue={mcpToolsDefinition || defaultValue}
                onChange={(newValue) => {
                  setMcpToolsDefinition(newValue);
                  onMergeChange(newValue);
                }}
              />
            </div>
          </div>
        ) : (
          mcpToolsDefinition && (
            <div>
              <div style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 8 }}>
                <CircleIcon />
                {intl('apigw.mcp-server.mcp-server-details.request-dev.StepUpdateAndConfirmThe')}
              </div>
              <div style={{ padding: 12 }}>
                <div style={{ display: 'flex', gap: 12, marginBottom: 8 }}>
                  <div style={{ flex: 1 }}>
                    {intl('apigw.mcp-server.mcp-server-details.request-dev.ListOfCurrentMcpsTools')}
                  </div>
                  <div style={{ flex: 1 }}>
                    {intl('apigw.mcp-server.mcp-server-details.request-dev.TheFinalListOfMcp')}
                  </div>
                </div>
                <MergeEditor
                  theme="light"
                  originalValue={defaultValue}
                  changedValue={mcpToolsDefinition || defaultValue}
                  onChange={(newValue) => {
                    setMcpToolsDefinition(newValue);
                    onMergeChange(newValue);
                  }}
                />
              </div>
            </div>
          )
        )}
      </div>
    </div>
  );
}

export default RequestDev;
