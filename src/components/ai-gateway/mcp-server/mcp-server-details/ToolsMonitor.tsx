import React, { useState, useEffect } from 'react';
import { intl, Button, Icon } from '@ali/cnd';
import Statistics from './Statistics';

const ToolsMonitor = (props: any) => {
  const { apiId, gatewayId, serverId, toolName, mcpServerInfo = {}, onClose } = props;

  return (
    <div className="mcp-tools-monitor">
      <div className="monitor-title">
        <div>
          <span className="mr-24">
            {`${intl('@ali/widget-edas-microgw::widget.gateway.action.monitor')}`}
          </span>
        </div>
        <div>
          <Button
            text
            onClick={() => {
              onClose && onClose();
            }}
          >
            <Icon type="close" />
          </Button>
        </div>
      </div>
      <Statistics
        {...{ apiId, gatewayId, serverId, mcpServerInfo }}
        toolName={toolName}
        typeMonitor={'toolMonitor'}
      />
    </div>
  );
};
export default ToolsMonitor;
