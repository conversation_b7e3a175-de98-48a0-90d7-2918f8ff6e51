import React, { useState, useEffect } from 'react';
import Iframe from '~/components/shared/Iframe';
import services from '~/utils/services/microgw_widget';
import { intl, Loading, Button, Icon } from '@ali/cnd';
import NotPermission from '~/components/shared/NotPermission';
import { SlsLogsConfigDialog } from '~/components/shared/SlsLog/LogsConfigDialog';
import { SLS_LOG_RESOURCE_TYPE } from '~/components/shared/SlsLog';

const Statistics = ({
  apiId,
  gatewayId,
  serverId,
  mcpServerInfo = {},
  toolName,
  typeMonitor,
}: any) => {
  const [dashboardUrl, setDashboardUrl] = useState('');
  const [loading, setLoading] = useState(true);
  const [slsEnableLoading, setSlsEnableLoading] = useState(false);
  const [slsEnable, setSlsEnable] = useState(false);
  const [logEnable, setLogEnable] = useState(false);
  const [logOpenLoading, setLogOpenLoading] = useState(false);
  const [openLoading, setOpenLoading] = useState(false);
  const [openLogVisible, setOpenLogVisible] = useState(false);

  useEffect(() => {
    // handleSearchSlsOpen();
    getDashBoardUrl();
  }, []);

  // const handleSearchSlsOpen = async (entry?) => {
  //   if (!entry) {
  //     setLoading(true);
  //   }
  //   setSlsEnableLoading(true);
  //   const res = await services.getIsOpensls({
  //     customErrorHandle: (err, data, callback) => {
  //       setSlsEnableLoading(false);
  //       if (!entry) {
  //         setLoading(false);
  //       }
  //       callback();
  //     },
  //   });
  //   const { Enabled } = res;
  //   setSlsEnable(Enabled);
  //   setSlsEnableLoading(false);
  //   if (Enabled) {
  //     getDashBoardUrl();
  //   } else {
  //     if (!entry) {
  //       setLoading(false);
  //     }
  //   }
  // };

  // const handleOpenSls = async () => {
  //   setOpenLoading(true);
  //   await services.updateOpensls({
  //     customErrorHandle: (err, data, callback) => {
  //       setOpenLoading(false);
  //     },
  //   });
  //   setOpenLoading(false);
  //   setSlsEnable(true);
  //   getDashBoardUrl();
  // };

  const getDashBoardUrl = async (entry?) => {
    setLogOpenLoading(true);
    if (!entry) setLoading(true);
    const res = await services.getGatewayDashboard({
      params: {
        name: 'MCP_SERVER',
        source: 'SLS',
        filter: {
          routeName: mcpServerInfo.name,
        },
        gatewayId: gatewayId,
        apiId: apiId,
        routeId: serverId,
      },
      customErrorHandle: (err, data, callback) => {
        if (
          err?.code === 'CloudProductInactive.LogDeliveryNotEnabled' ||
          err?.data?.code === 'CloudProductInactive.LogDeliveryNotEnabled'
        ) {
          setLogEnable(false);
          return;
        }
        callback();
      },
    });

    if (res?.url) {
      setLogEnable(true);
      let url = res.url;
      if (typeMonitor === 'toolMonitor') {
        url = `${res.url}&ai_log.mcp_tool_name=${toolName}`;
      }
      setDashboardUrl(url);
    }
    setLogOpenLoading(false);
    setLoading(false);
  };

  const onLoad = () => {};

  return (
    <Loading visible={loading} style={{ width: '100%', height: '100%', position: 'relative' }}>
      {!logEnable && (
        <NotPermission
          title={intl('apigw.apiAi.statistics.YouHaveNotEnabledGateway')}
          desc={
            <div style={{ color: '#333333' }}>
              <p>{intl('mse.microgw.plugin.log_post.open.tip1')}</p>
              <p>{intl('apigw.detail.components.RouteLogs.NoteThatLogDeliveryIs')}</p>
            </div>
          }
          btn={
            <div>
              <Button
                type="primary"
                loading={openLoading}
                onClick={() => {
                  setOpenLogVisible(true);
                }}
              >
                {intl('mse.microgw.plugin.log_post.open')}
              </Button>
              <Button
                style={{ marginLeft: 8 }}
                onClick={() => (logOpenLoading ? null : getDashBoardUrl('refresh'))}
              >
                {!logOpenLoading && <Icon type="refresh" />}
                {logOpenLoading && <Icon type="loading" />}
              </Button>
            </div>
          }
        />
      )}

      {logEnable && dashboardUrl && (
        <Iframe
          params={dashboardUrl}
          styles={{
            width: '100%',
            border: 'none',
            height: typeMonitor === 'toolMonitor' ? 'calc(70vh - 54px)' : window.innerHeight - 290,
          }}
          onLoad={onLoad}
        />
      )}

      {openLogVisible && (
        <SlsLogsConfigDialog
          GatewayUniqueId={gatewayId}
          onClose={() => setOpenLogVisible(false)}
          onRefreshCheck={getDashBoardUrl}
          logsConfig={{}}
          resourceType={SLS_LOG_RESOURCE_TYPE.ROUTE}
        />
      )}
    </Loading>
  );
};
export default Statistics;
