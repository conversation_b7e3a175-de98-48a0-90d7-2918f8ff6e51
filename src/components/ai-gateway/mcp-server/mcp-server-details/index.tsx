import React, { useEffect, useMemo, useRef, useState } from 'react';
import AppLayout from '~/containers/AppLayout';
import jsyaml from 'js-yaml';
import {
  intl,
  Truncate,
  Collapse,
  Form,
  Input,
  Tab,
  Button,
  SlidePanel,
  Radio,
  Balloon,
  Message,
  Select,
  Icon,
  Tag,
  Loading,
  DataFields,
  LinkButton,
} from '@ali/cnd';
import copy from 'copy-to-clipboard';
import services from '~/utils/services';
import { get, isEmpty, includes, find, first, map } from 'lodash';
import { McpServerStatus } from '../mcp-server-list/McpServerCard';
import { base64Decode, base64EncodeUnicode } from '~/utils';
import Swagger from './Swagger';
import DeleteRouter from '../mcp-server-action/delete';
import { API_PUBLISH_STATUS } from '~/constants/apiManage';
import OfflineRouter from '../mcp-server-action/offline';
import CreateOrEditMcpServer from '../mcp-server-action/create-edit';
import PublishRouter from '../mcp-server-action/publish';
import { CircleIcon } from './circleIcon';
import McpConsumer from './McpConsumer';
import { validateAuth } from './auth';
import CustomYaml from './custom-yaml';
import MCPInspector from '../../mcp-inspector';
import { mcpServerCreateFromTypeMap } from '../common';
import BasicInfo from './BasicInfo';
import Logs from './Logs';
import AiPlugin from '~/components/api-manage/apiAi/policiesPlugins/plugin';
import Statistics from './Statistics';
import ToolsMonitor from './ToolsMonitor';
import './index.less';

const McpServerDetails = (props) => {
  const {
    match: {
      params: { id },
    },
    history,
    detailData: gatewayData,
    envInfo,
  } = props;
  const pathname = location.hash.split('?')[0];
  const apiId = pathname?.split('/')[5];
  const routeId = pathname?.split('/')[6];
  const [mcpServerInfo, setMcpServerInfo] = useState<{
    domainInfos: any[];
    name: string;
    mcpServerInfo?: {
      mcpServerConfig?: string;
      importInstanceId?: string;
      importNamespace?: string;
      importMcpServerId?: string;
      createFromType?: string;
    };
  }>({ domainInfos: [], name: '' });
  const [activeTab, setActiveTab] = useState('tools');
  const [loading, setLoading] = useState(true);
  const [mcpAuthLoading, setMcpAuthLoading] = useState(true);
  const [headerConfig, setHeaderConfig] = useState({});

  const [editToolsVisible, setEditToolsVisible] = useState(false);
  const [currentPlugin, setCurrentPlugin] = useState({
    pluginId: '',
    pluginClassId: '',
    pluginAttachmentId: '',
    configExample: '',
  });
  const [mcpTools, setMcpTools] = useState([]);
  const [pluginConfig, setPluginConfig] = useState();
  const [defaultMcpToolsYAML, setDefaultMcpToolsYAML] = useState('');

  const [domainList, setDomainList] = useState([]);
  const [currentTab, setCurrentTab] = useState('SSE');
  const [mcpPluginConfig, setMCPPluginConfig] = useState({ pluginAttachmentId: '' });
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [authEnabled, setAuthEnabled] = useState(null);
  const [nacosInstanceDetail, setNacosInstanceDetail] = useState<any>(null);
  const [toolMonitorVisible, setToolMonitorVisible] = useState(false);
  const [toolName, setToolName] = useState('');

  const isNacosMcpServer = get(mcpServerInfo, 'mcpServerInfo.createFromType', '').includes('Nacos');
  const importNamepsace = get(mcpServerInfo, 'mcpServerInfo.importNamespace', '');

  const isMcpHosting = includes(
    ['NacosMcpHosting', 'ApiGatewayMcpHosting'],
    get(mcpServerInfo, 'mcpServerInfo.createFromType', ''),
  );
  const isApiGatewayMcpHosting =
    get(mcpServerInfo, 'mcpServerInfo.createFromType', '') === 'ApiGatewayMcpHosting';

  let timerRef = useRef(null);
  const retryCountRef = useRef(0);

  const pubDomain = domainList.find((item) => item.networkType === 'Internet');
  const privateDomain = domainList.find((item) => item.networkType === 'Intranet');

  const selectedDomainList = useMemo(
    () =>
      [pubDomain, privateDomain]
        .concat(
          get(mcpServerInfo, 'domainInfos', []).map((v) => ({
            ...v,
            name: v?.protocol ? `${v?.protocol?.toLowerCase()}://${v.name}` : v.name,
          })),
        )
        .filter(Boolean),
    [pubDomain, privateDomain, mcpServerInfo],
  );

  const [currentDomain, setCurrentDomain] = useState(selectedDomainList[0]?.name || '');

  const exposedUriPath = isMcpHosting
    ? get(mcpServerInfo, 'mcpServerInfo.mcpRouteConfig.exposedUriPath')
    : '';

  const gatewayInfo = useMemo(() => {
    const firstDeployConfig: any = first(get(mcpServerInfo, 'deployConfigs') || []) || {};
    const _environment: any =
      find([get(mcpServerInfo, 'environmentInfo', {})], {
        environmentId: firstDeployConfig.environmentId,
      }) || {};
    return get(_environment, 'gatewayInfo');
  }, [JSON.stringify(mcpServerInfo)]);

  useEffect(() => {
    if (selectedDomainList) {
      setCurrentDomain(selectedDomainList[0]?.name);
    }
  }, [selectedDomainList]);

  useEffect(() => {
    apiId && routeId && getMcpServerDetails();
  }, [apiId, routeId]);

  const getMcpServerDetails = async (isInit = true) => {
    setLoading(true);
    retryCountRef.current += 1;
    const data = await services.getRouterInfo({
      params: { httpApiId: apiId, routerId: routeId },
    });
    if (retryCountRef.current > 5) {
      clearInterval(timerRef.current);
      return;
    }
    if (
      includes([API_PUBLISH_STATUS.PUBLISHING, API_PUBLISH_STATUS.BEING_OFFLINE], data.deployStatus)
    ) {
      if (isInit) {
        getMcpServerDetailsByInterval();
      }
    } else {
      clearInterval(timerRef.current);
    }
    setDomainList(
      data?.environmentInfo?.subDomains?.map((v) => ({
        ...v,
        name: v?.protocol ? `${v?.protocol?.toLowerCase()}://${v.name}` : v.name,
      })) || [],
    );

    includes(['NacosMcpHosting', 'ApiGatewayMcpHosting'], data.mcpServerInfo?.createFromType) &&
      getListPolicies(gatewayData.gatewayId, get(data, 'routeId'));
    if (!data.mcpServerInfo?.createFromType?.includes('Nacos')) {
      getListPlugins(gatewayData.gatewayId);
    } else {
      const pluginConfigYAML = data.mcpServerInfo?.mcpServerConfig;
      const pluginConfigJSON = jsyaml.load(pluginConfigYAML);
      setPluginConfig(pluginConfigJSON);
      const _tools = map(get(pluginConfigJSON, 'tools', []), (item) => ({
        label: item.name,
        value: item.name,
        ...item,
      }));
      setMcpTools(_tools || []);
      setDefaultMcpToolsYAML(pluginConfigYAML);
      setLoading(false);
    }
    setMcpServerInfo(data);
  };

  const getMcpServerDetailsByInterval = () => {
    timerRef.current = setInterval(() => {
      getMcpServerDetails(false);
    }, 1000);
  };

  const getNacosInstanceDetail = () => {
    services
      .QueryClusterDetailForMSE({
        params: {
          InstanceId: mcpServerInfo?.mcpServerInfo.importInstanceId,
          AclSwitch: false,
        },
      })
      .then((res) => {
        setNacosInstanceDetail(res);
      });
  };

  useEffect(() => {
    if (refreshIndex > 0) {
      getMcpServerDetails();
      // getListPlugins(gatewayData.gatewayId);
      // getEnv(gatewayData.gatewayId);
    }
  }, [refreshIndex]);

  const getListPlugins = async (gatewayId) => {
    setLoading(true);
    const data = await services.ListPluginClasses({
      params: {
        pageSize: 100,
        pageNumber: 1,
        gatewayId,
        nameLike: 'mcp-server',
        source: 'HigressOfficial',
        gatewayType: 'AI',
      },
    });
    const items = data.items || [];
    if (items.length > 0) {
      const plugin = items[0];
      setCurrentPlugin(plugin);
      const pluginData = await services.ListPluginAttachments({
        params: {
          pageNumber: 1,
          pageSize: 10,
          pluginId: plugin.pluginId,
          attachResourceTypes: 'Operation,GatewayRoute',
          attachResourceId: routeId,
        },
      });
      try {
        const filteredPlugins = pluginData.items.filter(
          (item) => item.attachResourceId === routeId,
        );
        const mcpPluginConfig = filteredPlugins[0];
        if (!mcpPluginConfig) {
          setLoading(false);
          return;
        }
        setMCPPluginConfig(mcpPluginConfig);
        const pluginConfigYAML = base64Decode(mcpPluginConfig.pluginConfig);
        const pluginConfigJSON = jsyaml.load(pluginConfigYAML);
        setPluginConfig(pluginConfigJSON);
        const _tools = map(get(pluginConfigJSON, 'tools', []), (item) => ({
          label: item.name,
          value: item.name,
          ...item,
        }));
        setMcpTools(_tools || []);
        setDefaultMcpToolsYAML(pluginConfigYAML);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        Message.error(error.message);
      }
    } else {
      setLoading(false);
    }
  };
  const getListPolicies = async (gatewayId, currentResourceId) => {
    setMcpAuthLoading(true);
    const policies = await services.ListPolicies({
      params: {
        withAttachments: true,
        attachResourceType: 'GatewayRoute',
        attachResourceId: currentResourceId,
        gatewayId,
      },
    });
    const { items = [] } = policies;
    const HeaderModifyPolicy = find(items, { className: 'HeaderModify' });
    let config = {} as any;
    try {
      config = JSON.parse(get(HeaderModifyPolicy, 'config', '{}'));
      if (get(HeaderModifyPolicy, 'policyId')) {
        config.policyId = get(HeaderModifyPolicy, 'policyId');
      }
    } catch (error) {}
    setHeaderConfig(config);
    setMcpAuthLoading(false);
  };

  const generateMCPURLConfig = (
    domain: string,
    pubDomain: string,
    serverName: string,
    type = 'sse',
  ) => {
    if (!serverName) return {};
    return {
      mcpServers: {
        [serverName]: {
          ...(type === 'sse' ? { type: 'sse' } : {}),
          url: `${domain === '*' ? pubDomain : domain}/mcp-servers/${serverName}${exposedUriPath}${!isMcpHosting && type === 'sse' ? '/sse' : ''}`,
        },
      },
    };
  };

  const generateMCPURL = (
    domain: string,
    pubDomain: string,
    serverName: string,
    exposedUriPath = '',
    type = 'sse',
  ) => {
    return `${domain === '*' ? pubDomain : domain}/mcp-servers/${serverName}${exposedUriPath}${
      !isMcpHosting && type === 'sse' ? '/sse' : ''
    }`;
  };

  const onRefresh = () => setRefreshIndex(refreshIndex + 1);

  useEffect(() => {
    if (isNacosMcpServer) {
      if (importNamepsace) {
        getNacosInstanceDetail();
      } else {
        Message.error(intl('apigw.data.maybe.deleted'));
      }
    }
  }, [isNacosMcpServer, importNamepsace]);

  const mcpServerCreateFromTypeList =
    mcpServerCreateFromTypeMap.get(mcpServerInfo?.mcpServerInfo?.createFromType) || [];

  const streamableHttpUrl = useMemo(() => {
    return generateMCPURL(
      currentDomain,
      pubDomain?.name,
      get(mcpServerInfo, 'name'),
      exposedUriPath,
      '',
    );
  }, [currentDomain, pubDomain?.name, mcpServerInfo.name]);

  const sseUrl = useMemo(() => {
    return generateMCPURL(
      currentDomain,
      pubDomain?.name,
      get(mcpServerInfo, 'name'),
      exposedUriPath,
      'sse',
    );
  }, [currentDomain, pubDomain?.name, mcpServerInfo.name]);

  const sseUrls = useMemo(() => {
    return selectedDomainList.map((v) => {
      return {
        url: generateMCPURL(
          v.name,
          pubDomain?.name,
          get(mcpServerInfo, 'name'),
          exposedUriPath,
          'sse',
        ),
        networkType: v.networkType,
      };
    });
  }, [selectedDomainList, pubDomain?.name, mcpServerInfo.name]);

  useEffect(() => {
    return () => {
      clearInterval(timerRef.current);
    };
  }, []);

  const handleChange = (key) => {
    setToolMonitorVisible(false);
    setActiveTab(key as string);
  };

  const protocol = isMcpHosting
    ? get(mcpServerInfo, 'mcpServerInfo.mcpRouteConfig.protocol', '')
    : '';

  return (
    <AppLayout
      gatewayType="AI"
      breadcrumbs={[
        {
          to: `/${window.regionId}/ai-gateway`,
          text: intl('apigw.components.overview.ResourceList.InstanceManagement'),
        },
        {
          to: `/${window.regionId}/ai-gateway/${id}/detail`,
          text: gatewayData?.Name,
        },
        {
          text: get(mcpServerInfo, 'name') || '',
        },
      ]}
      hasBackArrow
      onBackArrowClick={() =>
        history.push(`/${window.regionId}/ai-gateway/${id}/mcp-server?region=${window.regionId}`)
      }
      titleExtra={
        isNacosMcpServer || loading ? null : (
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <CreateOrEditMcpServer
              type="edit"
              httpApiId={apiId}
              curData={mcpServerInfo}
              envInfo={envInfo}
              gatewayType={get(gatewayData, 'gatewayType')}
              gatewayInfo={{
                engineVersion: get(gatewayData, 'version'),
                gatewayId: get(gatewayData, 'gatewayId'),
                gatewayStatus: get(gatewayData, 'status'),
                name: get(gatewayData, 'name'),
                vpcInfo: get(gatewayData, 'vpc'),
              }}
              onRefresh={onRefresh}
              trigger={
                <Button type="primary">{intl('apigw.mcp-server.mcp-server-details.Edit')}</Button>
              }
            />

            {includes(
              [
                API_PUBLISH_STATUS.PUBLISHED,
                API_PUBLISH_STATUS.PUBLISHED_WITH_CHANGES,
                API_PUBLISH_STATUS.OFFLINE_FAILED,
              ],

              get(mcpServerInfo, 'deployStatus'),
            ) && (
              <OfflineRouter
                routeInfo={mcpServerInfo}
                httpApiId={apiId}
                onRefresh={onRefresh}
                text={false}
              />
            )}
            {includes(
              [
                API_PUBLISH_STATUS.NOT_PUBLISHED,
                API_PUBLISH_STATUS.FAILED,
                API_PUBLISH_STATUS.NOT_DEPLOYED,
              ].filter(Boolean),

              get(mcpServerInfo, 'deployStatus'),
            ) && (
              <PublishRouter
                routeInfo={mcpServerInfo}
                httpApiId={apiId}
                onRefresh={onRefresh}
                text={false}
              />
            )}
            {includes(
              [
                API_PUBLISH_STATUS.NOT_PUBLISHED,
                API_PUBLISH_STATUS.NOT_DEPLOYED,
                API_PUBLISH_STATUS.FAILED,
              ],

              get(mcpServerInfo, 'deployStatus'),
            ) && (
              <DeleteRouter
                routeInfo={mcpServerInfo}
                httpApiId={apiId}
                onRefresh={() => {
                  history.push(`/${window.regionId}/ai-gateway/${id}/mcp-server`);
                }}
                text={false}
                disabled={false}
              />
            )}
          </div>
        )
      }
      title={
        <div className="align-center mcp-detail-title">
          <Truncate type="width" threshold={300} align="t" className="mr-8">
            {get(mcpServerInfo, 'name') || ''}
          </Truncate>
          <Tag.Group>
            {mcpServerCreateFromTypeList.map((item) => (
              // @ts-ignore
              <Tag.Colored key={item.label} type={item.type}>
                {item.label}
                {/* @ts-ignore */}
              </Tag.Colored>
            ))}
          </Tag.Group>
          {isNacosMcpServer &&
          get(mcpServerInfo, 'deployStatus') === API_PUBLISH_STATUS.NOT_PUBLISHED ? null : (
            <div className="fz-12 f-w-400 align-center" style={{ height: 18, lineHeight: '100%' }}>
              {!isEmpty(mcpServerInfo) && (
                <McpServerStatus value={get(mcpServerInfo, 'deployStatus')} />
              )}
            </div>
          )}
        </div>
      }
    >
      <div className="color-gray pl-16 pr-16">{get(mcpServerInfo, 'description')}</div>

      <div
        style={{
          marginTop: 16,
          width: '100%',
          height: '100%',
          background: '#Fff',
          borderRadius: 4,
          minHeight: 500,
        }}
        id={'mcp-server-details-container'}
      >
        <Tab activeKey={activeTab} onChange={handleChange}>
          <Tab.Item title={intl('gateway.domain.info.slide.title.basic')} key="tools">
            <div style={{ display: 'flex', justifyContent: 'space-between', padding: '16px 0' }}>
              <div style={{ flex: 2 }}>
                {isMcpHosting && (
                  <div style={{ marginBottom: 14, border: '1px solid #E5e5e5', padding: '16px' }}>
                    <h2 className="f-w-500 fz-14 mb-8">{intl('mse.common.backend.service')}</h2>
                    <BasicInfo
                      mcpServerInfo={mcpServerInfo}
                      mcpAuthLoading={mcpAuthLoading}
                      headerConfig={headerConfig}
                      onRefresh={() => {
                        getListPolicies(gatewayData.gatewayId, get(mcpServerInfo, 'routeId'));
                      }}
                    />
                  </div>
                )}
                {!isApiGatewayMcpHosting && (
                  <div style={{ padding: '16px', border: '1px solid #E5e5e5' }}>
                    <h2 style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <div>
                        {intl('apigw.mcp-server.mcp-server-details.Tools')} ({mcpTools.length})
                      </div>
                      {isNacosMcpServer && nacosInstanceDetail ? (
                        <div>
                          <Button
                            size="small"
                            type="primary"
                            onClick={() => {
                              const detail = nacosInstanceDetail;
                              const prefix =
                                get(window, 'ALIYUN_CONSOLE_CONFIG.fEnv') === 'pre' ? 'pre-' : '';
                              const id = mcpServerInfo?.mcpServerInfo?.importMcpServerId;
                              window.open(
                                id
                                  ? `https://${prefix}mse.console.aliyun.com/#/Instance/MCP/detail?ClusterId=${detail.ClusterId}&ClusterName=${detail.ClusterName}&ClusterType=${detail.ClusterType}&InstanceId=${detail.InstanceId}&VersionCode=${detail.ClusterVersion}&AppVersion=${detail.AppVersion}&MseVersion=${detail.MseVersion}&ChargeType=${detail.ChargeType}&region=${window.regionId}&namespaceId=${mcpServerInfo.mcpServerInfo?.importNamespace}&id=${mcpServerInfo?.mcpServerInfo?.importMcpServerId}`
                                  : `https://${prefix}mse.console.aliyun.com/#/Instance/MCP/servers?ClusterId=${detail.ClusterId}&ClusterName=${detail.ClusterName}&ClusterType=${detail.ClusterType}&InstanceId=${detail.InstanceId}&VersionCode=${detail.ClusterVersion}&AppVersion=${detail.AppVersion}&MseVersion=${detail.MseVersion}&ChargeType=${detail.ChargeType}&region=${window.regionId}&namespaceId=${mcpServerInfo.mcpServerInfo?.importNamespace}`,
                              );
                            }}
                          >
                            {intl('apigw.go.to.nacos.edit')}
                          </Button>
                          <Button style={{ marginLeft: 8 }} size="small" onClick={onRefresh}>
                            <Icon type="refresh" />
                          </Button>
                        </div>
                      ) : (
                        <div>
                          {mcpTools.length > 0 && (
                            <Button
                              size="small"
                              type="primary"
                              onClick={() => setEditToolsVisible(true)}
                            >
                              {intl('apigw.mcp-server.mcp-server-details.EditingTools')}
                            </Button>
                          )}
                          <Button style={{ marginLeft: 8 }} size="small" onClick={onRefresh}>
                            <Icon type="refresh" />
                          </Button>
                        </div>
                      )}
                    </h2>
                    <div>
                      <Collapse>
                        {loading ? (
                          <Loading
                            style={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 16,
                              justifyContent: 'center',
                              alignItems: 'center',
                              height: '400px',
                            }}
                          />
                        ) : mcpTools.length === 0 ? (
                          <div
                            style={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 16,
                              justifyContent: 'center',
                              alignItems: 'center',
                              height: '400px',
                            }}
                          >
                            <div
                              style={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: 8,
                              }}
                            >
                              <img
                                width={140}
                                src="https://img.alicdn.com/imgextra/i2/O1CN01zBc8We1DSWAY2EmEH_!!6000000000215-55-tps-340-251.svg"
                                alt=""
                              />

                              <span style={{ color: '#aaa' }}>
                                {intl(
                                  'apigw.mcp-server.mcp-server-details.CurrentlyNoToolsAreAvailable',
                                )}
                              </span>
                            </div>
                            {!isNacosMcpServer && (
                              <Button type="primary" onClick={() => setEditToolsVisible(true)}>
                                <Icon type="plus" />
                                {intl('apigw.mcp-server.mcp-server-details.AddTools')}
                              </Button>
                            )}
                          </div>
                        ) : (
                          mcpTools.map((item) => (
                            <Collapse.Panel
                              title={
                                <div>
                                  {item.name}
                                  <LinkButton
                                    className="ml-16"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setToolName(item.name);
                                      setToolMonitorVisible(true);
                                    }}
                                  >
                                    <img src="https://img.alicdn.com/imgextra/i4/O1CN01gfTvqj1neivKbKg1I_!!6000000005115-55-tps-16-16.svg" />
                                  </LinkButton>
                                </div>
                              }
                              key={item.name}
                            >
                              <div style={{ marginBottom: 16, fontSize: 14, color: '#808080' }}>
                                {item.description}
                              </div>
                              {item?.args?.map((arg, idx) => {
                                return (
                                  <div key={arg.name} style={{ marginBottom: 16 }}>
                                    <div style={{ marginBottom: 8 }}>
                                      {intl('apigw.mcp-server.mcp-server-details.Parameter')}
                                      {idx + 1}
                                    </div>
                                    <div
                                      style={{
                                        padding: 12,
                                        background: '#242526',
                                        color: '#fff',
                                        borderRadius: 2,
                                      }}
                                    >
                                      <div>name: {arg.name}</div>
                                      <div>description: {arg.description}</div>
                                    </div>
                                  </div>
                                );
                              })}
                              {/* <Form>
                         {item.args.map((arg) => (
                           <Form.Item required={arg.required} label={arg.name}>
                             <Input placeholder={arg.description} />
                           </Form.Item>
                         ))}
                        </Form> */}
                            </Collapse.Panel>
                          ))
                        )}
                      </Collapse>
                    </div>
                  </div>
                )}
              </div>
              <div
                style={{
                  flex: 1,
                  height: '100%',
                  marginLeft: 24,
                  background: '#Fff',
                  border: '1px solid #E5e5e5',
                  borderRadius: 4,
                  width: '35%',
                  padding: '16px 24px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 16,
                }}
              >
                <section style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div style={{ fontWeight: 'bold' }}>
                      {intl('apigw.mcp-server.mcp-server-details.ConnectToTheMcpService')}
                    </div>
                    {/* <div>查看文档</div> */}
                  </div>
                  <Radio.Group style={{ display: 'flex' }} shape="button" defaultValue="agent">
                    <Radio style={{ flex: 1, maxWidth: 200 }} value="agent">
                      {intl('apigw.mcp-server.mcp-server-details.AgentClient')}
                    </Radio>
                    {/* <Radio style={{ flex: 1 }} value="typescript">
                 Typescript
                </Radio>
                <Radio style={{ flex: 1 }} value="python">
                 Python
                </Radio> */}
                  </Radio.Group>
                </section>
                <section style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div
                      style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 8 }}
                    >
                      <CircleIcon />
                      {intl('apigw.mcp-server.mcp-server-details.StepGenerateAUrl')}
                    </div>
                  </div>
                  <Select
                    style={{ width: '100%' }}
                    value={currentDomain}
                    onChange={(value) => {
                      setCurrentDomain(value);
                    }}
                  >
                    {selectedDomainList.map((domain) => (
                      <Select.Option
                        style={{ display: 'flex', alignItems: 'center' }}
                        value={domain.name}
                      >
                        <span style={{ color: '#808080', marginRight: 10 }}>
                          {intl('apigw.mcp-server.mcp-server-details.DomainName')}
                        </span>{' '}
                        {domain.name}
                        {domain.networkType === 'Internet' ? (
                          <Tag style={{ marginLeft: 4 }}>
                            <span
                              style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                height: '100%',
                              }}
                            >
                              {intl('apigw.mcp-server.mcp-server-details.ForTestingOnly')}
                            </span>
                          </Tag>
                        ) : null}
                      </Select.Option>
                    ))}
                  </Select>
                  <div>
                    <div style={{ border: '1px solid #E5E5E5' }}>
                      <Tab
                        activeKey={protocol || currentTab}
                        style={{ background: '#EFF3F8' }}
                        onChange={(key) => setCurrentTab(key as string)}
                        extra={
                          <div
                            style={{ color: '#0064C8', marginRight: 8 }}
                            onClick={() => {
                              copy(
                                JSON.stringify(
                                  generateMCPURLConfig(
                                    currentDomain,
                                    pubDomain?.name,
                                    get(mcpServerInfo, 'name'),
                                    currentTab === 'SSE' ? 'sse' : 'streamableHTTP',
                                  ),
                                  null,
                                  2,
                                ),
                              );
                              Message.success(
                                intl('apigw.mcp-server.mcp-server-details.CopiedSuccessfully'),
                              );
                            }}
                          >
                            <Icon
                              style={{ color: '#0064C8', marginRight: 4 }}
                              size="xs"
                              type="copy"
                            />
                            {intl('apigw.mcp-server.mcp-server-details.Copy')}
                          </div>
                        }
                      >
                        {(protocol === 'StreamableHTTP' || protocol === '') && (
                          <Tab.Item title="Streamable HTTP" key="StreamableHTTP">
                            <pre
                              style={{
                                padding: 16,
                                background: '#FFF',
                                width: '100%',
                                overflow: 'auto',
                              }}
                            >
                              {JSON.stringify(
                                generateMCPURLConfig(
                                  currentDomain,
                                  pubDomain?.name,
                                  get(mcpServerInfo, 'name'),
                                  '',
                                ),
                                null,
                                2,
                              )}
                            </pre>
                          </Tab.Item>
                        )}
                        {(protocol === 'SSE' || protocol === '') && (
                          <Tab.Item title="SSE" key="SSE">
                            <pre
                              style={{
                                padding: 16,
                                background: '#FFF',
                                width: '100%',
                                overflow: 'auto',
                              }}
                            >
                              {JSON.stringify(
                                generateMCPURLConfig(
                                  currentDomain,
                                  pubDomain?.name,
                                  get(mcpServerInfo, 'name'),
                                  'sse',
                                ),
                                null,
                                2,
                              )}
                            </pre>
                          </Tab.Item>
                        )}
                      </Tab>
                    </div>
                  </div>
                </section>
                <section style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div
                      style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 8 }}
                    >
                      <CircleIcon />
                      {intl('apigw.mcp-server.mcp-server-details.StepDomainNameDnsMapping')}
                    </div>
                  </div>

                  <div>
                    <div style={{ border: '1px solid #E5E5E5' }}>
                      <div style={{ background: '#EFF3F8', padding: 16, color: '#555' }}>
                        {intl('apigw.mcp-server.mcp-server-details.TheServiceDomainNameNeeds')}
                      </div>
                      <div style={{ padding: '8px 16px' }}>
                        <span>
                          {intl('apigw.mcp-server.mcp-server-details.GatewayAccessAddress')}
                        </span>
                        <div
                          style={{
                            marginTop: 8,
                            paddingLeft: 16,
                            display: 'flex',
                            flexDirection: 'column',
                            gap: 2,
                          }}
                        >
                          {pubDomain && (
                            <span>
                              {intl('apigw.mcp-server.mcp-server-details.PublicNetwork')}
                              {pubDomain.name}{' '}
                              <Icon
                                style={{ color: '#0064C8' }}
                                size="xs"
                                type="copy"
                                onClick={() => {
                                  copy(pubDomain.name);
                                  Message.success(
                                    intl('apigw.mcp-server.mcp-server-details.CopiedSuccessfully'),
                                  );
                                }}
                              />
                            </span>
                          )}
                          {privateDomain && (
                            <span>
                              {intl('apigw.mcp-server.mcp-server-details.PrivateNetwork')}
                              {privateDomain.name}{' '}
                              <Icon
                                style={{ color: '#0064C8' }}
                                size="xs"
                                type="copy"
                                onClick={() => {
                                  copy(privateDomain.name);
                                  Message.success(
                                    intl('apigw.mcp-server.mcp-server-details.CopiedSuccessfully'),
                                  );
                                }}
                              />
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </div>
            </div>
          </Tab.Item>
          <Tab.Item key={'policies-plugins'} title={intl('apigw.aiapi.tabs.policyplugin')}>
            <div className="mt-16">
              <h3 className="f-w-500 fz-14 mb-14">
                {intl('apigw.apiAi.policiesPlugins.MorePlugIns')}
              </h3>
            </div>
            <AiPlugin
              currentEnvId={first(get(mcpServerInfo, 'deployConfigs', []))?.environmentId}
              httpApiInfo={mcpServerInfo}
              gatewayInfo={gatewayInfo}
              sceneType={'McpApi'}
              isAll={false}
            />
          </Tab.Item>
          {
            // !isNacosMcpServer && (
            <Tab.Item title={intl('apigw.pages.requestDemo.Debugging')}>
              <MCPInspector
                authEnabled={authEnabled}
                serverId={routeId}
                envId={get(envInfo, 'environmentId')}
                urls={[sseUrl, streamableHttpUrl]}
                sseUrls={sseUrls}
                tools={mcpTools}
                isMcpHosting={isMcpHosting}
              />
            </Tab.Item>
            // )
          }
          <Tab.Item
            title={intl('apigw.mcp-server.mcp-server-details.ConsumerCertification')}
            key="consumer"
          >
            <div style={{ padding: '0px 24px' }}>
              <McpConsumer
                onEnabled={setAuthEnabled}
                serverId={routeId}
                apiId={apiId}
                envId={get(envInfo, 'environmentId')}
                refreshIndex={refreshIndex}
                gatewayInfo={{
                  engineVersion: get(gatewayData, 'version'),
                  gatewayId: get(gatewayData, 'gatewayId'),
                  gatewayStatus: get(gatewayData, 'status'),
                  name: get(gatewayData, 'name'),
                  vpcInfo: get(gatewayData, 'vpc'),
                }}
                mcpTools={mcpTools}
              />
            </div>
          </Tab.Item>
          <Tab.Item title={intl('apigw.aiapi.tabs.statistics')} key="statistics">
            <Statistics
              apiId={apiId}
              gatewayId={gatewayData?.gatewayId}
              serverId={routeId}
              mcpServerInfo={mcpServerInfo}
            />
          </Tab.Item>
          <Tab.Item title={intl('apigw.mcp-server.mcp-server-details.Log')} key="logs">
            <Logs
              pluginClassId={currentPlugin?.pluginClassId}
              pluginId={currentPlugin?.pluginId}
              gatewayId={gatewayData?.gatewayId}
              mcpServerInfo={mcpServerInfo}
              pluginConfig={pluginConfig}
            />
          </Tab.Item>
        </Tab>
      </div>
      <EditTools
        configExample={currentPlugin?.configExample}
        isCreateing={!Boolean(pluginConfig)}
        defaultMcpToolsYAML={defaultMcpToolsYAML}
        visible={editToolsVisible}
        mcpServerInfo={mcpServerInfo}
        onClose={() => setEditToolsVisible(false)}
        onOk={async (text) => {
          if (text === '') return;
          if (!Boolean(pluginConfig)) {
            await services.CreatePluginAttachment({
              content: {
                pluginId: currentPlugin.pluginId,
                gatewayId: gatewayData.gatewayId,
                attachResourceIds: [routeId],
                attachResourceType: 'GatewayRoute',
                enable: true,
                pluginConfig: base64EncodeUnicode(text),
              },
            });
          } else {
            await services.UpdatePluginAttachment({
              params: {
                pluginAttachmentId: mcpPluginConfig.pluginAttachmentId,
              },
              content: {
                gatewayId: gatewayData.gatewayId,
                attachResourceIds: [routeId],
                attachResourceType: 'GatewayRoute',
                enable: true,
                pluginConfig: base64EncodeUnicode(text),
              },
            });
          }
          if (!isNacosMcpServer) {
            getListPlugins(gatewayData.gatewayId);
          } else {
            getMcpServerDetails();
          }
        }}
      />
      {activeTab === 'tools' && toolMonitorVisible && (
        <ToolsMonitor
          apiId={apiId}
          gatewayId={gatewayData?.gatewayId}
          serverId={routeId}
          mcpServerInfo={mcpServerInfo}
          toolName={toolName}
          onClose={() => {
            setToolName('');
            setToolMonitorVisible(false);
          }}
        />
      )}
    </AppLayout>
  );
};

interface IEditToolsProps {
  configExample: string;
  isCreateing: boolean;
  defaultMcpToolsYAML: string;
  visible: boolean;
  mcpServerInfo: object;
  onClose: () => void;
  onOk: (text: string) => void;
}
function EditTools(props: IEditToolsProps) {
  const { visible, onClose, onOk, defaultMcpToolsYAML, mcpServerInfo, isCreateing, configExample } =
    props;
  const [checked, setChecked] = useState(isCreateing ? 'swagger' : 'tools');
  const [yamlData, setYamlData] = useState(defaultMcpToolsYAML);
  const [disabled, setDisabled] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const mergedYamlDataRef = useRef(yamlData);
  const mergedYamlForRequestDataRef = useRef(yamlData);
  const customYamlDataRef = useRef(yamlData);

  const defaultAuthConfigRef = useRef<any[] | null>(null);

  if (defaultAuthConfigRef.current === null) {
    try {
      const j = jsyaml.load(defaultMcpToolsYAML);
      if (j.server && typeof j.server === 'object') {
        defaultAuthConfigRef.current = get(j, 'server.security', []);
      } else {
        defaultAuthConfigRef.current = [];
      }
    } catch (error) {
      defaultAuthConfigRef.current = [];
    }
  }

  const swaggerAuthRef = useRef<any[]>(defaultAuthConfigRef.current);
  const customYamlAuthRef = useRef<any[]>(defaultAuthConfigRef.current);

  useEffect(() => {
    if (defaultMcpToolsYAML !== '') {
      setYamlData(defaultMcpToolsYAML);
      mergedYamlDataRef.current = defaultMcpToolsYAML;
      mergedYamlForRequestDataRef.current = defaultMcpToolsYAML;
      customYamlDataRef.current = defaultMcpToolsYAML;
    }
  }, [defaultMcpToolsYAML]);

  useEffect(() => {
    setChecked(isCreateing ? 'swagger' : 'tools');
    setDisabled(isCreateing ? true : false);
  }, [isCreateing]);

  useEffect(() => {
    if (visible) {
      if (checked === 'swagger') {
        if (mergedYamlDataRef.current === '') {
          setDisabled(true);
        } else {
          setDisabled(false);
        }
      } else if (checked === 'tools') {
        if (yamlData === '') {
          setDisabled(true);
        } else {
          setDisabled(false);
        }
      }
    }
  }, [visible, checked]);

  const radioOptions = useMemo(() => {
    return isCreateing
      ? [
          {
            label: intl('apigw.mcp-server.mcp-server-details.SwaggerBasedFiles'),
            value: 'swagger',
            desc: intl('apigw.mcp-server.mcp-server-details.UploadSwaggerFilesToAutomatically'),
          },
          {
            label: intl('apigw.mcp-server.mcp-server-details.CustomYaml'),
            value: 'tools',
            desc: intl('apigw.mcp-server.mcp-server-details.ManuallyCreateAnMcpsTool'),
          },
          // { label: '基于请求调试', value: 'request', desc: '通过 AI，将在调试结果生成 MCP 工具' },
        ]
      : [
          {
            label: intl('apigw.mcp-server.mcp-server-details.CustomYaml'),
            value: 'tools',
            desc: intl('apigw.mcp-server.mcp-server-details.ManuallyCreateAnMcpsTool'),
          },
          {
            label: intl('apigw.mcp-server.mcp-server-details.SwaggerBasedFiles'),
            value: 'swagger',
            desc: intl('apigw.mcp-server.mcp-server-details.UploadSwaggerFilesToAutomatically'),
          },
          // { label: '基于请求调试', value: 'request', desc: '通过 AI，将在调试结果生成 MCP 工具' },
        ];
  }, [isCreateing]);

  return (
    <SlidePanel
      title={
        isCreateing
          ? intl('apigw.mcp-server.mcp-server-details.CreateTools')
          : intl('apigw.mcp-server.mcp-server-details.EditingTools')
      }
      width={window.innerWidth * 0.8}
      isShowing={visible}
      onClose={onClose}
      okProps={{
        disabled,
        loading: submitting,
      }}
      onOk={async () => {
        try {
          const data =
            checked === 'tools'
              ? customYamlDataRef.current
              : checked === 'request'
                ? mergedYamlForRequestDataRef.current
                : mergedYamlDataRef.current;
          if (checked === 'swagger') {
            if (!validateAuth(swaggerAuthRef.current)) {
              Message.error(intl('apigw.mcp-server.mcp-server-details.SetCredentialInformation'));
              return;
            }
          } else if (checked === 'tools') {
            if (!validateAuth(customYamlAuthRef.current)) {
              Message.error(intl('apigw.mcp-server.mcp-server-details.SetCredentialInformation'));
              return;
            }
          }
          jsyaml.load(data);
          setSubmitting(true);
          await onOk(data);
          setSubmitting(false);
          onClose();
          Message.success(
            isCreateing
              ? intl('apigw.mcp-server.mcp-server-details.CreatedSuccessfully')
              : intl('apigw.mcp-server.mcp-server-details.UpdatedSuccessfully'),
          );
        } catch (error) {
          setSubmitting(false);
          Message.error(error?.message);
        }
      }}
      onCancel={onClose}
    >
      <section>
        <div
          style={{
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: 8,
            marginBottom: 8,
          }}
        >
          <CircleIcon />
          {intl('apigw.mcp-server.mcp-server-details.EditMethod')}
        </div>
        <Radio.Group
          value={checked}
          onChange={(value) => {
            setChecked(value as string);
            if (value === 'swagger') {
              if (mergedYamlDataRef.current === '') {
                setDisabled(true);
                return;
              }
            }
            if (value === 'request') {
              if (mergedYamlForRequestDataRef.current === '') {
                setDisabled(true);
                return;
              }
            }
            setDisabled(false);
          }}
          style={{ display: 'flex', gap: 6, width: '100%' }}
        >
          {radioOptions.map((option) => (
            <Radio
              key={option.value}
              style={{
                padding: '16px',
                border: '1px solid #E5e5e5',
                borderRadius: 4,
                background: checked === option.value ? '#EFF3F8' : 'white',
                flex: 1,
                height: 80,
              }}
              value={option.value}
            >
              {option.label}
              <p style={{ marginLeft: 22, marginTop: 8, color: '#808080' }}>{option.desc}</p>
            </Radio>
          ))}
        </Radio.Group>
      </section>
      {checked === 'swagger' && (
        <Swagger
          isCreateing={isCreateing}
          defaultValue={yamlData}
          mcpServerInfo={mcpServerInfo}
          onMergeChange={(newValue) => {
            mergedYamlDataRef.current = newValue;
            if (newValue == '') {
              setDisabled(true);
            } else {
              setDisabled(false);
            }
          }}
          onAuthChange={(value) => {
            swaggerAuthRef.current = value;
          }}
        />
      )}
      {/* {checked === 'request' && (
         <RequestDev
           mcpServerInfo={mcpServerInfo}
           isCreateing={isCreateing}
           defaultValue={yamlData}
           onMergeChange={(newValue) => {
             mergedYamlForRequestDataRef.current = newValue;
             if (newValue == '') {
               setDisabled(true);
             } else {
               setDisabled(false);
             }
           }} />
        )} */}
      {checked === 'tools' && (
        <CustomYaml
          configExample={configExample}
          onAuthChange={(value) => {
            customYamlAuthRef.current = value;
          }}
          defaultValue={yamlData}
          onChange={(value) => {
            customYamlDataRef.current = value;
            // setYamlData(value);
            if (value.length === 0) {
              setDisabled(true);
            } else {
              setDisabled(false);
            }
          }}
        />
      )}
    </SlidePanel>
  );
}

export default McpServerDetails;
