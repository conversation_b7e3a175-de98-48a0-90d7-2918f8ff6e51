import jsyaml from 'js-yaml';
import { base64Decode } from '~/utils';
import services from '~/utils/services';
import { find, get, isEmpty, map, includes, filter } from 'lodash';

/** 
 * resources 自定义tools
 */
export const useMcpToolsHook = (options) => {
  const { httpApiId, routeId, gatewayId, resources = [], showAll = true } = options;
  const fetchMcpToolsData = async () => {
    try {
      const data = await services.getRouterInfo({
        params: { httpApiId, routerId: routeId },
      });
      if (!data.mcpServerInfo?.createFromType?.includes('Nacos')) {
        const _tools = await getListPlugins();
        return _tools;
      } else {
        const pluginConfigYAML = data.mcpServerInfo?.mcpServerConfig;
        const tools = getTools(pluginConfigYAML)
        const { resultTools } = showAll ? getAllTools(tools, resources) : getAuthTools(tools, resources);
        return resultTools;
      }
    } catch (error) {
      return {
        data: [],
      };
    }
  };
  const getListPlugins = async () => {
    const data = await services.ListPluginClasses({
      params: {
        pageSize: 100,
        pageNumber: 1,
        gatewayId,
        nameLike: 'mcp-server',
        source: 'HigressOfficial',
        gatewayType: 'AI',
      },
    });
    const items = data.items || [];
    if (items.length > 0) {
      const plugin = items[0];
      const pluginData = await services.ListPluginAttachments({
        params: {
          pageNumber: 1,
          pageSize: 10,
          pluginId: plugin.pluginId,
          attachResourceTypes: 'Operation,GatewayRoute',
          attachResourceId: routeId,
        },
      });
      try {
        const filteredPlugins = pluginData.items.filter((item) => item.attachResourceId === routeId);
        const mcpPluginConfig = filteredPlugins[0];
        if (!mcpPluginConfig) {
          const { resultTools } = getAuthTools([], resources);
          return resultTools;
        }
        const pluginConfigYAML = base64Decode(mcpPluginConfig.pluginConfig);
        const tools = getTools(pluginConfigYAML)
        const { resultTools } = showAll ? getAllTools(tools, resources) : getAuthTools(tools, resources);
        return resultTools;
      } catch (error) {
        console.error(error.message);
        return { data: [] };
      }
    } else {
    }
  }
  /**
 * 根据返回结果获取工具列表
 */
  const getTools = (mcpServerConfig) => {
    const pluginConfigJSON = jsyaml.load(mcpServerConfig);
    let _tools = get(pluginConfigJSON, 'tools', []);
    return _tools;
  }
  const getAllTools = (_tools, resources) => {
    // 自定义输入的工具如果未在tools中，那也要展示出来
    // 编辑时自定义的tool也在列表中展示
    const mcpToolNames = new Set(_tools.map((tool) => {
      tool.value = tool.name;
      tool.label = tool.name;
      return tool.name;
    }));
    const resultTools = [..._tools];
    resources.forEach((resource) => {
      if (!mcpToolNames.has(resource)) {
        resultTools.push({
          name: resource,
          label: resource,
          value: resource,
          // disabled: true,
        });
      }
    });
    return { resultTools };
  };

  const getAuthTools = (_tools, resources) => {
    // 自定义输入的工具如果未在tools中，那也要展示出来
    const resultTools = resources.map((resource) => {
      const matchedTool = _tools.find((item) => item.name === resource);
      return matchedTool || {
        name: resource,
        description: '',
        label: resource,
        value: resource,
      };
    });
    return { resultTools };
  };

  return {
    fetchMcpToolsData,
    getListPlugins,
    getAuthTools,
    getAllTools
  }
}

