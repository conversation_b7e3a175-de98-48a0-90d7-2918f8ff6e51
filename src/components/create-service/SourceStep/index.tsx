import React, { useState, useEffect } from 'react';
import {
  intl,
  Balloon,
  Form,
  Input,
  Field,
  Select,
  Message,
  LinkButton,
  useHistory,
} from '@ali/cnd';
import { includes, noop } from 'lodash';
import CommonEvent from '~/components/shared/CommonEvent';
import CreateK8sSource from './CreateK8sSource';
import CreateNacosSource from './CreateNacosSource';
import { request } from '~/utils/services';
import { compareVersion } from '~/utils';
import { showProductNotOpenMessage } from '~/components/shared/ProductNotOpen';
import { useAliyunProductStatus } from '~/useHooks/useAliyunProductStatus';
import { SourceTypes } from '~/constants';
import { CREATE_SERVICE_FORM_LAYTOUT } from '..';
import { requestResourceGroupParams } from '~/useHooks/useResourceGroup';
import UpgradeGatewayVersion from '~/components/shared/UpgradeGatewayVersion';
import Cookie from '~/utils/cookie';
import { useSetAuthed, useSetIsUseCloudFlowAuthed } from '~/components/app-provider';
import { checkRoleExistenceCloudflow } from '~/components/auth/useAuth';

const aliyunSite = Cookie.get('aliyun_site') || 'CN';
const isIntl = aliyunSite === 'INTL';
const isSaeDisabled = isIntl
  ? includes(window.ALIYUN_CONSOLE_GLOBAL.saeDisabledRegionsIntl, window.regionId)
  : includes(window.ALIYUN_CONSOLE_GLOBAL.saeDisabledRegions, window.regionId);

const SOURCEDATA = (gatewayType) => {
  return [
    {
      label: (
        <div style={{ fontWeight: 'bold', color: '#333' }}>
          {intl('mse.microgw.create.service.select.dynamic.desc')}
        </div>
      ),

      children: [
        {
          value: SourceTypes.K8S,
          label: intl('@ali/widget-edas-microgw::widget.service.root_ack'),
          disabled: false,
        },
        gatewayType !== 'AI' && {
          value: SourceTypes.Nacos,
          label: intl('@ali/widget-edas-microgw::widget.service.root_mse'),
          disabled: false,
        },
      ].filter(Boolean),
    },
    {
      label: (
        <div style={{ fontWeight: 'bold', color: '#333' }}>
          {intl('mse.microgw.create.service.select.static.desc')}
        </div>
      ),

      children: [
        {
          value: SourceTypes.FC3,
          // label: intl('@ali/widget-edas-microgw::widget.service.root_fc'),
          // disabled: false,
          label: includes(window.ALIYUN_CONSOLE_GLOBAL.fcDisabledRegions, window.regionId) ? (
            <Balloon closable={false} trigger={intl('apigw.headBtn.publish.FunctionComputeFc')}>
              {intl('apigw.create-service.SourceStep.NotOpenYet')}
            </Balloon>
          ) : (
            intl('apigw.headBtn.publish.FunctionComputeFc')
          ),

          disabled: includes(window.ALIYUN_CONSOLE_GLOBAL.fcDisabledRegions, window.regionId),
        },
        window.regionId !== 'cn-qingdao' && {
          value: SourceTypes.Sae,
          // label: intl('mse.microgw.source.sae'),
          // disabled: false,
          label: isSaeDisabled ? (
            <Balloon closable={false} trigger={intl('mse.microgw.source.sae')}>
              {intl('apigw.create-service.SourceStep.NotOpenYet')}
            </Balloon>
          ) : (
            intl('mse.microgw.source.sae')
          ),

          disabled: isSaeDisabled,
        },
        {
          value: SourceTypes.VIP,
          label: intl('@ali/widget-edas-microgw::widget.service.root_fix'),
          disabled: false,
        },
        {
          value: SourceTypes.DNS,
          label: intl('@ali/widget-edas-microgw::widget.service.root_dns'),
          disabled: false,
        },
        gatewayType !== 'AI' && {
          value: SourceTypes.AI,
          label: intl('apigw.create-service.SourceStep.AiServices'),
          disabled: false,
        },
        gatewayType !== 'AI' &&
          includes(window.ALIYUN_CONSOLE_GLOBAL.cloudFlowRegionListVisible, window.regionId) && {
            value: SourceTypes.CloudFlow,
            label: intl('apigw.components.EditForm.CloudWorkflowCloudflow'),
            disabled: false,
          },
      ].filter(Boolean),
    },
  ];
};

const limitSourceCount = {
  K8S: 5,
  MSE_NACOS: 1,
  MSE_ZK: 1,
};

const SourceStep = (props) => {
  const {
    GatewayUniqueId,
    changeCurrentStep,
    changeSourceType,
    handleSearchServiceList,
    dynamicSourceType,
    sourceType,
    oktextDisable = noop,
    handleClose,
    gatewayType,
  } = props;
  const field = Field.useField();
  const { enable: csIsOpen } = useAliyunProductStatus({ product: 'cs' });
  const { enable: mseprepaidIsOpen } = useAliyunProductStatus({ product: 'mseprepaid' });
  const { enable: msepostIsOpen } = useAliyunProductStatus({ product: 'msepost' });
  const { enable: saeIsOpen } = useAliyunProductStatus({
    product: 'serverless',
  });
  const [sourceTypeList, setSourceTypeList] = useState(SOURCEDATA(gatewayType));
  const [currentSourceType, setCurrentSourceType] = useState(sourceType);
  const [sourceData, setSourceData] = useState([]);
  const [isAddSource, setIsAddSource] = useState(false);
  const [gatewayInfo, setGatewayInfo] = useState({});
  const [productIsOpen, setProductIsOpen] = useState(true);
  const [upgradeVisible, setUpgradeVisible] = useState(false);
  const history = useHistory();
  const setIsUseCloudFlowAuthed = useSetIsUseCloudFlowAuthed();
  const setAuthed = useSetAuthed();

  useEffect(() => {
    setSourceTypeList(SOURCEDATA(gatewayType));
    GatewayUniqueId && handleGatewayDetail();
  }, []);

  useEffect(() => {
    if (
      currentSourceType &&
      (includes(dynamicSourceType, currentSourceType) || currentSourceType === SourceTypes.Sae)
    ) {
      if (
        (currentSourceType === SourceTypes.K8S && csIsOpen) ||
        (currentSourceType === SourceTypes.Nacos && (mseprepaidIsOpen || msepostIsOpen)) ||
        (currentSourceType === SourceTypes.Sae && saeIsOpen)
      ) {
        setProductIsOpen(true);
      } else {
        setProductIsOpen(false);
      }
    } else {
      setProductIsOpen(true);
    }
  }, [currentSourceType, csIsOpen, mseprepaidIsOpen, msepostIsOpen, saeIsOpen]);

  const handleGatewayDetail = async () => {
    const res = await request({
      productCode: 'APIG',
      action: 'GetGateway',
    })({
      params: {
        gatewayId: GatewayUniqueId,
      },
    });
    res && setGatewayInfo({ ...res, upgrade: compareVersion(res.targetVersion, res.version) > 0 });
  };

  const onSourceTypeChange = async (v) => {
    if (v === SourceTypes.CloudFlow) {
      let res = await checkRoleExistenceCloudflow();
      if (!res) {
        setIsUseCloudFlowAuthed(true);
        setAuthed(false);
        return;
      }
    }
    setCurrentSourceType(v);
    changeSourceType(v);
    setIsAddSource(false);
    if (!includes(dynamicSourceType, v)) {
      changeCurrentStep(1);
    } else {
      changeCurrentStep(0);
    }
  };

  // k8s/nacos步骤根据来源数量刷新
  useEffect(() => {
    if (includes(dynamicSourceType, currentSourceType)) getSourceData();
  }, [currentSourceType]);

  const getSourceData = async (entry?) => {
    const res = await request({
      productCode: 'APIG',
      action: 'ListSources',
    })({
      params: {
        gatewayId: GatewayUniqueId,
        pageNumber: 1,
        pageSize: 50,
        ...requestResourceGroupParams(),
      },
    });
    const list = res?.items || [];
    if (list.length) {
      const data = list.filter((item) => {
        return item.type === currentSourceType;
      });
      setSourceData(data);
      if (data.length) {
        changeCurrentStep(1, entry);
      } else {
        changeCurrentStep(0);
      }
    }
  };

  const handleSourceTypeRender = (item) => {
    if (item?.value == SourceTypes.K8S) {
      return (
        <div style={{ marginLeft: 10 }}>
          {intl('@ali/widget-edas-microgw::widget.service.root_ack')}
          <span style={{ color: '#808080', marginLeft: 4 }}>
            {intl('mse.microgw.source.K8S.desc')}
          </span>
        </div>
      );
    } else if (item?.value === SourceTypes.Nacos) {
      return (
        <div style={{ marginLeft: 10 }}>
          MSE Nacos
          <span style={{ color: '#808080', marginLeft: 4 }}>
            {intl('mse.microgw.source.MSE.desc')}
          </span>
        </div>
      );
    } else if (item?.value === SourceTypes.Zookeeper) {
      return (
        <div style={{ marginLeft: 10 }}>
          MSE Zookeeper
          <span style={{ color: '#808080', marginLeft: 4 }}>
            {intl('mse.microgw.source.ZK.desc')}
          </span>
        </div>
      );
    } else if (item?.value === SourceTypes.Edas) {
      return (
        <div style={{ marginLeft: 10 }}>
          {intl('mse.microgw.source.edas')}
          <span style={{ color: '#808080', marginLeft: 4 }}>
            {intl('mse.microgw.source.EDAS.desc')}
          </span>
        </div>
      );
    } else if (item?.value === SourceTypes.Sae) {
      return <div style={{ marginLeft: 10 }}>{intl('mse.microgw.source.sae')}</div>;
    } else if (item?.value === SourceTypes.VIP) {
      return (
        <div style={{ marginLeft: 10 }}>
          {intl('@ali/widget-edas-microgw::widget.service.root_fix')}
          <span style={{ color: '#808080', marginLeft: 4 }}>
            {intl('mse.microgw.create.service.select.vip.desc')}
          </span>
        </div>
      );
    } else if (item?.value === SourceTypes.DNS) {
      return (
        <div style={{ marginLeft: 10 }}>
          {intl('@ali/widget-edas-microgw::widget.service.root_dns')}
          <span style={{ color: '#808080', marginLeft: 4 }}>
            {intl('mse.microgw.create.service.select.dns.desc')}
          </span>
        </div>
      );
    } else if (item?.value === SourceTypes.FC3) {
      return (
        <div style={{ marginLeft: 10 }}>
          {intl('mse.microgw.create.service.select.fc.name')}
          <span style={{ color: '#808080', marginLeft: 4 }}>
            {intl('mse.microgw.create.service.select.fc.desc')}
          </span>
        </div>
      );
    } else if (item?.value === SourceTypes.AI) {
      return (
        <div style={{ marginLeft: 10 }}>{intl('apigw.create-service.SourceStep.AiServices.1')}</div>
      );
    } else {
      return <div style={{ marginLeft: 10 }}>{item.label}</div>;
    }
  };
  return (
    <div className="source-step">
      {/* <div style={{ color: '#808080', lineHeight: '32px' }}>
            {intl('mse.gateway.router.create_service.step2.desc')}
           </div> */}
      <Form field={field} {...CREATE_SERVICE_FORM_LAYTOUT}>
        <Form.Item
          label={intl('@ali/widget-edas-microgw::widget.source.menu')}
          required
          // asterisk={false}
        >
          <Select
            name="SourceType"
            required
            //@ts-ignore
            requiredMessage={intl('@ali/widget-edas-microgw::widget.service.root_require')}
            onChange={onSourceTypeChange}
            dataSource={sourceTypeList}
            style={{ width: '100%' }}
            itemRender={handleSourceTypeRender}
            value={currentSourceType}
          />

          {oktextDisable() && currentSourceType === SourceTypes.FC3 && (
            <div
              style={{
                marginTop: 4,
                color: '#e00000',
                fontWeight: 'normal',
              }}
            >
              {intl('apigw.create-service.SourceStep.FcServiceIntegrationHasBeen')}
              <LinkButton
                onClick={() => {
                  sessionStorage.setItem('enableUpgradeVersionGateway', 'true');
                  history.push(
                    `/${window.regionId}/gateway/${GatewayUniqueId}/detail?region=${window.regionId}`,
                  );
                }}
              >
                {intl('apigw.create-service.SourceStep.UpgradeTheGatewayVersion')}
              </LinkButton>
            </div>
          )}

          {oktextDisable() && includes([SourceTypes.CloudFlow], currentSourceType) && (
            <div
              style={{
                color: '#e00000',
                fontWeight: 'normal',
              }}
            >
              {intl('apigw.components.EditForm.CloudFlowServiceIntegrationUpgradeTip')}
              <LinkButton
                onClick={() => {
                  sessionStorage.setItem('enableUpgradeVersionGateway', 'true');
                  history.push(
                    `/${window.regionId}/gateway/${GatewayUniqueId}/detail?region=${window.regionId}`,
                  );
                }}
              >
                {intl('apigw.create-service.SourceStep.UpgradeTheGatewayVersion')}
              </LinkButton>
            </div>
          )}

          {oktextDisable() && currentSourceType === SourceTypes.Sae && (
            <div
              style={{
                marginTop: 4,
                color: '#e00000',
                fontWeight: 'normal',
              }}
            >
              {intl('apigw.create-service.SourceStep.TheCurrentInstanceVersionIs')}

              <LinkButton
                onClick={() => {
                  setUpgradeVisible(true);
                }}
                className="mr-2 ml-2"
              >
                {intl('apigw.create-service.SourceStep.Upgrade')}
              </LinkButton>
              {intl('apigw.create-service.SourceStep.ToOrLater')}
            </div>
          )}
        </Form.Item>
        {currentSourceType &&
          productIsOpen &&
          (includes(dynamicSourceType, currentSourceType) ||
            currentSourceType === SourceTypes.Sae) && (
            <Form.Item
              label={intl('@ali/widget-edas-microgw::widget.service.root')}
              style={{ marginBottom: 8 }}
            >
              <div>
                {sourceData.map((sourceItem) => {
                  return (
                    <Input
                      value={sourceItem?.name}
                      key={sourceItem?.sourceId}
                      style={{ width: '100%', marginBottom: 8 }}
                      disabled
                    />
                  );
                })}
              </div>
            </Form.Item>
          )}

        {currentSourceType && !productIsOpen && (
          <div className="mb-10">{showProductNotOpenMessage(currentSourceType)}</div>
        )}
        {isAddSource && (
          <div className="create-source-box">
            {currentSourceType === SourceTypes.K8S && (
              <CreateK8sSource
                GatewayUniqueId={GatewayUniqueId}
                SourceType={currentSourceType}
                gatewayInfo={gatewayInfo}
                handleAddSource={() => {
                  setIsAddSource(false);
                  getSourceData('source');
                  handleSearchServiceList('source');
                }}
                handleCancelAdd={() => {
                  setIsAddSource(false);
                  getSourceData();
                }}
                handleProductIsOpen={(isOpen) => {
                  setProductIsOpen(isOpen);
                }}
              />
            )}

            {currentSourceType === SourceTypes.Nacos && (
              <CreateNacosSource
                GatewayUniqueId={GatewayUniqueId}
                SourceType={currentSourceType}
                gatewayInfo={gatewayInfo}
                handleAddSource={() => {
                  setIsAddSource(false);
                  getSourceData('source');
                  handleSearchServiceList('source');
                }}
                handleCancelAdd={() => {
                  setIsAddSource(false);
                  getSourceData();
                }}
                handleProductIsOpen={(isOpen) => {
                  setProductIsOpen(isOpen);
                }}
              />
            )}
          </div>
        )}

        {!isAddSource && productIsOpen && (
          <div style={{ marginTop: isAddSource ? 16 : 0 }}>
            {currentSourceType &&
              includes(dynamicSourceType, currentSourceType) &&
              ((limitSourceCount[currentSourceType] &&
                sourceData.length < limitSourceCount[currentSourceType]) ||
                !limitSourceCount[currentSourceType]) && (
                <CommonEvent
                  onClick={() => {
                    setIsAddSource(true);
                  }}
                  text={
                    limitSourceCount[currentSourceType]
                      ? `${intl('mse.gateway.router.create_service.add_source')} (${
                          sourceData.length
                        }/${limitSourceCount[currentSourceType]}) `
                      : intl('mse.gateway.router.create_service.add_source')
                  }
                />
              )}
          </div>
        )}
      </Form>
      {upgradeVisible && (
        <UpgradeGatewayVersion
          currentInstance={gatewayInfo}
          handleOk={() => {
            setUpgradeVisible(false);
            Message.success({
              content: intl('apigw.create-service.SourceStep.TheInstanceUpgradeTakesAbout'),
              duration: 6000,
            });
            handleClose();
          }}
          handleClose={() => {
            setUpgradeVisible(false);
          }}
          fromPage="gatewayList"
        />
      )}
    </div>
  );
};
export default SourceStep;
