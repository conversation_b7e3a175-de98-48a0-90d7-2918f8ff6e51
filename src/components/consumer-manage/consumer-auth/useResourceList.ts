import { intl } from '@ali/cnd';
import { validateContent } from '~/utils/validate';
import { useEffect, useState } from 'react';
import services from '~/utils/services';
import { REQUEST_API_TYPE } from './ApiTransfer';
import { concat, every, filter, find, forEach, get, includes, isEmpty, map, noop } from 'lodash';

interface Options {
  /**
   * 资源类型
   */
  attachResourceType: string;
  /**
   * api类型
   */
  apiType?: string;
  /**
   * api唯一标识
   */
  apiId?: string;
  /**
   * 类型
   */
  type: string;
  /**
   * 授权规则Id
   */
  consumerAuthorizationRuleId: string;
}

export const disabledHelps = {
  authorizedRestApi: intl(
    'apigw.consumer-manage.consumer-auth.useResourceList.AuthorizationAlreadyExistsForThe',
  ),
  authorizedLLM: intl(
    'apigw.consumer-manage.consumer-auth.useResourceList.AuthorizationAlreadyExistsForThe',
  ),
  authorizedRestApiOperation: intl(
    'apigw.consumer-manage.consumer-auth.useResourceList.AuthorizationAlreadyExistsForThe.1',
  ),
  authorizedMCP: intl(
    'apigw.consumer-manage.consumer-auth.useResourceList.AuthorizationAlreadyExistsForThe.3',
  ),
  authorizedHttpApiRoute: intl(
    'apigw.consumer-manage.consumer-auth.useResourceList.AuthorizationAlreadyExistsForThe.2',
  ),
  notEnableAuthPolicy: intl(
    'apigw.consumer-manage.consumer-auth.useResourceList.ConsumerAuthenticationNotEnabled',
  ),
  inconsistencyAuthType: intl(
    'apigw.consumer-manage.consumer-auth.useResourceList.InconsistentAuthorizationMethods',
  ),
};

export const itemDisabledInfo = (item) => {
  const { notEnableAuthPolicy, inconsistencyAuthType, authorized, resourceType } = item;
  let disabledStatus = '';
  if (authorized) {
    disabledStatus = `authorized${resourceType}`;
  } else if (notEnableAuthPolicy) {
    disabledStatus = 'notEnableAuthPolicy';
  } else if (inconsistencyAuthType) {
    disabledStatus = 'inconsistencyAuthType';
  }
  return {
    disabled: !!disabledStatus,
    tip: disabledStatus ? disabledHelps[disabledStatus] : '',
  };
};

const validateAuth = ({
  resourceInfo,
  currentEnvironmentId = '',
  consumerId,
  resourceType,
  consumerAuthentications,
  type,
  consumerAuthorizationRuleId = '',
  formatterType,
}) => {
  // 当前环境信息
  const currentEnvironmentInfo = includes(['HttpApiRoute', 'MCP'], resourceType)
    ? get(resourceInfo, 'environmentInfo')
    : find(resourceInfo?.environments || [], (env) => env.environmentId === currentEnvironmentId);
  // 消费者认证策略信息
  const authPolicyInfo = find(
    get(currentEnvironmentInfo, 'policyInfos'),
    (policyInfo) => policyInfo.className === 'Authentication',
  );

  // 消费者认证策略配置信息
  let authPolicyConfig = {};
  try {
    authPolicyConfig = JSON.parse(get(authPolicyInfo, 'config', '{}'));
  } catch (error) { }

  // 消费者信息
  const consumerInfo = find(
    includes(['MCP', 'HttpApiRoute'], resourceType)
      ? get(resourceInfo, 'consumerInfos')
      : get(currentEnvironmentInfo, 'consumerInfos'),
    (consumerInfo) => {
      if (type === 'edit') {
        return (
          consumerInfo.consumerId === consumerId &&
          get(consumerInfo, 'resourceType') === resourceType &&
          consumerInfo.consumerAuthorizationRuleId !== consumerAuthorizationRuleId
        );
      } else {
        // 授权范围是 接口时，需要判断是否存在api级的授权信息
        if (formatterType === 'api' && resourceType === 'RestApiOperation') {
          return (
            consumerInfo.consumerId === consumerId &&
            get(consumerInfo, 'resourceType') === 'RestApi'
          );
        } else {
          return (
            consumerInfo.consumerId === consumerId &&
            get(consumerInfo, 'resourceType') === resourceType
          );
        }
      }
    },
  );

  // let isEnableAuthPolicy = get(authPolicyConfig, 'enable', false);

  let validateAuthInfo = {} as any;

  // // 是否开启消费者认证策略
  // if (!authPolicyInfo && includes(['api', 'route'], formatterType)) {
  //   validateAuthInfo.notEnableAuthPolicy = true;
  // }

  // // 校验消费者认证方式
  // if (
  //   !includes(consumerAuthentications, get(authPolicyConfig, 'authenticationType')) &&
  //   includes(
  //     ['RestApi', 'HttpApiRoute', formatterType === 'api' ? 'RestApiOperation' : ''],
  //     resourceType,
  //   )
  // ) {
  //   validateAuthInfo.inconsistencyAuthType = true;
  // }

  // 是否已授权  (operationConsumerAuthExists 判断 api是否存在 接口级授权信息)
  if (
    !isEmpty(consumerInfo) ||
    (resourceType === 'RestApi' && get(currentEnvironmentInfo, 'operationConsumerAuthExists'))
  ) {
    validateAuthInfo.authorized = true;
  }
  return validateAuthInfo;
};
/**
 * api列表数据格式化方法
 * @param data
 * @returns
 */
export const apiFormatter = (data, options) => {
  const { currentEnvironmentId, consumerAuthentications, consumerId, resourceType, type } = options;
  let _items = [];

  forEach(data, (item) => {
    const httpApis = map(item.versionedHttpApis, (versionedHttpApi) => {
      const validateAuthInfo =
        resourceType === 'HttpApiRoute'
          ? {}
          : validateAuth({
            resourceInfo: versionedHttpApi,
            currentEnvironmentId,
            consumerId,
            resourceType,
            consumerAuthentications,
            type,
            formatterType: 'api',
          });

      return {
        ...versionedHttpApi,
        version:
          get(versionedHttpApi, 'versionInfo.version') ||
          intl('apigw.consumer-manage.consumer-auth.useResourceList.InitializeVersion'),
        label: versionedHttpApi.name,
        value: versionedHttpApi.httpApiId,
        ...itemDisabledInfo({
          ...validateAuthInfo,
          resourceType: resourceType === 'LLM' ? 'LLM' : 'RestApi',
        }),
      };
    });
    item.versionedHttpApis = httpApis;
    _items = concat(_items, httpApis);
  });
  return includes(['LLM', 'RestApi', 'Agent'], resourceType) ? _items : data;
};

/**
 * 接口列表数据格式化方法
 * @param data
 * @returns
 */
export const operationFormatter = (data, options) => {
  const {
    currentEnvironmentId,
    consumerAuthentications,
    consumerId,
    isValidate = true,
    type,
    consumerAuthorizationRuleId,
  } = options;
  return map(data, (item) => {
    const validateAuthInfo = isValidate
      ? validateAuth({
        resourceInfo: item,
        currentEnvironmentId,
        consumerId,
        resourceType: 'RestApiOperation',
        consumerAuthentications,
        type,
        consumerAuthorizationRuleId,
        formatterType: 'operation',
      })
      : {};
    return {
      ...item,
      label: item.name,
      value: item.operationId,
      ...itemDisabledInfo({ ...validateAuthInfo, resourceType: 'RestApiOperation' }),
    };
  });
};

/**
 * 路由列表数据格式化方法
 * @param data
 * @returns
 */
export const routeFormatter = (data, options) => {
  const {
    consumerAuthentications,
    consumerId,
    isValidate = true,
    type,
    consumerAuthorizationRuleId,
    resourceType = 'HttpApiRoute',
  } = options;
  return map(data, (item) => {
    const validateAuthInfo = isValidate
      ? validateAuth({
        resourceInfo: item,
        consumerId,
        resourceType,
        consumerAuthentications,
        consumerAuthorizationRuleId,
        type,
        formatterType: 'route',
      })
      : {};
    return {
      ...item,
      label: item.name,
      value: item.routeId,
      ...itemDisabledInfo({
        ...validateAuthInfo,
        resourceType: resourceType === 'MCP' ? 'MCP' : 'HttpApiRoute',
      }),
    };
  });
};

const useResourceList = (options: Options) => {
  const { attachResourceType, apiType, apiId, type, consumerAuthorizationRuleId } =
    options as Options;
  const [configuredList, setConfiguredList] = useState([]);
  const [previewing, setPreviewing] = useState(false);
  const resourceFetchInfo = {
    RestApi: {
      service: services.ListHttpApis,
      requestParams: { types: REQUEST_API_TYPE[apiType] },
      formatter: apiFormatter,
    },
    RestApiOperation: {
      service: services.ListHttpApiOperations,
      requestParams: { httpApiId: apiId, consumerAuthorizationRuleId },
      formatter: operationFormatter,
    },
    HttpApiRoute: {
      service: services.ListHttpApiRoutes,
      requestParams: { httpApiId: apiId, consumerAuthorizationRuleId },
      formatter: routeFormatter,
    },
  };
  const fetchData = async (params, currentItems) => {
    setPreviewing(true);
    try {
      const { pageNumber = 1, pageSize = 100 } = params;
      const { items = [], totalSize: total = 0 } = await get(
        resourceFetchInfo,
        `${attachResourceType}.service`,
        noop as any,
      )({
        params: {
          pageNumber,
          pageSize,
          ...get(resourceFetchInfo, `${attachResourceType}.requestParams`, {}),
        },
      });
      // 不同类型数据进行格式化处理
      const _items = get(
        resourceFetchInfo,
        `${attachResourceType}.formatter`,
        noop as any,
      )(items, { isValidate: false });
      const updatedItems = currentItems.concat(_items);

      // // 判断是否已经拿到全部配置信息
      // const ids = map(updatedItems, 'value');
      // const isAllConfigured = every(attachResourceIds, (val) => includes(ids, val));

      // // 当还有更多数据, 并且没有拿到全部信息，继续递归调用
      // if (pageNumber * pageSize < total && !isAllConfigured) {
      //   return await fetchData({ pageNumber: pageNumber + 1, pageSize }, updatedItems);
      // }

      if (pageNumber * pageSize < total) {
        return await fetchData({ pageNumber: pageNumber + 1, pageSize }, updatedItems);
      }

      // 返回已配置的详细数据
      // const _configuredList = filter(updatedItems, (item) =>
      //   includes(attachResourceIds, get(item, 'value')),
      // );

      setConfiguredList(updatedItems);
    } finally {
      setPreviewing(false);
    }
  };

  useEffect(() => {
    if (type === 'edit') {
      apiId && fetchData({ pageNumber: 1, pageSize: 100 }, []);
    }
  }, [apiId]);

  return [configuredList, previewing] as any;
};

export default useResourceList;
