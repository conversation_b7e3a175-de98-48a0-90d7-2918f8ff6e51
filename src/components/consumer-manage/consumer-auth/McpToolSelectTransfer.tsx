import { Form, intl } from '@ali/cnd';
import { get, map } from 'lodash';
import React, { useState, useEffect } from 'react';
import SelectScroll from '~/components/api-manage/components/selectScroll';
import services from '~/utils/services';
import McpToolConfig from './api-consumer-auth/McpToolConfig';
import { useMcpToolsHook } from '../../ai-gateway/mcp-server/mcpToolsHook';

const McpToolSelectTransfer = ({ gatewayId, type, field, gatewayType, resourceType }) => {
    const [mcpList, setMcpList] = useState([]);
    const [mcpTools, setMcpTools] = useState([]);
    const { init, setValue, getValue } = field;
    useEffect(() => {
        setMcpList([]);
        setValue('parentResourceId', '');
        setValue('resourcesData', []);
        gatewayId && getHttpApiId();
    }, [gatewayId]);

    useEffect(() => {
        getValue('apiId') && getMcpList();
    }, [getValue('apiId')]);

    const getHttpApiId = async () => {
        const result = await services.ListHttpApis({
            params: {
                pageNumber: 1,
                pageSize: 1,
                types: 'MCP',
                gatewayId,
            },
        });
        let _result = get(result, 'items', []);
        let currentApiId = get(_result, '[0].versionedHttpApis[0].httpApiId');
        setValue('apiId', currentApiId);
    };
    const getMcpList = async (params = {}, callBack = ({ }) => { }) => {
        if (!getValue('apiId')) return;
        const { keyword } = params as any;
        const result = await services.ListHttpApiRoutes({
            params: {
                httpApiId: getValue('apiId'),
                pageNumber: 1,
                pageSize: 200,
                nameLike: keyword || undefined,
                withAuthPolicyInfo: true,
            },
        });
        let _result = get(result, 'items', []);
        _result = map(_result, (item) => ({
            ...item,
            label: item.name,
            value: item.routeId,
        }));
        setValue('parentResourceId', _result[0]?.routeId);
        setMcpList(_result);
        getTools();
        await callBack({ data: _result });
    };
    const getTools = async () => {
        if (!getValue('parentResourceId')) {
            return;
        }
        const { fetchMcpToolsData } = useMcpToolsHook({
            httpApiId: getValue('apiId'),
            routeId: getValue('parentResourceId'),
            gatewayId: gatewayId,
        });
        const result = await fetchMcpToolsData();
        setMcpTools(result);
    };
    return (
        <Form
            field={field}
            {...{
                labelCol: {
                    span: 4,
                },
            }}
        >
            <Form.Item
                label={intl(
                    'apigw.consumer-manage.consumer-auth.McpToolSelectTransfer.SelectAServiceForMcps',
                )}
                required
            >
                <SelectScroll
                    {...init('parentResourceId', {
                        rules: {
                            required: true,
                            message: intl('apigw.consumer-manage.consumer-auth.McpToolSelectTransfer.SelectAServiceForMcps.placeholder'),
                        },
                    })}
                    // showView={true}
                    onChange={(apiInfo, _, item) => {
                        setValue('routeId', get(apiInfo, 'value'));
                        setValue('resourcesData', []);
                        setValue('attachResourceIds', [get(apiInfo, 'value')]);
                        getTools();
                    }}
                    showRefresh={true}
                    showSearch={true}
                    fetchDate={getMcpList}
                    style={{ width: '100%' }}
                    placeholder={intl('apigw.consumer-manage.consumer-auth.McpToolSelectTransfer.SelectAServiceForMcps.placeholder')}
                    disabled={type === 'edit'}
                    dataSource={mcpList}
                />
            </Form.Item>
            <Form.Item
                label={intl('apigw.consumer-manage.consumer-auth.McpToolSelectTransfer.SelectTools')}
                required={true}
            >
                <McpToolConfig
                    {...init('resourcesData', {
                        rules: [
                            {
                                required: true,
                                message: intl(
                                    'apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.SelectATool',
                                ),
                            },
                        ],
                    })}
                    dataSource={mcpTools}
                />
            </Form.Item>
        </Form>
    );
};

export default McpToolSelectTransfer;
