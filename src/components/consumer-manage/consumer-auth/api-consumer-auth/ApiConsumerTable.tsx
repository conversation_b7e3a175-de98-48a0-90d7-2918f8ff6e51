import React, { useEffect, useState, useMemo } from 'react';
import { intl, useHistory, CndTable } from '@ali/cnd';
import { columns, fetchData, search } from './ApiConsumerTableProps';
import ConsumerAuthSlide from './ConsumerAuthSlide';
import { isEmpty, includes } from 'lodash';
import { BatchDeleteConsumerAuth } from '../DeleteConsumerAuth';
const ConsumerTable = (props) => {
  const {
    currentEnvId,
    currentGatewayInfo,
    attachResourceType,
    attachResourceId,
    parentResourceId,
    onRefresh,
    apiType = '',
    mcpTools
  } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const history = useHistory();

  useEffect(() => {
    if (currentEnvId && attachResourceId) setRefreshIndex(Date.now());
  }, [currentEnvId, attachResourceId, attachResourceType]);

  const curData = useMemo(() => {
    return {
      apiType,
      attachResourceType,
      attachResourceIds: [attachResourceId],
      apiId: parentResourceId,
      mcpTools: mcpTools,
      ...(currentEnvId && !isEmpty(currentGatewayInfo)
        ? {
          environmentInfo: {
            environmentId: currentEnvId,
            gatewayInfo: { ...currentGatewayInfo },
          },
        }
        : {}),
    }
  }, [JSON.stringify(props)]);

  return (
    <CndTable
      fetchData={
        (async (value) => {
          if (!attachResourceId) return { data: [], total: 0 };
          return await fetchData({
            ...value,
            environmentId: currentEnvId,
            resourceType: includes(['LLM', 'Agent'], apiType) ? apiType : attachResourceType,
            parentResourceId: attachResourceType === 'MCPTool' ? attachResourceId : undefined,
            resourceId: attachResourceType !== 'MCPTool' ? attachResourceId : undefined,
          });
        }) as any
      }
      columns={columns({ setRefreshIndex, history, apiType, curData }) as any}
      refreshIndex={refreshIndex}
      primaryKey="consumerAuthorizationRuleId"
      rowSelection={{
        selectedRowKeys,
        onChange(selected) {
          setSelectedRowKeys(selected);
        },
      }}
      selection={() => {
        return (
          <BatchDeleteConsumerAuth
            consumerAuthorizationRuleIds={selectedRowKeys}
            onSuccess={() => {
              setRefreshIndex(Date.now());
              setSelectedRowKeys([]);
            }}
          />
        );
      }}
      operation={
        <ConsumerAuthSlide
          onRefresh={onRefresh}
          setRefreshIndex={setRefreshIndex}
          curData={curData}
          buttonText={intl('apigw.consumer-auth.api-consumer-auth.ApiConsumerTable.Authorization')}
          type={'create'}
        />
      }
      search={search() as any}
      fixedHeader
      maxBodyHeight={260}
      showRefreshButton
      pagination={{
        pageSizeList: [10, 20, 50, 100],
        hideOnlyOnePage: false,
      }}
    />
  );
};

export default ConsumerTable;
