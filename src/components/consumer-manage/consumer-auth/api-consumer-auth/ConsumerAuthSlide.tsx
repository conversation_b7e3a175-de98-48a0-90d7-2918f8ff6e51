import React, {
  useRef,
  forwardRef,
  useImperative<PERSON>andle,
  useMemo,
  useEffect,
  useState,
} from 'react';
import SlideButton from '~/components/shared/SlideButton';
import { Form, intl, Field, Radio, Message } from '@ali/cnd';
import { get, noop, isEmpty, includes } from 'lodash';
import { FORM_FIXED_LAYOUT } from '~/constants/formLayout';
import { NA, FEATURE_STATUS_ENV_VISIBLE } from '~/constants';
import EnvSelect from '~/components/api-manage/headBtn/publish/components/envAndBackendServices/envSelect';
import services from '~/utils/services';
import './index.less';
import { onFormatRequestParamsIn } from '../ConsumerApiSidePanel';
import ConsumerSelect from './ConsumerSelect';
import { API_CREATE_STAGE, trackConsumerAuthEvent } from '~/track';
import { CREATE_API_TYPE } from '~/components/api-manage/createApi';
import McpToolConfig from './McpToolConfig';
import { useMcpToolsHook } from '../../../ai-gateway/mcp-server/mcpToolsHook';

const SlideContent = forwardRef((props: any, ref) => {
  const intervalRef = useRef(null);
  const { type, curData, setRefreshIndex, from } = props;
  const [tools, setTools] = useState([]);
  const field = Field.useField();
  const { init, getValue, validate, setValues, setValue, reset } = field;
  useImperativeHandle(ref, () => ({ submit }));

  useEffect(() => {
    return () => clearInterval(intervalRef.current);
  }, []);

  useEffect(() => {
    if (from === 'consumer-auth') {
      getTools();
    }
  }, [from]);

  const getTools = async () => {
    if (!curData?.routeId) {
      return;
    }
    const options = {
      httpApiId: curData?.apiId,
      routeId: curData?.routeId,
      gatewayId: get(curData, 'environmentInfo.gatewayInfo.gatewayId'),
      resources: curData?.resources,
      showAll: true
    }
    const { fetchMcpToolsData } = useMcpToolsHook(options)
    const result = await fetchMcpToolsData();
    setTools(result);
    return result;
  }
  useEffect(() => {
    // 编辑时自定义的tool也在列表中展示
    const { mcpTools, resources = [] } = curData;
    if (mcpTools) {
      const { getAllTools } = useMcpToolsHook({});
      const { resultTools } = getAllTools(mcpTools, resources)
      setTools(resultTools);
    }
  }, [JSON.stringify(curData.mcpTools), JSON.stringify(curData.resources)]);

  const submit = () => {
    return new Promise<void>((resolve, reject) => {
      validate(async (errors, values: any) => {
        if (errors) {
          reject();
          return;
        }
        try {
          const { consumerAuthorizationRuleIds: ruleId, responseSuccess } =
            type === 'create'
              ? await services.CreateConsumerAuthorizationRules({
                content: { ...onFormatRequestParamsIn(values) },
              })
              : await services.UpdateAuthorizationRule({
                params: {
                  consumerAuthorizationRuleId: curData?.consumerAuthorizationRuleId,
                  consumerId: get(curData, 'consumerInfo.consumerId', ''),
                },
                content: { resources: values.resourcesData },
              });

          // if (ruleId) {
          // const response = await callConsumerRuleInIntervals(intervalRef, {
          //   consumerAuthorizationRuleId: ruleId,
          //   consumerId: consumerIds.join(','),
          // });
          if (ruleId || responseSuccess) {
            setRefreshIndex(Date.now());
            Message.success(
              type === 'create'
                ? intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.AuthorizationSucceeded',
                )
                : intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.EditedSuccessfully',
                ),
            );
            type === 'create' && trackConsumerAuthEvent({ stage: API_CREATE_STAGE.success });
            resolve();
            // TODO: 接口授权场景下 暂时不自动切换外层环境
            // onRefresh && onRefresh(get(values, 'environmentInfo.environmentId'));
          } else {
            Message.error(
              type === 'create'
                ? intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.AuthorizationFailed',
                )
                : intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.FailedToEdit'),
            );
            reject();
            type === 'create' && trackConsumerAuthEvent({ stage: API_CREATE_STAGE.fail });
          }
          // }
        } catch (error) {
          reject();
          type === 'create' && trackConsumerAuthEvent({ stage: API_CREATE_STAGE.success });
        }
      });
    });
  };

  useEffect(() => {
    if (isEmpty(curData)) return;
    setValues({
      ...curData,
      attachResourceType:
        get(curData, 'apiType') === CREATE_API_TYPE.Agent
          ? CREATE_API_TYPE.Agent
          : get(curData, 'attachResourceType'),
    });
  }, [JSON.stringify(curData)]);

  useEffect(() => {
    if (isEmpty(curData)) return;
    setValue("resourcesData", get(curData, 'resources', []));
  }, []);

  const gatewayInfo = useMemo(() => {
    return get(getValue('environmentInfo'), 'gatewayInfo', {});
  }, [getValue('environmentInfo')]);


  return (
    <>
      <Form {...FORM_FIXED_LAYOUT} field={field} labelTextAlign="left">
        <Form.Item
          label={intl(
            'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ScopeOfAuthorization',
          )}
        >
          <Radio.Group
            {...init('attachResourceType', { initValue: 'RestApiOperation' })}
            dataSource={[
              {
                label: intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Routing'),
                value: 'HttpApiRoute',
              },
              {
                label: intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Interface'),
                value: 'RestApiOperation',
              },
              {
                label: 'API',
                value: 'RestApi',
              },
              {
                label: 'API',
                value: 'LLM',
              },
              {
                label: 'API',
                value: 'AiApi',
              },
              {
                label: 'API',
                value: 'Agent',
              },
              {
                label: intl('apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.Mcps'),
                value: 'MCP',
              },
              {
                label: intl('apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.Tools'),
                value: 'MCPTool',
              },
            ]}
            renderPreview={(item) => {
              return (
                <div style={{ lineHeight: '30px' }}>
                  {item.label}
                </div>
              );
            }}
            isPreview
          />
        </Form.Item>
        {get(curData, 'attachResourceType') === 'RestApiOperation' && (
          <Form.Item
            label={
              FEATURE_STATUS_ENV_VISIBLE
                ? intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.EffectiveEnvironmentNameId',
                )
                : intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.InstanceNameId')
            }
          >
            {/* @ts-ignore */}
            <EnvSelect
              {...init('environmentInfo', {
                rules: [
                  {
                    required: true,
                    message: FEATURE_STATUS_ENV_VISIBLE
                      ? intl(
                        'apigw.createApi.create-actions.CreateIngressApiSidePanel.SelectAnEnvironment',
                      )
                      : intl(
                        'apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.SelectAnInstance',
                      ),
                  },
                ],
              })}
              hideAction
              renderPreview={(item) => {
                if (!item.label) return;
                const { name, environmentId, gatewayInfo = {} } = item;
                const { name: gatewayName, gatewayId } = gatewayInfo;
                return (
                  <span style={{ lineHeight: '32px' }}>
                    {FEATURE_STATUS_ENV_VISIBLE
                      ? environmentId || name
                        ? `${name}/${environmentId}`
                        : NA
                      : gatewayName || gatewayId
                        ? `${gatewayName} / ${gatewayId}`
                        : NA}
                  </span>
                );
              }}
              type={type}
              notFoundContent={
                FEATURE_STATUS_ENV_VISIBLE
                  ? intl(
                    'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.NoOptionalActiveEnvironmentIs',
                  )
                  : intl(
                    'apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.NoOptionalInstancesAreAvailable',
                  )
              }
              placeholder={
                FEATURE_STATUS_ENV_VISIBLE
                  ? intl(
                    'apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.SelectYourEnvironment',
                  )
                  : intl('apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.SelectAnInstance')
              }
              isPreview
            />
          </Form.Item>
        )}

        {!isEmpty(gatewayInfo) &&
          FEATURE_STATUS_ENV_VISIBLE &&
          includes(['RestApiOperation'], get(curData, 'attachResourceType')) && (
            <Form.Item
              label={intl(
                'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.InstanceNameId',
              )}
              isPreview
            >
              <span>
                {get(gatewayInfo, 'name')
                  ? `${get(gatewayInfo, 'name')}/${get(gatewayInfo, 'gatewayId')}`
                  : get(gatewayInfo, 'gatewayId')}
              </span>
            </Form.Item>
          )}

        <Form.Item
          label={intl('apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.Consumers')}
          required
        >
          {type === 'create' ? (
            <ConsumerSelect
              field={field}
              currentEnvId={get(curData, 'environmentInfo.environmentId')}
              attachResourceType={
                includes(['AiApi', 'LLM'], get(curData, 'apiType'))
                  ? get(curData, 'apiType')
                  : get(curData, 'attachResourceType')
              }
              gatewayType={
                includes(['LLM', 'MCP', 'Agent'], get(curData, 'apiType')) ? 'AI' : 'API'
              }
              attachResourceId={get(curData, 'attachResourceIds[0]')}
            />
          ) : (
            <div style={{ lineHeight: '32px' }}>{get(curData, 'consumerInfo.name')}</div>
          )}
        </Form.Item>
        {
          get(curData, 'apiType') === 'MCP' && getValue('attachResourceType') === 'MCPTool' ? (
            <Form.Item
              label={intl('apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.SelectTools')}
              required
            >
              <McpToolConfig
                {...init('resourcesData', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.SelectATool',
                      ),
                    },
                  ],
                })}
                dataSource={tools}
              />
            </Form.Item>
          ) : (
            <></>
          )
        }
      </Form>
    </>
  );
});
const ConsumerAuthSlide = ({
  buttonText,
  linkButton = false,
  curData = {},
  type = 'create',
  text = false,
  setRefreshIndex = noop,
  onRefresh = noop,
  className = '',
  trigger = null,
  record = {},
  from = 'api'
}) => {
  const slideContentRef = useRef(null);
  const typeInfo = {
    create: {
      slideTitle: intl(
        'apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.AddConsumerAuthorization',
      ),
      okText: intl('apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.Add'),
    },
    edit: {
      slideTitle: intl(
        'apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.EditConsumerAuthorization',
      ),
      okText: intl('apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.Ok'),
    },
  };

  return (
    <SlideButton
      className={`f-w-400 ${className}`}
      buttonText={buttonText}
      customTrigger={trigger}
      slideTitle={get(typeInfo, `[${type}].slideTitle`)}
      linkButton={linkButton}
      text={text}
      onOpen={() => {
        trackConsumerAuthEvent({
          stage: API_CREATE_STAGE.trigger,
        });
      }}
      slideContent={
        <SlideContent
          ref={slideContentRef}
          curData={curData}
          record={record}
          setRefreshIndex={setRefreshIndex}
          onRefresh={onRefresh}
          type={type}
          from={from}
        />
      }
      submit={async () => {
        return slideContentRef?.current?.submit();
      }}
      autoClose={true}
      slideSize={1080}
      okText={get(typeInfo, `[${type}].okText`)}
    />
  );
};

export default ConsumerAuthSlide;
