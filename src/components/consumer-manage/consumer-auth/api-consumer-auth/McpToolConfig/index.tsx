import { intl } from '@ali/cnd';
import React, { useState, forwardRef, useEffect } from 'react';
import { Select } from '@ali/cnd';

const McpToolConfig = forwardRef((props: any) => {
  const { value = [], dataSource = [], onChange } = props;
  const [list, setList] = useState(value);
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    setList(value);
  }, [JSON.stringify(value)]);
  const renderInstanceItem = (item) => {
    return (
      <div>
        {item?.label}
        <span style={{ color: 'rgb(128, 128, 128)', marginLeft: '10px' }}>{item?.description}</span>
      </div>
    );
  };
  const onToolChange = (tagVal, actionType) => {
    if (actionType === 'itemClick') {
      onChange(tagVal);
    } else if (actionType === 'enter' && searchValue && !list.includes(searchValue)) {
      const _data = [...list, searchValue];
      onChange(_data);
    } else if (actionType === 'tag') {
      const _data = [...tagVal];
      onChange(_data);
    }
    setSearchValue('');
  };
  return (
    <div>
      <Select
        value={list}
        mode="tag"
        onChange={(tagVal, actionType) => {
          onToolChange(tagVal, actionType);
        }}
        onBlur={() => {
          if (searchValue && !list.includes(searchValue)) {
            const _data = [...list, searchValue];
            onChange(_data);
          }
          setSearchValue('');
        }}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && searchValue === '') {
            e.preventDefault();
            e.stopPropagation();
          }
        }}
        dataSource={dataSource}
        style={{ width: '100%' }}
        onSearch={setSearchValue}
        searchValue={searchValue}
        showSearch
        itemRender={renderInstanceItem}
        placeholder={intl('apigw.api-consumer-auth.McpToolConfig.SelectAToolOrEnter')}
      />
    </div>
  );
});

export default McpToolConfig;
