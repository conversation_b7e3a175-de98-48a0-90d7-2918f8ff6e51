import React, { useState } from 'react';
import services from '~/utils/services';
import { Actions, intl, Icon, Balloon, Button, Tag, Table } from '@ali/cnd';
import IdOrNameColumn from '~/components/shared/IdOrNameColumn';
import { get, includes, size, map, filter } from 'lodash';
import { NA } from '~/constants';
import Status from '~/components/shared/Status';
import ConsumerDetailsSlide from './ConsumerDetailsSlide';
import DeleteConsumerAuth from '../DeleteConsumerAuth';
import ConsumerAuthSlide from './ConsumerAuthSlide';

export const columns = ({ setRefreshIndex, history, apiType, curData }) => {
  const { Group: TagGroup } = Tag;
  let cols = [
    {
      key: 'consumerInfo.name',
      title: intl('apigw.consumer-manage.consumer-list.ConsumerTableProps.ConsumerNameId'),
      dataIndex: 'consumerInfo.name',
      cell: (value, index, record) => {
        return (
          <IdOrNameColumn
            idProps={{
              text: value,
              hasEdit: false,
              children: (
                <div className="align-center">
                  <ConsumerDetailsSlide
                    curData={{ name: value, consumerId: get(record, 'consumerInfo.consumerId') }}
                    buttonText={value}
                    linkButton
                    gatewayType={includes(['LLM', 'MCP', 'Agent'], apiType) ? 'AI' : 'API'}
                  />

                  {get(record, 'deployStatus') === 'Failed' && (
                    <Balloon
                      type="primary"
                      autoFocus
                      align="t"
                      trigger={
                        <Icon type="warning_fill" size="small" className="color-error ml-8" />
                      }
                      triggerType="hover"
                    >
                      {intl(
                        'apigw.consumer-manage.consumer-list.ConsumerTableProps.ResourceChangeFailedPleaseTry',
                      )}
                    </Balloon>
                  )}
                </div>
              ),
            }}
            nameProps={{
              label: intl('apigw.consumer-manage.consumer-list.ConsumerTableProps.ConsumerId'),
              hasEdit: false,
              text: get(record, 'consumerInfo.consumerId'),
              truncateProps: {
                type: 'width',
                threshold: 264,
                align: 't',
              },
            }}
          />
        );
      },
      lock: 'left',
      width: 300,
    },
    {
      key: 'resourceType',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.ScopeOfAuthorization'),
      dataIndex: 'resourceType',
      cell: (value, index, record) => {
        const resourceTypes = {
          MCP: intl('apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Mcps'),
          RestApi: 'API',
          LLM: 'API',
          RestApiOperation: intl('apigw.consumer-manage.consumer-auth.tableProps.Interface'),
          HttpApiRoute: intl('apigw.consumer-manage.consumer-auth.tableProps.Routing'),
          Agent: 'API',
          MCPTool: intl('apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Tools'),
        };
        return resourceTypes[value] || NA;
      },
    },
    {
      key: 'resources',
      title: intl('apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.AuthorizationTool'),
      dataIndex: 'resources',
      cell: (value, index, record) => {
        if (size(value) > 2) {
          const showValue = value.slice(0, 2);
          const _resources = value.map((resource) => {
            const matchedTool = curData?.mcpTools.find((item) => item.name === resource);
            return matchedTool || { name: resource, description: '' };
          });

          return (
            <>
              <TagGroup>
                {map(showValue, (item, index) => {
                  return (
                    <Tag type="normal" key={index} style={{ margin: '2px' }}>
                      {item}
                    </Tag>
                  );
                })}
              </TagGroup>
              <Balloon
                align="tl"
                title={
                  <span>
                    {intl(
                      'apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.AuthorizationTool',
                    )}{' '}
                    ({value.length})
                  </span>
                }
                trigger={
                  <Button text type="primary" className="ml-6">
                    {intl('apigw.api-manage.apiList.ApiListTableProps.ViewAll')}({value.length})
                  </Button>
                }
                closable={false}
                // @ts-ignore
                style={{ width: 400 }}
              >
                <Table dataSource={_resources} maxBodyHeight={600}>
                  <Table.Column
                    title={intl(
                      'apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Tools',
                    )}
                    dataIndex="name"
                  />

                  <Table.Column
                    title={intl(
                      'apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Description',
                    )}
                    dataIndex="description"
                  />
                </Table>
              </Balloon>
            </>
          );
        } else {
          return (
            <TagGroup>
              {map(value, (item) => {
                return (
                  <Tag type="normal" key={item} style={{ margin: '2px' }}>
                    {item}
                  </Tag>
                );
              })}
            </TagGroup>
          );
        }
      },
      visible: curData.attachResourceType === 'MCPTool',
    },
    {
      key: 'consumerInfo.enable',
      title: intl('apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Status'),
      dataIndex: 'consumerInfo.enable',
      cell: (value, index, record) => {
        return (
          <Status
            value={value}
            dataSource={[
              {
                label: intl('apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Enabled'),
                value: true,
                iconType: 'check_fill',
                type: 'success',
              },
              {
                label: intl('apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Disabled'),
                value: false,
                type: 'minus_fill',
              },
            ]}
          />
        );
      },
    },
    {
      key: 'Action',
      title: intl('gateway.env.manage.table.column.title.action'),
      dataIndex: 'Action',
      cell: (value, index, record) => {
        return (
          // @ts-ignore
          <Actions threshold={2}>
            {curData.attachResourceType === 'MCPTool' && (
              <ConsumerAuthSlide
                // onRefresh={onRefresh}
                setRefreshIndex={setRefreshIndex}
                curData={{
                  ...curData,
                  resources: record.resources,
                  consumerInfo: record.consumerInfo,
                  resourceId: record.resourceId,
                  resourceIdentifier: record.resourceIdentifier,
                  consumerAuthorizationRuleId: record.consumerAuthorizationRuleId,
                  routeId: record.resourceInfo.routeId,
                }}
                linkButton
                buttonText={intl(
                  'apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Edit',
                )}
                type={'edit'}
                from={'api-auth'}
              />
            )}

            <DeleteConsumerAuth
              consumerAuthorizationRuleId={get(record, 'consumerAuthorizationRuleId')}
              onSuccess={() => setRefreshIndex(Date.now())}
              {...get(record, 'consumerInfo')}
            />
          </Actions>
        );
      },
      lock: 'right',
      width: 180,
    },
  ];

  if (curData.attachResourceType !== 'MCPTool') {
    cols = filter(cols, (item) => {
      return item.visible !== false;
    });
  }
  return cols;
};

export const fetchData = async (params) => {
  const { current, pageSize, ...reset } = params;
  try {
    const { items = [], totalSize } = await services.QueryConsumerAuthorizationRules({
      params: {
        pageNumber: current,
        pageSize,
        ...reset,
      },
    });
    return {
      data: items,
      total: totalSize,
    };
  } catch (error) {
    return { data: [], total: 0 };
  }
};

export const search = () => {
  return {
    defaultDataIndex: 'consumerNameLike',
    defaultSelectedDataIndex: 'consumerNameLike',
    options: [
      {
        label: intl('@ali/widget-edas-microgw::widget.consumer.name'),
        templateProps: {
          placeholder: intl(
            'apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.EnterAConsumerNameTo',
          ),
        },
        dataIndex: 'consumerNameLike',
        template: 'input',
      },
    ],
  };
};
