import React, { useState } from 'react';
import {
  intl,
  Truncate,
  Tag,
  Copy,
  Actions,
  Link,
  Button,
  CndTable,
  CndResourceDisplay,
  Balloon,
  Icon,
  LinkButton,
} from '@ali/cnd';
import { ApiTypeInfo, CREATE_API_TYPE } from '~/components/api-manage/createApi';
import { NA, routeMatchRule } from '~/constants';
import { find, get, isEmpty, map, includes, filter } from 'lodash';
import services from '~/utils/services';
import DeleteConsumerAuth from './DeleteConsumerAuth';
import { BatchDeleteConsumerAuth } from './DeleteConsumerAuth';
import { authTypes } from './api-consumer-auth/ConsumerAuthConfig';
import ConsumerApiSidePanel from './ConsumerApiSidePanel';
import ConsumerAuthSlide from './api-consumer-auth/ConsumerAuthSlide';
import { useMcpToolsHook } from '../../ai-gateway/mcp-server/mcpToolsHook';
export const columns = ({ history, setRefreshIndex, onEditAction }) => {
  return [
    // 站位，保持布局
    {
      key: 'name11',
      title: ' ',
      dataIndex: 'apiInfo111',
      cell: (value, index, record) => {
        return null;
      },
      lock: 'left',
      width: 30,
    },
    {
      key: 'name',
      title: intl('apigw.api-manage.apiList.ApiListTableProps.ApiName'),
      dataIndex: 'apiInfo',
      cell: (value, index, record) => {
        let apiId = get(value, 'httpApiId');
        let name = get(value, 'name');
        let version =
          get(value, 'versionInfo.version') ||
          intl('apigw.consumer-manage.consumer-auth.tableProps.InitializeVersion');
        const gatewayName = get(record, 'gatewayInfo.name');
        const gatewayId = get(record, 'gatewayInfo.gatewayId');

        const isRuleEfficient =
          (!isEmpty(record, 'apiInfo') && get(record, 'apiInfo.deployConfigs[0].environmentId')) ===
          get(record, 'environmentInfo.environmentId');
        let isRestApi = get(value, 'type') === CREATE_API_TYPE.REST;
        const typePaths = {
          [CREATE_API_TYPE.HTTP]: `/${window.regionId}/api-manage/api-http/${apiId}/router?region=${window.regionId}`,
          [CREATE_API_TYPE.REST]: `/${window.regionId}/api-manage/api-rest/${name}/${apiId}?region=${window.regionId}&tabKey=InterfaceList`,
          [CREATE_API_TYPE.AI]: `/${window.regionId}/api-manage/api-ai/${apiId}?region=${window.regionId}`,
          [CREATE_API_TYPE.LLM]: `/${window.regionId}/ai-gateway/${gatewayId}/llm-api/${apiId}?region=${window.regionId}`,
          // [CREATE_API_TYPE.MCP]: `/${window.regionId}/ai-gateway/${gatewayId}/mcp-server/${apiId}/${}?region=${window.regionId}`,
        };
        return (
          <>
            <div className="align-center">
              {/* @ts-ignore */}
              <Link to={typePaths[value?.type]}>
                <Copy text={name} showIconTooltip truncateProps={{ type: 'width', threshold: 150 }}>
                  {name}
                </Copy>
              </Link>
              {get(record, 'deployStatus') === 'Failed' && (
                <Balloon
                  type="primary"
                  autoFocus
                  align="t"
                  trigger={<Icon type="warning_fill" size="small" className="color-error" />}
                  triggerType="hover"
                >
                  {intl(
                    'apigw.consumer-manage.consumer-auth.tableProps.ResourceChangeFailedPleaseTry',
                  )}
                </Balloon>
              )}

              {isRuleEfficient &&
                !get(record, 'apiInfo.enableAuth') &&
                get(record, 'apiInfo.type') === 'AI' && (
                  <Balloon
                    trigger={<Icon type="info_fill" size={'small'} className="color-warning" />}
                    align="t"
                    closable={false}
                  >
                    {intl(
                      'apigw.consumer-manage.consumer-auth.tableProps.ConsumerAuthenticationIsNotEnabled',
                    )}
                  </Balloon>
                )}

              {!isRuleEfficient && get(record, 'apiInfo.type') === 'AI' && (
                <Balloon
                  trigger={<Icon type="info_fill" size={'small'} className="color-warning" />}
                  align="t"
                  closable={false}
                  triggerType="hover"
                  style={{ width: 300 }}
                >
                  {intl('apigw.consumer-manage.consumer-auth.tableProps.TheCurrentAiApiIs', {
                    gatewayName: gatewayName,
                    gatewayId: gatewayId,
                  })}
                </Balloon>
              )}
            </div>
            {isRestApi && (
              <span>
                {intl('apigw.consumer-manage.consumer-auth.tableProps.VersionVersion', {
                  version: version,
                })}
              </span>
            )}
          </>
        );
      },
      lock: 'left',
      width: 200,
    },
    {
      key: 'type',
      title: intl('apigw.api-manage.apiList.ApiListTableProps.ApiType'),
      dataIndex: 'apiInfo.type',
      cell: (value, index, record) => {
        const typeInfo = ApiTypeInfo[value] || {};
        return isEmpty(typeInfo) ? (
          NA
        ) : (
          <div className="align-center">
            <img src={typeInfo.icon} width={18} alt="" />
            <div className="ml-8">{typeInfo.title}</div>
          </div>
        );
      },
      width: 150,
    },
    {
      key: 'consumerAuthorization',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.ConsumerCertification'),
      dataIndex: 'consumerAuthorization',
      cell: (value, index, record) => {
        const apiInfo = get(record, 'apiInfo');
        return includes(['AI', 'LLM'], get(apiInfo, 'type'))
          ? authTypes[get(apiInfo, 'authConfig.authType')] ||
          intl('apigw.consumer-manage.consumer-auth.tableProps.NotEnabled')
          : NA;
      },
      width: 150,
    },

    {
      key: 'gatewayInfo',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.Instance'),
      dataIndex: 'gatewayInfo',
      cell: (value, index, record) => {
        const isShowEnv = get(record, 'resourceType') !== 'HttpApiRoute';
        const gatewayName = get(record, 'gatewayInfo.name');
        const gatewayId = get(record, 'gatewayInfo.gatewayId');

        return isShowEnv ? (
          <div>
            <CndResourceDisplay
              resourceName={gatewayName}
              resourceId={gatewayId}
              url={`${window.location.origin}/#/${window.regionId}/gateway/${gatewayId}/detail`}
              showLinkIcon
              copyNameProps={{
                truncateProps: {
                  type: 'width',
                  threshold: 220,
                },
              }}
              copyIdProps={{
                truncateProps: {
                  type: 'width',
                  threshold: 220,
                },
              }}
            />
          </div>
        ) : (
          NA
        );
      },
    },
    {
      key: 'resourceType',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.ScopeOfAuthorization'),
      dataIndex: 'resourceType',
      cell: (value, index, record) => {
        const resourceTypes = {
          RestApi: 'API',
          LLM: 'API',
          RestApiOperation: intl('apigw.consumer-manage.consumer-auth.tableProps.Interface'),
          HttpApiRoute: intl('apigw.consumer-manage.consumer-auth.tableProps.Routing'),
        };
        return resourceTypes[value] || NA;
      },
      width: 150,
    },
    // {
    //   key: 'expireTimestamp',
    //   title: '有效期',
    //   dataIndex: 'expireTimestamp',
    //   cell: (value, index, record) => {
    //     const aboutToExpire = get(record, 'aboutToExpire');
    //     const haveExpired = get(record, 'haveExpired');
    //     const expireMode = get(record, 'expireMode');
    //     return (
    //       <>
    //         <span>{expireMode === 'LongTerm' ? '长期' : formatDate(value)}</span>
    //         {aboutToExpire && (
    //           <Tag
    //             color="yellow"
    //             className="ml-8"
    //             size="small"
    //             style={{ border: 'none', color: '#CF6700', background: '#FFEEDD' }}
    //           >
    //             即将过期
    //           </Tag>
    //         )}
    //         {haveExpired && (
    //           <Tag
    //             color="yellow"
    //             className="ml-8"
    //             size="small"
    //             style={{ border: 'none', color: '#D50B16', background: '#FFEDEC' }}
    //           >
    //             即将过期
    //           </Tag>
    //         )}
    //       </>
    //     );
    //   },
    // },
    {
      key: 'Action',
      title: intl('gateway.env.manage.table.column.title.action'),
      dataIndex: 'Action',
      cell: (value, index, record) => {
        return (
          // @ts-ignore
          <Actions threshold={2}>
            {/* {get(record, 'resourceType') !== 'RestApi' && (
            <Button
            text
            type="primary"
            className="f-w-400"
            onClick={() => {
             onEditAction(record);
            }}
            >
            {intl('apigw.consumer-manage.consumer-auth.tableProps.Edit')}
            </Button>
            )} */}
            {!includes(['RestApiOperation', 'HttpApiRoute'], get(record, 'resourceType')) ? (
              <DeleteConsumerAuth
                consumerId={get(record, 'consumerId')}
                consumerAuthorizationRuleId={get(record, 'consumerAuthorizationRuleId')}
                name={get(record, 'apiInfo.name')}
                onSuccess={() => setRefreshIndex(+new Date())}
              />
            ) : (
              <span>{NA}</span>
            )}
          </Actions>
        );
      },
      lock: 'right',
      width: 150,
    },
  ];
};

export const aiGatewayColumns = ({ history, setRefreshIndex, onEditAction, resourceType }) => {
  let aiColnums = [
    // 站位，保持布局
    {
      key: 'name11',
      title: ' ',
      dataIndex: 'apiInfo111',
      cell: (value, index, record) => {
        return null;
      },
      lock: 'left',
      width: 30,
    },
    {
      key: 'name',
      title: includes(['LLM', 'Agent'], resourceType)
        ? intl('apigw.api-manage.apiList.ApiListTableProps.ApiName')
        : intl('apigw.consumer-manage.consumer-auth.tableProps.ServiceName'),

      dataIndex: 'apiInfo',
      cell: (value, index, record) => {
        let apiId = get(value, 'httpApiId');
        let name = get(value, 'name');
        let routeInfo = get(record, 'resourceInfo.route');
        const gatewayInfo = includes(['LLM', 'Agent'], resourceType)
          ? get(record, 'gatewayInfo')
          : get(routeInfo, 'environmentInfo.gatewayInfo');
        const gatewayName = get(gatewayInfo, 'name');
        const gatewayId = get(gatewayInfo, 'gatewayId');

        const isRuleEfficient =
          (!isEmpty(record, 'apiInfo') && get(record, 'apiInfo.deployConfigs[0].environmentId')) ===
          get(record, 'environmentInfo.environmentId');
        const typePaths = {
          [CREATE_API_TYPE.LLM]: `/${window.regionId}/ai-gateway/${gatewayId}/llm-api/${apiId}?region=${window.regionId}`,
          [CREATE_API_TYPE.Agent]: `/${window.regionId}/ai-gateway/${gatewayId}/agent-api/${apiId}?region=${window.regionId}`,
          [CREATE_API_TYPE.MCP]: `/${window.regionId
            }/ai-gateway/${gatewayId}/mcp-server/${apiId}/${get(routeInfo, 'routeId')}?region=${window.regionId
            }`,
        };
        return (
          <>
            <div className="align-center">
              {/* @ts-ignore */}
              <Link to={typePaths[value?.type]}>
                <Copy text={name} showIconTooltip truncateProps={{ type: 'width', threshold: 150 }}>
                  {includes(['LLM', 'Agent'], get(record, 'resourceType'))
                    ? name
                    : get(routeInfo, 'name')}
                </Copy>
              </Link>
              {get(record, 'deployStatus') === 'Failed' && (
                <Balloon
                  type="primary"
                  autoFocus
                  align="t"
                  trigger={<Icon type="warning_fill" size="small" className="color-error" />}
                  triggerType="hover"
                >
                  {intl(
                    'apigw.consumer-manage.consumer-auth.tableProps.ResourceChangeFailedPleaseTry',
                  )}
                </Balloon>
              )}

              {(includes(['LLM', 'Agent'], get(record, 'apiInfo.type'))
                ? !get(record, 'apiInfo.enableAuth')
                : !get(routeInfo, 'enableAuth')) && (
                <Balloon
                  trigger={<Icon type="info_fill" size={'small'} className="color-warning" />}
                  align="t"
                  closable={false}
                >
                  {intl('apigw.consumer-manage.consumer-auth.tableProps.Current')}
                  {get(record, 'apiInfo.type') === 'LLM'
                    ? 'Model API'
                    : get(record, 'apiInfo.type') === 'Agent'
                      ? 'Agent API'
                      : intl('apigw.consumer-manage.consumer-auth.tableProps.Mcps')}{' '}
                  {intl(
                    'apigw.consumer-manage.consumer-auth.tableProps.ConsumerAuthenticationIsNotEnabled.2',
                  )}
                </Balloon>
              )}

              {!isRuleEfficient && includes(['LLM', 'Agent'], get(record, 'apiInfo.type')) && (
                <Balloon
                  trigger={<Icon type="info_fill" size={'small'} className="color-warning" />}
                  align="t"
                  closable={false}
                  triggerType="hover"
                  style={{ width: 300 }}
                >
                  {intl('apigw.consumer-manage.consumer-auth.tableProps.TheCurrentLlmApiIs')}
                  {gatewayName} / {gatewayId}
                  {intl(
                    'apigw.consumer-manage.consumer-auth.tableProps.ThisAuthorizationRuleDoesNot',
                  )}
                  {/* {intl('apigw.consumer-manage.consumer-auth.tableProps.TheCurrentAiApiIs', {
                gatewayName: gatewayName,
                gatewayId: gatewayId,
                })} */}
                </Balloon>
              )}
            </div>
          </>
        );
      },
      // width: 200,
    },
    {
      key: 'consumerAuthorization',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.ConsumerCertification'),
      dataIndex: 'consumerAuthorization',
      cell: (value, index, record) => {
        const apiInfo = get(record, 'apiInfo');
        const routeInfo = get(record, 'resourceInfo.route');
        return (
          authTypes[
          includes(['LLM', 'Agent'], get(record, 'apiInfo.type'))
            ? get(apiInfo, 'authConfig.authType')
            : get(routeInfo, 'authConfig.authType')
          ] || intl('apigw.consumer-manage.consumer-auth.tableProps.NotEnabled')
        );
      },
      // width: 150,
    },

    {
      key: 'gatewayInfo',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.Instance'),
      dataIndex: 'gatewayInfo',
      cell: (value, index, record) => {
        let routeInfo = get(record, 'resourceInfo.route');
        const gatewayInfo = includes(['LLM', 'Agent'], get(record, 'apiInfo.type'))
          ? get(record, 'gatewayInfo')
          : get(routeInfo, 'environmentInfo.gatewayInfo');
        const gatewayName = get(gatewayInfo, 'name');
        const gatewayId = get(gatewayInfo, 'gatewayId');

        return (
          <div>
            <CndResourceDisplay
              resourceName={gatewayName}
              resourceId={gatewayId}
              url={`${window.location.origin}/#/${window.regionId}/ai-gateway/${gatewayId}/detail`}
              showLinkIcon
              copyNameProps={{
                truncateProps: {
                  type: 'width',
                  threshold: 220,
                },
              }}
              copyIdProps={{
                truncateProps: {
                  type: 'width',
                  threshold: 220,
                },
              }}
            />
          </div>
        );
      },
    },
    {
      key: 'resourceType',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.ScopeOfAuthorization'),
      dataIndex: 'resourceType',
      cell: (value, index, record) => {
        return {
          MCP: intl('apigw.consumer-manage.consumer-auth.tableProps.Mcps.1'),
          MCPTool: intl('apigw.consumer-manage.consumer-auth.tableProps.McpTools'),
        }[value];
      },
      visible: false,
    },
    {
      key: 'Action',
      title: intl('gateway.env.manage.table.column.title.action'),
      dataIndex: 'Action',
      cell: (value, index, record) => {
        return (
          // @ts-ignore
          <Actions threshold={2}>
            {
              includes(['MCPTool'], record.resourceType) && (
                <ConsumerAuthSlide
                  setRefreshIndex={setRefreshIndex}
                  curData={{
                    apiType: 'MCP',
                    attachResourceType: 'MCPTool',
                    attachResourceIds: [record.resourceId],
                    apiId: get(record, 'apiInfo.httpApiId'),
                    routeId: get(record, 'resourceInfo.route.routeId'),
                    // mcpTools: mcpTools,
                    environmentInfo: {
                      environmentId: get(
                        record,
                        'resourceInfo.route.environmentInfo.environmentId',
                      ),
                      gatewayInfo: get(record, 'resourceInfo.route.environmentInfo.gatewayInfo'),
                    },
                    resources: record.resources,
                    consumerInfo: record.consumerInfo,
                    resourceId: record.resourceId,
                    // resourceIdentifier: record.resourceIdentifier,
                    consumerAuthorizationRuleId: record.consumerAuthorizationRuleId,

                  }}
                  linkButton
                  buttonText={intl('apigw.consumer-manage.consumer-auth.tableProps.Edit')}
                  type={'edit'}
                  from={'consumer-auth'}
                />
              )
            }

            {!includes(['RestApiOperation', 'HttpApiRoute'], get(record, 'resourceType')) ? (
              <DeleteConsumerAuth
                consumerId={get(record, 'consumerId')}
                consumerAuthorizationRuleId={get(record, 'consumerAuthorizationRuleId')}
                name={
                  get(record, 'resourceType') === 'MCP'
                    ? get(record, 'resourceInfo.route.name')
                    : get(record, 'apiInfo.name')
                }
                onSuccess={() => setRefreshIndex(+new Date())}
              />
            ) : (
              <span>{NA}</span>
            )}
          </Actions>
        );
      },
      lock: 'right',
      width: 150,
    },
  ];

  if (resourceType !== 'MCP') {
    return filter(aiColnums, (item) => {
      return item.visible !== false;
    });
  } else {
    return aiColnums;
  }
};
const mcpColumns = () => {
  return [
    {
      key: 'name11',
      title: ' ',
      dataIndex: 'apiInfo111',
      cell: (value, index, record) => {
        return null;
      },
      lock: 'left',
      width: 30,
    },
    {
      key: 'toolName',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.Tools'),
      dataIndex: 'name',
    },
    {
      key: 'description',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.Description'),
      dataIndex: 'description',
    },
  ];
};
export const McpExpandRender = ({
  httpApiId,
  routeId,
  gatewayId,
  resources,
}) => {
  return (
    <div className="mr-16 mb-8 consumer-authorization-rule-item">
      <CndTable
        fetchData={
          (async (value) => {
            const options = {
              httpApiId, routeId, gatewayId,
              resources,
              showAll: false
            }
            const { fetchMcpToolsData } = useMcpToolsHook(options)
            const result = await fetchMcpToolsData();
            return { data: result };
          }) as any
        }
        columns={mcpColumns() as any}
        pagination={false}
      // refreshIndex={refreshExpandedIndex}
      // showRefreshButton
      />
    </div>
  );
};

export const expandedRowColumns = ({
  setRefreshExpandedIndex,
  history,
  setRefreshIndex,
  resourceType,
}) => {
  const operation = [
    {
      key: 'resourceInfo.operationInfo.name',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.InterfaceName'),
      dataIndex: 'resourceInfo.operationInfo.name',
      cell: (value, i, record) => {
        return (
          <>
            <Truncate showTooltip align="t" type="width" threshold={194}>
              {value}
            </Truncate>
            {!get(record, 'resourceInfo.operationInfo.enableAuth') && (
              <Balloon
                trigger={<Icon type="info_fill" size={'small'} className="ml-4 color-warning" />}
                align="t"
                closable={false}
              >
                {intl('apigw.consumer-manage.consumer-auth.tableProps.TheCurrentInterfaceDoesNot')}
              </Balloon>
            )}
          </>
        );
      },
      width: 200,
    },
    {
      key: 'resourceInfo.operationInfo.path',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.Path'),
      dataIndex: 'resourceInfo.operationInfo.path',
      width: 150,
    },
    {
      key: 'resourceInfo.operationInfo.authConfig.authType',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.ConsumerCertification'),
      dataIndex: 'resourceInfo.operationInfo.authConfig.authType',
      cell: (value, index, record) => {
        return (
          authTypes[value] || intl('apigw.consumer-manage.consumer-auth.tableProps.NotEnabled')
        );
      },
    },
    {
      key: 'Action',
      title: intl('gateway.env.manage.table.column.title.action'),
      dataIndex: 'Action',
      cell: (value, index, record) => {
        return (
          // @ts-ignore
          <Actions threshold={2}>
            <DeleteConsumerAuth
              consumerId={get(record, 'consumerId')}
              consumerAuthorizationRuleId={get(record, 'consumerAuthorizationRuleId')}
              name={get(record, 'resourceInfo.operationInfo.name')}
              onSuccess={() => {
                setRefreshExpandedIndex(+new Date());
                setRefreshIndex(+new Date());
              }}
            />
          </Actions>
        );
      },
      lock: 'right',
      width: 150,
    },
  ];

  const router = [
    {
      key: 'resourceInfo.route.name',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.RouteName'),
      dataIndex: 'resourceInfo.route.name',
      cell: (value, i, record) => {
        return (
          <>
            <Truncate showTooltip align="t" type="width" threshold={194}>
              {value}
            </Truncate>
            {!get(record, 'resourceInfo.route.enableAuth') && (
              <Balloon
                trigger={<Icon type="info_fill" size={'small'} className="ml-4 color-warning" />}
                align="t"
                closable={false}
              >
                {intl(
                  'apigw.consumer-manage.consumer-auth.tableProps.ConsumerAuthenticationIsNotEnabled.1',
                )}
              </Balloon>
            )}
          </>
        );
      },
      width: 200,
      lock: true,
    },
    {
      key: 'resourceInfo.route.match',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.Path'),
      dataIndex: 'resourceInfo.route.match.path',
      cell: (value = {}) => {
        const routeType = find(routeMatchRule, (rule) => rule.value === get(value, 'type'));
        const routePath = get(value, 'value');

        return (
          <>
            <Truncate showTooltip align="t" type="width" threshold={194}>
              {get(routeType, 'label')} {routePath}
            </Truncate>
          </>
        );
      },
      width: 150,
      lock: true,
    },
    {
      key: 'resourceInfo.route.authConfig.authType',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.ConsumerCertification'),
      dataIndex: 'resourceInfo.route.authConfig.authType',
      cell: (value, index, record) => {
        return (
          authTypes[value] || intl('apigw.consumer-manage.consumer-auth.tableProps.NotEnabled')
        );
      },
      width: 150,
      lock: true,
    },
    {
      key: 'resourceInfo.route.environmentInfo',
      title: intl('apigw.consumer-manage.consumer-auth.tableProps.Instance'),
      dataIndex: 'resourceInfo.route.environmentInfo.name',
      cell: (value, index, record) => {
        const environmentInfo = get(record, 'resourceInfo.route.environmentInfo');
        const envName = get(environmentInfo, 'name');
        const gatewayName = get(environmentInfo, 'gatewayInfo.name');
        const gatewayId = get(environmentInfo, 'gatewayInfo.gatewayId');

        return (
          <div key={envName}>
            <CndResourceDisplay
              resourceName={gatewayName}
              resourceId={gatewayId}
              url={`${window.location.origin}/#/${window.regionId}/gateway/${gatewayId}/detail`}
              showLinkIcon
              copyNameProps={{
                truncateProps: {
                  type: 'width',
                  threshold: 220,
                },
              }}
              copyIdProps={{
                truncateProps: {
                  type: 'width',
                  threshold: 220,
                },
              }}
            />
          </div>
        );
      },
    },
    {
      key: 'Action',
      title: intl('gateway.env.manage.table.column.title.action'),
      dataIndex: 'Action',
      cell: (value, index, record) => {
        return (
          // @ts-ignore
          <Actions threshold={2}>
            <DeleteConsumerAuth
              consumerId={get(record, 'consumerId')}
              consumerAuthorizationRuleId={get(record, 'consumerAuthorizationRuleId')}
              name={get(record, 'resourceInfo.route.name')}
              onSuccess={() => {
                setRefreshExpandedIndex(+new Date());
                setRefreshIndex(+new Date());
              }}
            />
          </Actions>
        );
      },
      lock: 'right',
      width: 150,
    },
  ];

  return resourceType === 'RestApiOperation' ? operation : router;
};


export const ExpandedRender = ({
  apiInfo,
  environmentId,
  resourceType,
  refreshExpandedIndex,
  setRefreshIndex,
  setRefreshExpandedIndex,
  consumerId,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  return (
    <div className="mr-16 mb-8 consumer-authorization-rule-item">
      <CndTable
        primaryKey="consumerAuthorizationRuleId"
        fetchData={
          (async (value) => {
            return await fetchData({
              ...value,
              consumerId,
              parentResourceId: apiInfo.httpApiId,
              environmentId: environmentId,
            });
          }) as any
        }
        columns={
          expandedRowColumns({
            setRefreshExpandedIndex,
            history,
            setRefreshIndex,
            resourceType,
          }) as any
        }
        refreshIndex={refreshExpandedIndex}
        showRefreshButton
        rowSelection={{
          selectedRowKeys,
          onChange(selected) {
            setSelectedRowKeys(selected);
          },
        }}
        selection={() => {
          return (
            <BatchDeleteConsumerAuth
              consumerAuthorizationRuleIds={selectedRowKeys}
              onSuccess={() => {
                setRefreshExpandedIndex(Date.now());
                setRefreshIndex(+new Date());
                setSelectedRowKeys([]);
              }}
            />
          );
        }}
        pagination={{
          pageSize: 10,
          pageSizeSelector: false,
          hideOnlyOnePage: false,
          type: 'simple',
        }}
      />
    </div>
  );
};

export const fetchData = async (params) => {
  const { current, pageSize, ...reset } = params;
  try {
    const { items = [], totalSize } = await services.QueryConsumerAuthorizationRules({
      params: {
        pageNumber: current,
        pageSize,
        ...reset,
      },
    });
    let _items = items;
    if (get(reset, 'groupByApi')) {
      _items = map(_items, (item) => {
        return {
          ...item,
          consumerAuthorizationRuleKey: get(item, 'environmentInfo.environmentId')
            ? get(item, 'apiInfo.httpApiId') + get(item, 'environmentInfo.environmentId')
            : get(item, 'apiInfo.httpApiId'),
        };
      });
    } else if (get(params, 'resourceTypes') === 'MCP,MCPTool') {
      _items = map(_items, (item) => {
        return {
          ...item,
          aiGatewayConsumerAuthorizationRuleKey: get(item, 'consumerAuthorizationRuleId'),
        };
      });
    }
    return {
      data: _items,
      total: totalSize,
    };
  } catch (error) {
    return { data: [], total: 0 };
  }
};

export const operationFetchData = async (params) => {
  const { current, pageSize = 10, ...reset } = params;
  try {
    const { items = [], totalSize } = await services.ListHttpApiOperations({
      params: {
        pageNumber: current,
        pageSize,
        ...reset,
      },
    });
    return {
      data: items,
      total: totalSize,
    };
  } catch (error) {
    return { data: [], total: 0 };
  }
};
export const routeFetchData = async (params) => {
  const { current, pageSize = 10, ...reset } = params;
  try {
    const { items = [], totalSize } = await services.ListHttpApiRoutes({
      params: {
        pageNumber: current,
        pageSize,
        ...reset,
      },
    });
    return {
      data: items,
      total: totalSize,
    };
  } catch (error) {
    return { data: [], total: 0 };
  }
};

export const search = () => ({
  defaultDataIndex: 'apiNameLike',
  defaultSelectedDataIndex: 'apiNameLike',
  options: [
    {
      label: intl('apigw.api-manage.apiList.ApiListTableProps.ApiName'),
      templateProps: {
        placeholder: intl('apigw.consumer-manage.consumer-auth.tableProps.EnterAnApiNameTo'),
      },
      dataIndex: 'apiNameLike',
      template: 'input',
    },
  ],
});
