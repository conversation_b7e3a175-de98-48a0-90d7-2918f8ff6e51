import { Button, Form, intl, Loading, Message, Radio, Select, Switch } from '@ali/cnd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Field, SlidePanel } from '@ali/cnd';
import { FORM_FIXED_LAYOUT } from '~/constants/formLayout';
import ApiTransfer from './ApiTransfer';
import EnvSelect from '~/components/api-manage/headBtn/publish/components/envAndBackendServices/envSelect';
import { get, includes, isEmpty, map, size } from 'lodash';
import OperationsTransfer from './OperationsTransfer';
import ApiVersionFormItem from './ApiVersionFormItem';
import RouteTransfer from './RouteTransfer';
import moment from 'moment';
import { NA, FEATURE_STATUS_ENV_VISIBLE } from '~/constants';
import services from '~/utils/services';
import ApiVersionPreview from './ApiVersionPreview';
import { CREATE_API_TYPE } from '~/components/api-manage/createApi';
import McpTransfer from './McpTransfer';
import { API_CREATE_STAGE, trackConsumerAuthEvent } from '~/track';
import AgentTransfer from './AgentTransfer';
import McpToolTransfer from './McpToolSelectTransfer';
const currentDate = moment().startOf('day');

enum RESOURCE_TYPE {
  RestApi = 'RestApi',
  RestApiOperation = 'RestApiOperation',
  HttpApiRoute = 'HttpApiRoute',
  WebSocket = 'WebSocket',
  LLM = 'LLM',
  MCP = 'MCP',
  MCPTool = 'MCPTool',
  Agent = 'Agent',
}

const attachResourceTypeValue = {
  RestApi: 'RestApiOperation',
  AI: 'RestApi',
  HttpApi: 'HttpApiRoute',
  Websocket: 'HttpApiRoute',
  LLM: 'LLM',
  MCP: 'MCP',
  MCPTool: 'MCPTool',
  Agent: 'Agent',
};

const transferFormLabel = {
  [RESOURCE_TYPE.RestApi]: intl(
    'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectApi',
  ),
  [RESOURCE_TYPE.MCP]: intl(
    'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectAServiceForMcps',
  ),
  // [RESOURCE_TYPE.MCPTool]: intl('MCP服务'),
  [RESOURCE_TYPE.LLM]: intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectApi'),
  [RESOURCE_TYPE.RestApiOperation]: intl(
    'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectAnInterface',
  ),
  [RESOURCE_TYPE.HttpApiRoute]: intl(
    'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectARoute',
  ),
  [RESOURCE_TYPE.Agent]: intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectApi'),
};

/**
 * 格式化请求参数
 * @param values
 * @returns
 */
export const onFormatRequestParamsIn = (values) => {
  const {
    consumerIds,
    attachResourceIds,
    attachResourceType,
    resourcesData,
    environmentInfo,
    expireMode,
    expireTimestamp,
  } = values;
  const _authorizationResourceInfos = map(attachResourceIds, (item) => {
    return {
      resourceId: attachResourceType !== 'MCPTool'
        ? includes(['HttpApiRoute', 'MCP'], attachResourceType)
          ? get(item, 'value', item)
          : item
        : undefined,
      parentResourceId: attachResourceType === 'MCPTool' ? item : undefined,
      environmentId: includes(['HttpApiRoute', 'MCP'], attachResourceType)
        ? get(item, 'environmentInfo.environmentId', get(environmentInfo, 'environmentId'))
        : get(environmentInfo, 'environmentId'),
      resources: attachResourceType === 'MCPTool' ? (resourcesData || attachResourceIds) : undefined,
      // gatewayId: get(environmentInfo, 'gatewayInfo.gatewayId'),
    };
  });
  const date = moment(
    expireTimestamp,
    intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.YyyyMmMonthDdDay'),
  );

  let authorizationRules = [];
  const authorizationRule = {
    resourceType: attachResourceType,
    expireMode: 'LongTerm',
    // expireTimestamp: date.isValid() ? date.valueOf() : expireTimestamp,
  };
  // 消费者反向授权场景
  if (size(consumerIds) === 1) {
    authorizationRules = map(_authorizationResourceInfos, (item) => {
      return {
        consumerId: get(consumerIds, '[0]'),
        resourceIdentifier: item,
        ...authorizationRule,
      };
    });
  } else {
    // 接口、路由正向授权场景
    authorizationRules = map(consumerIds, (item) => {
      return {
        consumerId: item,
        resourceIdentifier: get(_authorizationResourceInfos, '[0]'),
        ...authorizationRule,
      };
    });
  }
  return {
    authorizationRules,
  };
};

/**
 * 禁用当日之前的日期计算方法
 * @param date
 * @param view
 * @returns
 */
const disabledDate = function (date, view) {
  switch (view) {
    case 'date':
      return date.isBefore(currentDate, 'day');
    case 'year':
      return date.year() < currentDate.year();
    case 'month':
      return date.year() * 100 + date.month() < currentDate.year() * 100 + currentDate.month();
    default:
      return false;
  }
};

export const callConsumerRuleInIntervals = (intervalRef, params) => {
  return new Promise((resolve, reject) => {
    intervalRef.current = setInterval(async () => {
      try {
        const { deployStatus } = await services.GetConsumerAuthorizationRule({
          params,
        });
        if (deployStatus === '' || deployStatus === 'Success') {
          clearInterval(intervalRef.current);
          resolve(true);
        } else if (deployStatus === 'Failed') {
          clearInterval(intervalRef.current);
          resolve(false);
        }
      } catch (error) {
        clearInterval(intervalRef.current);
        reject(false);
      }
    }, 1000);
  });
};
const ConsumerApiSidePanel = (props): any => {
  const {
    setVisible,
    type = 'create',
    consumerAuthorizationRuleId,
    setRefreshIndex,
    consumerId,
    consumerAuthentications,
    currentRecord = {},
    gatewayType,
    resourceType,
  } = props;
  const [loading, setLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [deployFailed, setDeployFailed] = useState(false);
  const [withAPIsPublishedToEnvironment, setWithAPIsPublishedToEnvironment] = useState(true);
  const [currentApiInfo, setCurrentApiInfo] = useState({});
  const field = Field.useField({
    onChange(name, value) {
      if (name === 'apiType') {
        setValue('attachResourceType', attachResourceTypeValue[value]);
        setValue('apiId', '');
      }
      if (name === 'expireMode') {
        setValue('expireTimestamp', '');
      }
    },
    values: {
      apiType: !!resourceType ? resourceType : 'RestApi',
    },
  });
  const intervalRef = useRef(null);
  const { init, getValue, setValue, setValues, validate, reset, } = field;

  const gatewayInfo = useMemo(() => {
    return get(getValue('environmentInfo'), 'gatewayInfo', {});
  }, [getValue('environmentInfo')]);

  useEffect(() => {
    return () => clearInterval(intervalRef.current);
  }, []);

  useEffect(() => {
    if (type === 'edit') {
      if (consumerAuthorizationRuleId) {
        onConsumerAuthData(consumerAuthorizationRuleId);
      } else {
        setValues({
          environmentInfo: {
            ...get(currentRecord, 'environmentInfo', {}),
            gatewayInfo: {
              ...get(currentRecord, 'gatewayInfo', {}),
            },
          },
        });
      }
    }
  }, [type, consumerAuthorizationRuleId]);

  const handleSubmit = () => {
    return new Promise((resolve, reject) => {
      validate(async (errors, values: any) => {
        if (errors) {
          return reject(errors);
        }
        setIsProcessing(true);
        setDeployFailed(false);
        try {
          const { consumerAuthorizationRuleIds: ruleId, responseSuccess } =
            type === 'create'
              ? await services.CreateConsumerAuthorizationRules({
                content: { ...onFormatRequestParamsIn({ ...values, consumerIds: [consumerId] }) },
              })
              : await services.UpdateConsumerAuthorizationRule({
                params: { consumerAuthorizationRuleId, consumerId },
                content: { ...onFormatRequestParamsIn(values) },
              });

          if (ruleId || responseSuccess) {
            // const response = await callConsumerRuleInIntervals(intervalRef, {
            //   consumerAuthorizationRuleId: type === 'create' ? ruleId : consumerAuthorizationRuleId,
            //   consumerId,
            // });
            // if (response) {
            setRefreshIndex(Date.now());
            Message.success(
              type === 'create'
                ? intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.AuthorizationSucceeded',
                )
                : intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.EditedSuccessfully',
                ),
            );
            setVisible(false);
            type === 'create' && trackConsumerAuthEvent({ stage: API_CREATE_STAGE.success });
          } else {
            Message.error(
              type === 'create'
                ? intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.AuthorizationFailed',
                )
                : intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.FailedToEdit'),
            );
            setDeployFailed(true);
            type === 'create' && trackConsumerAuthEvent({ stage: API_CREATE_STAGE.fail });
          }
          // }
        } catch (error) { }
        setIsProcessing(false);
      });
    });
  };
  const onConsumerAuthData = async (consumerAuthorizationRuleId) => {
    setLoading(true);
    const data = await services.GetConsumerAuthorizationRule({
      params: {
        consumerAuthorizationRuleId,
        consumerId,
      },
    });
    const {
      expireMode,
      expireTimestamp,
      resourceType: attachResourceType,
      apiInfo,
      environmentInfo,
      gatewayInfo,
    } = data;
    const apiTypes = {
      Rest: 'RestApi',
      Http: 'HttpApi',
      Websocket: 'WebSocket',
      AI: 'AI',
      MCP: 'MCP',
      LLM: 'LLM',
    };
    console.log("apiInfo", apiInfo)
    setValues({
      expireMode,
      expireTimestamp: moment(expireTimestamp).format(
        intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.YyyyMmMonthDdDay'),
      ),
      attachResourceType,
      apiId: get(apiInfo, 'httpApiId'),
      apiName: get(apiInfo, 'name'),
      apiType: apiTypes[get(apiInfo, 'type')],
      resources: get(apiInfo, 'resources'),
      environmentInfo: {
        environmentId: get(environmentInfo, 'environmentId'),
        gatewayInfo,
      },
      ...(attachResourceType === 'RestApi'
        ? { attachResourceIds: [get(apiInfo, 'httpApiId')] }
        : {}),
    });
    setCurrentApiInfo(apiInfo);
    setLoading(false);
  };

  const slidePanelTitle = useMemo(() => {
    const titles = {
      [RESOURCE_TYPE.RestApi]: intl(
        'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ApiAuthorization',
      ),
      [RESOURCE_TYPE.RestApiOperation]: intl(
        'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.InterfaceAuthorization',
      ),
      [RESOURCE_TYPE.HttpApiRoute]: intl(
        'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.RouteAuthorization',
      ),
      [RESOURCE_TYPE.LLM]: intl(
        'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ApiAuthorization',
      ),
      [RESOURCE_TYPE.MCP]: intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Mcps'),
      [RESOURCE_TYPE.MCPTool]: intl(
        'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Mcps',
      ),
    };

    return type === 'create'
      ? intl(
        'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.AddTitlesgetvalueattachresourcetype',
        {
          titlesGetValueAttachResourceType: titles[getValue('attachResourceType') as any],
        },
      )
      : intl(
        'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.EditTitlesgetvalueattachresourcetype',
        {
          titlesGetValueAttachResourceType: titles[getValue('attachResourceType') as any],
        },
      );
  }, [type, getValue('attachResourceType')]);

  return (
    <SlidePanel
      title={slidePanelTitle}
      isShowing={true}
      width={960}
      onClose={() => {
        setVisible(false);
      }}
      customFooter={
        <>
          <Button type="primary" loading={isProcessing} onClick={() => handleSubmit()}>
            {intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Ok')}
          </Button>
          <Button onClick={() => setVisible(false)}>
            {intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Cancel')}
          </Button>
          {deployFailed && (
            <span className="color-error ml-16">
              {intl(
                'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ResourceChangeFailedPleaseTry',
              )}
            </span>
          )}
        </>
      }
    >
      {loading ? (
        <Loading visible style={{ width: '100%', height: '100%' }} />
      ) : (
        <Form
          {...FORM_FIXED_LAYOUT}
          field={field}
          labelTextAlign="left"
          isPreview={type === 'edit'}
        >
          {type === 'edit' && (
            <ApiVersionPreview apiName={getValue('apiName')} currentApiInfo={currentApiInfo} />
          )}

          {!resourceType && (
            <Form.Item
              label={intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ApiType')}
            >
              <Select
                {...init('apiType', {
                  initValue: gatewayType === 'API' ? 'RestApi' : 'LLM',
                })}
                placeholder={intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.PleaseSelect',
                )}
                dataSource={
                  gatewayType === 'API'
                    ? [
                      { label: 'REST API', value: 'RestApi' },
                      { label: 'HTTP API', value: 'HttpApi' },
                      { label: 'WebSocket API', value: 'Websocket' },
                      { label: 'AI API', value: 'AI' },
                    ]
                    : [
                      { label: 'Model API', value: 'LLM' },
                      {
                        label: intl(
                          'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Mcps',
                        ),
                        value: 'MCP',
                      },
                      {
                        label: 'Agent API',
                        value: 'Agent',
                      },
                    ]
                }
                className="full-width"
              />
            </Form.Item>
          )}
          {/* {getValue('attachResourceType') !== 'HttpApiRoute' && ( */}

          <Form.Item
            label={
              FEATURE_STATUS_ENV_VISIBLE
                ? intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.EffectiveEnvironmentNameId',
                )
                : intl(
                  'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ActiveGatewayInstance',
                )
            }
            required={type !== 'edit'}
          >
            {/* @ts-ignore */}
            <EnvSelect
              {...init('environmentInfo', {
                rules: [
                  {
                    required: true,
                    message: FEATURE_STATUS_ENV_VISIBLE
                      ? intl(
                        'apigw.createApi.create-actions.CreateIngressApiSidePanel.SelectAnEnvironment',
                      )
                      : intl(
                        'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectAnInstance',
                      ),
                  },
                ],
              })}
              hideAction
              renderPreview={(item) => {
                if (!item.label) return;
                const { name, environmentId, gatewayInfo = {} } = item;
                const { name: gatewayName, gatewayId } = gatewayInfo;
                return (
                  <span>
                    {FEATURE_STATUS_ENV_VISIBLE
                      ? environmentId || name
                        ? `${name}/${environmentId}`
                        : NA
                      : gatewayName || gatewayId
                        ? `${gatewayName} / ${gatewayId}`
                        : NA}
                  </span>
                );
              }}
              type={type}
              notFoundContent={
                FEATURE_STATUS_ENV_VISIBLE
                  ? intl(
                    'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.NoOptionalActiveEnvironmentIs',
                  )
                  : intl(
                    'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.NoOptionalInstancesAreAvailable',
                  )
              }
              scene="consumer"
              gatewayType={includes(['LLM', 'MCP', 'Agent'], getValue('apiType')) ? 'AI' : 'API'}
            />
          </Form.Item>
          {!isEmpty(gatewayInfo) && FEATURE_STATUS_ENV_VISIBLE && (
            <Form.Item
              label={intl(
                'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.InstanceNameId',
              )}
              isPreview
            >
              <span>
                {get(gatewayInfo, 'name')
                  ? `${get(gatewayInfo, 'name')}/${get(gatewayInfo, 'gatewayId')}`
                  : get(gatewayInfo, 'gatewayId')}
              </span>
            </Form.Item>
          )}
          {/* )} */}

          <Form.Item
            label={intl(
              'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ScopeOfAuthorization',
            )}
          >
            {getValue('apiType') === 'RestApi' && (
              <Radio.Group
                {...init('attachResourceType', { initValue: 'RestApiOperation' })}
                dataSource={[
                  type !== 'create'
                    ? {
                      label: 'API',
                      value: 'RestApi',
                    }
                    : null,
                  {
                    label: intl(
                      'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Interface',
                    ),
                    value: 'RestApiOperation',
                  },
                ].filter(Boolean)}
              />
            )}

            {getValue('apiType') === CREATE_API_TYPE.AI && (
              <Radio.Group
                {...init('attachResourceType', { initValue: 'RestApi' })}
                dataSource={[
                  {
                    label: 'API',
                    value: 'RestApi',
                  },
                ]}
              />
            )}

            {getValue('apiType') === CREATE_API_TYPE.LLM && (
              <Radio.Group
                {...init('attachResourceType', { initValue: 'LLM' })}
                dataSource={[
                  {
                    label: 'API',
                    value: 'LLM',
                  },
                ]}
              />
            )}

            {getValue('apiType') === CREATE_API_TYPE.MCP && (
              <Radio.Group
                {...init('attachResourceType', {
                  initValue: 'MCP',
                  props: {
                    onChange: (value) => {
                      if (value === 'MCP') {
                        reset('resourcesData');
                      } else {
                        reset('attachResourceIds');
                      }
                    },
                  },
                })}
                dataSource={[
                  {
                    label: intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Mcps'),
                    value: 'MCP',
                  }, {
                    label: intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Tools'),
                    value: 'MCPTool',
                  },
                ]}
              />
            )}
            {getValue('apiType') === CREATE_API_TYPE.Agent && (
              <Radio.Group
                {...init('attachResourceType', { initValue: 'Agent' })}
                dataSource={[
                  {
                    label: 'Agent API',
                    value: 'Agent',
                  },
                ]}
              />
            )}

            {includes(['HttpApi', 'Websocket'], getValue('apiType')) && (
              <Radio.Group
                {...init('attachResourceType', { initValue: 'HttpApiRoute' })}
                dataSource={[
                  {
                    label: intl('apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Routing'),
                    value: 'HttpApiRoute',
                  },
                ]}
              />
            )}
          </Form.Item>

          {includes([CREATE_API_TYPE.AI], getValue('apiType')) && (
            <Form.Item
              label={intl(
                'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.OnlyApisPublishedToThis',
              )}
            >
              <Switch
                checked={withAPIsPublishedToEnvironment}
                onChange={setWithAPIsPublishedToEnvironment}
              />
            </Form.Item>
          )}

          {includes(['HttpApiRoute', 'RestApiOperation'], getValue('attachResourceType')) &&
            getValue('apiType') !== 'MCP' &&
            type !== 'edit' && (
              <ApiVersionFormItem
                field={field}
                apiType={getValue('apiType')}
                attachResourceType={getValue('attachResourceType')}
                type={type}
                consumerId={consumerId}
                gatewayId={get(getValue('environmentInfo'), 'gatewayInfo.gatewayId')}
                environmentId={get(getValue('environmentInfo'), 'environmentId')}
                consumerAuthentications={consumerAuthentications}
              />
            )}

          {!(getValue('attachResourceType') === RESOURCE_TYPE.RestApi && type === 'edit') && (
            <>
              <Form.Item label={transferFormLabel[getValue('attachResourceType') as any]} required>
                {includes(
                  [RESOURCE_TYPE.RestApi, RESOURCE_TYPE.LLM],
                  getValue('attachResourceType'),
                ) && (
                    <ApiTransfer
                      {...(init('attachResourceIds', {
                        rules: [
                          {
                            required: true,
                            message: intl(
                              'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectTheApiToAuthorize',
                            ),
                          },
                        ],
                      }) as any)}
                      attachResourceType={getValue('attachResourceType')}
                      withAPIsPublishedToEnvironment={withAPIsPublishedToEnvironment}
                      environmentId={get(getValue('environmentInfo'), 'environmentId')}
                      gatewayId={get(getValue('environmentInfo'), 'gatewayInfo.gatewayId')}
                      apiType={getValue('apiType')}
                      consumerId={consumerId}
                      consumerAuthentications={consumerAuthentications}
                      type={type}
                    />
                  )}

                {getValue('attachResourceType') === RESOURCE_TYPE.RestApiOperation && (
                  <OperationsTransfer
                    {...(init('attachResourceIds', {
                      rules: [
                        {
                          required: true,
                          message: intl(
                            'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectTheInterfaceToAuthorize',
                          ),
                        },
                      ],
                    }) as any)}
                    attachResourceType={RESOURCE_TYPE.RestApiOperation}
                    environmentId={get(getValue('environmentInfo'), 'environmentId')}
                    consumerAuthorizationRuleId={consumerAuthorizationRuleId}
                    consumerId={consumerId}
                    apiId={getValue('apiId')}
                    apiName={getValue('apiName')}
                    type={type}
                  />
                )}

                {getValue('attachResourceType') === RESOURCE_TYPE.HttpApiRoute && (
                  <RouteTransfer
                    {...(init('attachResourceIds', {
                      rules: [
                        {
                          required: true,
                          message: intl(
                            'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectARouteToAuthorize',
                          ),
                        },
                      ],
                    }) as any)}
                    attachResourceType={RESOURCE_TYPE.HttpApiRoute}
                    consumerAuthentications={consumerAuthentications}
                    consumerAuthorizationRuleId={consumerAuthorizationRuleId}
                    consumerId={consumerId}
                    apiId={getValue('apiId')}
                    apiName={getValue('apiName')}
                    type={type}
                  />
                )}
                {getValue('attachResourceType') === RESOURCE_TYPE.MCP && (
                  <McpTransfer
                    {...(init('attachResourceIds', {
                      rules: [
                        {
                          required: true,
                          message: intl(
                            'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectTheMcpsToBe',
                          ),
                        },
                      ],

                    }) as any)}
                    attachResourceType={RESOURCE_TYPE.MCP}
                    consumerAuthentications={consumerAuthentications}
                    consumerAuthorizationRuleId={consumerAuthorizationRuleId}
                    consumerId={consumerId}
                    gatewayId={get(getValue('environmentInfo'), 'gatewayInfo.gatewayId')}
                    type={type}
                  />
                )}
                {getValue('attachResourceType') === RESOURCE_TYPE.Agent && (
                  <AgentTransfer
                    {...(init('attachResourceIds', {
                      rules: [
                        {
                          required: true,
                          message: intl(
                            'apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectTheApiToAuthorize',
                          ),
                        },
                      ],
                    }) as any)}
                    attachResourceType={RESOURCE_TYPE.Agent}
                    consumerAuthentications={consumerAuthentications}
                    environmentId={get(getValue('environmentInfo'), 'environmentId')}
                    consumerAuthorizationRuleId={consumerAuthorizationRuleId}
                    consumerId={consumerId}
                    gatewayId={get(getValue('environmentInfo'), 'gatewayInfo.gatewayId')}
                    type={type}
                  />
                )}
              </Form.Item>
              {getValue('attachResourceType') === RESOURCE_TYPE.MCPTool && (
                <McpToolTransfer
                  field={field}
                  resourceType={RESOURCE_TYPE.MCPTool}
                  // consumerAuthentications={consumerAuthentications}
                  // consumerAuthorizationRuleId={consumerAuthorizationRuleId}
                  // consumerId={consumerId}
                  gatewayId={get(getValue('environmentInfo'), 'gatewayInfo.gatewayId')}
                  gatewayType={gatewayType}
                  type={type}
                />

              )}
            </>
          )}

          {/* <Form.Item label={'授权时间'} required isPreview={false}>
          <Radio.Group {...init('expireMode', { initValue: 'LongTerm' })}>
          <Radio id="LongTerm" value="LongTerm" label="长期" />
          <Radio id="ShortTerm" value="ShortTerm" label="短期" />
          </Radio.Group>
          {getValue('expireMode') === 'ShortTerm' && (
          <div style={{ background: '#f6f6f6', padding: '12px 16px' }}>
           <Form.Item
             label={'有效期至:'}
             required
             isPreview={false}
             labelCol={{
               span: 3,
             }}
             wrapperCol={{ span: 21 }}
             style={{ marginBottom: 0 }}
           >
             <DatePicker
               {...init('expireTimestamp', {
                 rules: [{ required: true, message: '请选择授权时间' }],
               })}
               className="full-width"
               disabledDate={disabledDate}
             />
           </Form.Item>
          </div>
          )}
          </Form.Item> */}
        </Form>
      )}
    </SlidePanel>
  );
};

export default ConsumerApiSidePanel;
