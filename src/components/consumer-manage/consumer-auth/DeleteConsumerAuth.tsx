import React from 'react';
import { LinkButton, intl, Dialog, Message, Button } from '@ali/cnd';
import services from '~/utils/services';
import { size } from 'lodash';

export const BatchDeleteConsumerAuth = ({ consumerAuthorizationRuleIds = [], onSuccess }) => {
  const onDelete = async () => {
    Dialog.alert({
      title: intl('apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.BulkDeauthorization'),
      content: intl(
        'apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.ConfirmBulkDeauthorization',
      ),
      overlayProps: {
        className: 'basic-dialog-overlay',
      },
      okProps: {
        children: intl('apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.Release'),
        warning: true,
      },
      onOk: () => {
        return new Promise<void>(async (resolve, reject) => {
          const { responseSuccess } = await services.BatchDeleteConsumerAuthorizationRule({
            params: { consumerAuthorizationRuleIds: consumerAuthorizationRuleIds.join(',') },
          });
          if (responseSuccess) {
            Message.success(
              intl(
                'apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.AuthorizationRemovedSuccessfully',
              ),
            );
            onSuccess && onSuccess();
            resolve();
          } else {
            reject();
          }
        });
      },
    });
  };

  return (
    <Button type="primary" onClick={onDelete} disabled={size(consumerAuthorizationRuleIds) === 0}>
      {intl('apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.BulkDeauthorization')}
    </Button>
  );
};

const DeleteConsumerAuth = ({
  consumerAuthorizationRuleId,
  name,
  onSuccess,
  consumerId,
  isButton = false,
  text = '',
}) => {
  const onDelete = async () => {
    Dialog.alert({
      title: intl(
        'apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.ConfirmTheAuthorizationOfName',
        { name: name },
      ),
      overlayProps: {
        className: 'basic-dialog-overlay',
      },
      okProps: {
        children: intl('apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.Release'),
        warning: true,
      },
      onOk: () => {
        return new Promise<void>(async (resolve, reject) => {
          const { responseSuccess } = await services.RemoveConsumerAuthorizationRule({
            params: { consumerAuthorizationRuleId, consumerId },
          });
          if (responseSuccess) {
            Message.success(
              intl(
                'apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.AuthorizationRemovedSuccessfully',
              ),
            );
            onSuccess && onSuccess();
            resolve();
          } else {
            reject();
          }
        });
      },
    });
  };

  return (
    <>
      {isButton ? (
        <Button onClick={onDelete}>
          {text || intl('apigw.consumer-manage.consumer-list.DeleteConsumer.Delete')}
        </Button>
      ) : (
        <>
          <LinkButton onClick={onDelete}>
            {intl('apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.RevokeAuthorization')}
          </LinkButton>
        </>
      )}
    </>
  );
};
export default DeleteConsumerAuth;
