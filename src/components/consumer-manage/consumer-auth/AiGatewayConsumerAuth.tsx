import { intl } from '@ali/cnd';
import React, { useState, Fragment, useMemo, useEffect } from 'react';
import { CndTable, Button, useHistory } from '@ali/cnd';
import { columns, search, fetchData, aiGatewayColumns, McpExpandRender } from './tableProps';
import ConsumerApiSidePanel from './ConsumerApiSidePanel';
import { get, isEmpty, includes } from 'lodash';
import './index.less';

const AiGatewayConsumerAuth = (props) => {
  const { currentDetail, consumerId, gatewayType, resourceType } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [refreshExpandedIndex, setRefreshExpandedIndex] = useState(0);
  const [visible, setVisible] = useState(false);
  const [consumerApiSidePanelType, setConsumerApiSidePanelType] = useState('create');
  const [currentConsumerAuthId, setCurrentConsumerAuthId] = useState('');
  const [currentRecord, setCurrentRecord] = useState<any>({});
  const history = useHistory();

  const consumerAuthentications = useMemo(() => {
    const { jwtIdentityConfig, akSkIdentityConfigs, apiKeyIdentityConfigs } = currentDetail;
    let authentications = [];
    if (!isEmpty(jwtIdentityConfig)) {
      authentications.push('Jwt');
    }
    if (!isEmpty(akSkIdentityConfigs)) {
      authentications.push('AkSk');
    }
    if (!isEmpty(apiKeyIdentityConfigs)) {
      authentications.push('Apikey');
    }

    return authentications;
  }, [JSON.stringify(currentDetail)]);

  const onEditAction = (params) => {
    const { consumerAuthorizationRuleId } = params;
    setCurrentConsumerAuthId(consumerAuthorizationRuleId);
    setCurrentRecord(params);
    setConsumerApiSidePanelType('edit');
    setVisible(true);
  };

  const expandedRowRender = (record, index) => {
    return (
      <McpExpandRender
        httpApiId={get(record, 'apiInfo.httpApiId')}
        routeId={get(record, 'parentResourceId')}
        gatewayId={get(record, 'apiInfo.gatewayId')}
        resources={get(record, 'resources')}
      />
    );
  };
  return (
    <Fragment>
      <CndTable
        className="consumer-authorization-rule-list"
        primaryKey="aiGatewayConsumerAuthorizationRuleKey"
        fetchData={
          (async (value) => {
            setRefreshExpandedIndex(refreshExpandedIndex + 1);
            return await fetchData({
              ...value,
              consumerId: get(currentDetail, 'consumerId', consumerId),
              resourceType: resourceType === 'MCP' ? undefined : resourceType,
              resourceTypes: resourceType === 'MCP' ? 'MCP,MCPTool' : undefined,
              // environmentId: { get(record, 'environmentInfo.environmentId') }
            });
          }) as any
        }
        columns={aiGatewayColumns({ setRefreshIndex, history, onEditAction, resourceType }) as any}
        refreshIndex={refreshIndex}
        showRefreshButton
        search={resourceType === 'LLM' && (search() as any)}
        rowExpandable={(record) => {
          return includes(['MCPTool'], get(record, 'resourceType'));
        }}
        expandedIndexSimulate
        expandedRowRender={expandedRowRender}
        operation={
          <Button
            type="primary"
            onClick={() => {
              setConsumerApiSidePanelType('create');
              setCurrentRecord({});
              setVisible(true);
            }}
          >
            {intl('apigw.consumer-manage.consumer-auth.Authorization')}
          </Button>
        }
        pagination={{
          pageSize: 10,
          pageSizeList: [10, 20, 30],
          hideOnlyOnePage: false,
        }}
      />

      {visible && (
        <ConsumerApiSidePanel
          gatewayType={gatewayType}
          resourceType={resourceType}
          setVisible={setVisible}
          setRefreshIndex={() => {
            setCurrentRecord({});
            setRefreshIndex(Date.now());
          }}
          consumerId={get(currentDetail, 'consumerId', consumerId)}
          currentRecord={currentRecord}
          type={consumerApiSidePanelType}
          consumerAuthorizationRuleId={currentConsumerAuthId}
          consumerAuthentications={consumerAuthentications}
        />
      )}
    </Fragment>
  );
};

export default AiGatewayConsumerAuth;
