import React from 'react';
import { Button, intl } from '@ali/cnd';
import CachedData from '~/utils/cacheData';

const actions = [
  {
    title: intl('apigw.components.overview.ActionsGrid.CreateAnInstance'),
    href: CachedData.confLink('feature:gateway:common:buy', {
      regionId: CachedData.getCurrentRegionId(),
    }) as any,
  },
  {
    title: intl('apigw.overview.actions.quickstart'),
    href: CachedData.confLink('feature:overview:QuickStart') as any,
  },
  {
    title: 'OpenAPI',
    href: CachedData.confLink('feature:overview:OpenAPI') as any,
  },
  {
    title: intl('apigw.overview.actions.product'),
    href: CachedData.confLink('feature:overview:product') as any,
  },
];

export default () => {
  return (
    <div className="overview-basic-layout">
      <h3 className="f-w-500" style={{ marginBottom: 0 }}>
        {intl('apigw.components.overview.ActionsGrid.QuickEntrance')}
      </h3>
      <div className="full-width mt-16">
        {actions.map((action) => {
          const { href } = action;
          return (
            <Button
              className="next-action"
              onClick={() => {
                window.open(href, '_blank');
              }}
            >
              {action.title}
            </Button>
          );
        })}
      </div>
    </div>
  );
};
