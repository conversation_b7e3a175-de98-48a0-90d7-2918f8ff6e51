import {
  Divider,
  Field,
  Form,
  Icon,
  Input,
  Select,
  SlidePanel,
  Radio,
  NumberPicker,
  intl,
  Balloon,
} from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Align } from '~/constants/formLayout';
import PluginLanguage from '../shared/PluginCardList/PluginLanguage';
import { get, isEmpty, join, map, split, toNumber } from 'lodash';
import { UPLOAD_EXECUTE_STAGE } from './PluginTableProps';
import PluginUpload from './PluginUpload';
import services from '~/utils/services';
import { getVersionCompare } from '~/utils';
import ExternalLink from '../shared/ExternalLink';

const layoutForm = {
  labelCol: {
    span: 3,
  },
  wrapperCol: {
    span: 20,
  },
  labelTextAlign: 'left' as Align,
};

interface Props {
  visible: boolean;
  type?: string;
  onClose: () => void;
  onRefresh: (pluginClassId?: string) => void;
  plugin?: any;
  gatewayType?: string;
}

const regex = /^(?![-.])[a-z0-9-.]{1,20}(?<![-.])$/;

export const UploadCustomPlugin = ({
  visible,
  onClose,
  onRefresh,
  type = 'create',
  plugin = {},
  gatewayType = 'API',
}: Props) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isValidateName, setIsValidateName] = useState(false);
  const [gatewayVersion, setGatewayVersion] = useState('arbitrary');
  const [maxVersion, setMaxVersion] = useState('');
  const field = Field.useField();
  const { init, setValue, setValues } = field;
  const disabled = type === 'edit';

  useEffect(() => {
    if (gatewayVersion === 'arbitrary') {
      setValue('supportedMinGatewayVersion', '');
    }
  }, [gatewayVersion]);

  useEffect(() => {
    if (type === 'edit') getPluginClass();
  }, [JSON.stringify(plugin)]);

  const getPluginClass = async () => {
    const data = await services.GetPluginClass({
      params: { pluginClassId: get(plugin, 'pluginClassId') },
    });
    if (!isEmpty(data)) {
      setValues({
        alias: get(data, 'alias', ''),
        name: get(data, 'name', ''),
        description: get(data, 'description', ''),
        wasmLanguage: get(data, 'wasmLanguage', ''),
        version: incrementVersion(get(plugin, 'version', '')),
      });
      const versions = map(
        [
          ...get(data, 'otherVersions', []),
          { pluginClassId: get(plugin, 'pluginClassId'), version: get(data, 'version') },
        ],
        (item) => item.version,
      );
      const sortedVersions = versions.sort((a, b) => {
        return getVersionCompare(b, a) ? 1 : -1;
      });
      // 从大到小排序
      const maxversion = sortedVersions[0]; // 获取最大版本
      setMaxVersion(maxversion);
    }
  };

  const incrementVersion = (version) => {
    let versionArr = split(version, '.') as any;
    versionArr[versionArr.length - 1] = toNumber(versionArr[versionArr.length - 1]) + 1;
    return join(versionArr, '.');
  };

  const onSubmit = () => {
    setIsProcessing(true);
    field.validate(async (errors, values: any) => {
      if (errors) {
        setIsProcessing(false);
        return;
      }
      try {
        const { pluginClassId } = await services.CreatePluginClass({
          content: {
            ...values,
            supportedMinGatewayVersion: get(values, 'supportedMinGatewayVersion', ''),
            gatewayType,
          },
        });
        if (pluginClassId) {
          onRefresh && onRefresh(pluginClassId);
          onClose();
        }
        setIsProcessing(false);
      } catch (error) {
        setIsProcessing(false);
      }
    });
  };

  return (
    <SlidePanel
      title={
        type === 'create'
          ? intl('apigw.components.plugin-manage.UploadCustomPlugin.PublishPlugIns')
          : intl('apigw.components.plugin-manage.UploadCustomPlugin.ReleaseVersion')
      }
      isProcessing={isProcessing}
      isShowing={visible}
      onClose={onClose}
      onCancel={onClose}
      okText={intl('apigw.components.plugin-manage.UploadCustomPlugin.Upload')}
      onOk={onSubmit}
      width={1080}
    >
      <Form field={field} {...layoutForm}>
        <h4 className="font-medium">
          {intl('apigw.components.plugin-manage.UploadCustomPlugin.PlugInInformation')}
        </h4>
        <Form.Item label={intl('apigw.components.plugin-manage.UploadCustomPlugin.SelectLanguage')}>
          <PluginLanguage
            {...(init('wasmLanguage', { initValue: 'TinyGo' }) as any)}
            disabled={disabled}
          />
        </Form.Item>
        <Form.Item
          label={intl('apigw.components.plugin-manage.UploadCustomPlugin.PlugInId')}
          required
        >
          <Input
            {...init('name', {
              rules: [
                {
                  required: true,
                  message: intl(
                    'apigw.components.plugin-manage.UploadCustomPlugin.EnterTheEnglishIdOf',
                  ),
                },
                {
                  pattern: regex,
                  message: intl(
                    'apigw.components.plugin-manage.UploadCustomPlugin.TheInputMustBeTo',
                  ),
                },
              ],
            })}
            disabled={disabled}
            placeholder={intl(
              'apigw.components.plugin-manage.UploadCustomPlugin.EnterTheEnglishIdOf',
            )}
            trim
          />
        </Form.Item>
        <Form.Item label={intl('@ali/widget-edas-microgw::widget.plugin.form.name')} required>
          <Input
            {...init('alias', {
              rules: [
                {
                  required: true,
                  message: intl(
                    'apigw.components.plugin-manage.UploadCustomPlugin.EnterTheNameOfThe',
                  ),
                },
              ],
            })}
            disabled={disabled}
            placeholder={intl(
              'apigw.components.plugin-manage.UploadCustomPlugin.EnterTheChineseNameOf',
            )}
            trim
          />
        </Form.Item>
        <Form.Item label={intl('@ali/widget-edas-microgw::widget.plugin.form.summary')}>
          <Input.TextArea
            {...init('description')}
            placeholder={intl(
              'apigw.components.plugin-manage.UploadCustomPlugin.EnterAPlugInDescription',
            )}
            rows={3}
            disabled={disabled}
          />
        </Form.Item>
        {/* <Form.Item label={'插件文档'}>
              <PluginUpload
                {...(init('docUrl') as any)}
                accept=".md,.markdown,.mdown,.markdn,.mdtxt,.mdwn,.mdx,.text"
                path="plugin/doc"
              />
             </Form.Item> */}
        <Divider />
        <h4 className="font-medium">
          {intl('apigw.components.plugin-manage.UploadCustomPlugin.VersionInformation')}
        </h4>
        <Form.Item label={intl('@ali/widget-edas-microgw::widget.service.fc.version')} required>
          <Input
            {...init('version', {
              initValue: '1.0.0',
              rules: [
                {
                  pattern:
                    /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/,
                  message: intl(
                    'apigw.components.plugin-manage.UploadCustomPlugin.SupportsNumbersAndPoints',
                    {
                      SemVer: <ExternalLink url="https://semver.org/" label="semver" />,
                    },
                  ),
                  trigger: ['onBlur', 'onChange'],
                },
                {
                  validator: (rule, value: any, callback) => {
                    if (!value) {
                      callback(
                        intl(
                          'apigw.components.plugin-manage.UploadCustomPlugin.PleaseFillInTheVersion',
                        ),
                      );
                    } else if (value && maxVersion && getVersionCompare(maxVersion, value)) {
                      callback(
                        intl(
                          'apigw.components.plugin-manage.UploadCustomPlugin.TheVersionNumberOfThe',
                          { _maxVersion: maxVersion },
                        ) as string,
                      );
                    } else {
                      callback();
                    }
                  },
                  trigger: ['onBlur'],
                },
              ],
            })}
            trim
          />
        </Form.Item>
        <Form.Item
          label={intl('apigw.components.plugin-manage.UploadCustomPlugin.VersionDescription')}
          required
        >
          <Input.TextArea
            {...init('versionDescription', {
              rules: [
                {
                  required: true,
                  message: intl(
                    'apigw.components.plugin-manage.UploadCustomPlugin.EnterAVersionDescription',
                  ),
                },
              ],
            })}
            placeholder={intl(
              'apigw.components.plugin-manage.UploadCustomPlugin.EnterAVersionDescription',
            )}
            rows={3}
          />
        </Form.Item>
        <Form.Item
          label={intl('apigw.components.plugin-manage.UploadCustomPlugin.WasmFile')}
          required
        >
          <PluginUpload
            {...(init('wasmUrl', {
              rules: [
                {
                  required: true,
                  message: intl(
                    'apigw.components.plugin-manage.UploadCustomPlugin.PleaseUploadTheWasmFile',
                  ),
                },
              ],
            }) as any)}
            accept=".wasm,application/wasm"
            onValidate={(file) => {
              return regex.test(file?.name);
            }}
            onProgress={() => {
              setIsProcessing(true);
            }}
            onSuccess={() => {
              setIsProcessing(false);
            }}
            onError={(file) => {
              setIsValidateName(!regex.test(file?.name));
              setIsProcessing(false);
            }}
            validateErrorMessage={
              isValidateName
                ? intl('apigw.components.plugin-manage.UploadCustomPlugin.TheUploadMustBeTo')
                : ''
            }
          />
        </Form.Item>

        <Form.Item label={intl('@ali/widget-edas-microgw::widget.plugin.execute_phase')} required>
          <Select
            {...init('executeStage', {
              rules: [
                {
                  required: true,
                  message: intl(
                    'apigw.components.plugin-manage.UploadCustomPlugin.SelectExecutionPhase',
                  ),
                },
              ],
            })}
            className="w-full"
            dataSource={map(UPLOAD_EXECUTE_STAGE, (value, key) => ({
              label: value,
              value: key,
            }))}
          ></Select>
        </Form.Item>
        <Form.Item
          label={intl('@ali/widget-edas-microgw::widget.plugin.execute_priority')}
          required
        >
          <NumberPicker {...init('executePriority')} min={0} />
        </Form.Item>
        <Form.Item
          label={
            <span>
              {intl('apigw.components.plugin-manage.UploadCustomPlugin.AdaptToTheGatewayVersion')}

              <Balloon
                trigger={<Icon type="help" className="ml-4" size={'small'} />}
                align="t"
                alignEdge
                triggerType="hover"
              >
                {intl(
                  'apigw.components.plugin-manage.UploadCustomPlugin.TheMinimumGatewayVersionAllowed',
                )}
              </Balloon>
            </span>
          }
          required
        >
          <Radio.Group
            aria-labelledby="groupId"
            value={gatewayVersion}
            onChange={(v: string) => setGatewayVersion(v)}
            className="align-center"
          >
            <Radio id="arbitrary" value="arbitrary">
              {intl('apigw.components.plugin-manage.UploadCustomPlugin.AnyVersion')}
            </Radio>
            <Radio id="assigned" value="assigned">
              {intl('apigw.components.plugin-manage.UploadCustomPlugin.SpecifiedVersion')}
            </Radio>
            {gatewayVersion === 'assigned' && (
              <Form.Item style={{ margin: 0, display: 'inline-block' }}>
                <Input
                  {...init('supportedMinGatewayVersion', {
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'apigw.components.plugin-manage.UploadCustomPlugin.EnterTheGatewayVersion',
                        ),
                      },
                      {
                        pattern: /^\d+\.\d+\.\d+$/,
                        message: intl(
                          'apigw.components.plugin-manage.UploadCustomPlugin.EnterTheCorrectGatewayVersion',
                        ),
                      },
                    ],
                  })}
                  placeholder={intl(
                    'apigw.components.plugin-manage.UploadCustomPlugin.EnterTheGatewayVersion',
                  )}
                  trim
                />
              </Form.Item>
            )}
          </Radio.Group>
        </Form.Item>
      </Form>
    </SlidePanel>
  );
};
