import { Copy, intl, Truncate } from '@ali/cnd';
import React from 'react';
import services from '~/utils/services';
import { Actions, Balloon, Button, Dialog, LinkButton, Message, MultiLines } from '@ali/cnd';
import {
  PluginResourceTypes,
  PluginResourceTypesMapper,
} from '../../shared/PluginCardList/PluginConfig';
import Status from '../../shared/Status';
import { get, split } from 'lodash';
import { base64Decode } from '~/utils';
import CodeMirrorEditor from '../../shared/CodeMirrorEditor';
import { NA } from '~/constants';
import ExternalLink from '~/components/shared/ExternalLink';
import PluginAttachExtendName from './PluginAttachExtendName';

const handleDeleteRule = ({ pluginAttachmentId, onRefresh }) => {
  Dialog.alert({
    title: intl('apigw.components.plugin-manage.PluginAttachTableProps.DetermineDeleteRule'),
    content: intl(
      'apigw.components.plugin-manage.PluginAttachTableProps.TheDeletionMayAffectOnline',
    ),
    footerAlign: 'right',
    onOk: () => {
      return new Promise<void>(async (resolve, reject) => {
        try {
          const { responseSuccess } = await services.DeletePluginAttachment({
            params: { pluginAttachmentId },
          });
          if (responseSuccess) {
            Message.success(
              intl('apigw.components.plugin-manage.PluginAttachTableProps.TheRuleHasBeenDeleted'),
            );
            onRefresh && onRefresh();
            resolve();
          }
        } catch (error) {
          reject();
        }
      });
    },
  });
};

const handleChangRuleEnable = ({
  pluginAttachmentId,
  onRefresh,
  enable,
  pluginConfig,
  attachResourceId,
}) => {
  Dialog.alert({
    title: enable
      ? intl('apigw.components.plugin-manage.PluginAttachTableProps.DetermineTheDeactivationRule')
      : intl('apigw.components.plugin-manage.PluginAttachTableProps.DetermineEnablingRules'),
    content: intl(
      'apigw.components.plugin-manage.PluginAttachTableProps.ThisModificationMayAffectOnline',
    ),
    footerAlign: 'right',
    onOk: () => {
      return new Promise<void>(async (resolve, reject) => {
        let data = {
          attachResourceIds: split(attachResourceId, ',').filter(Boolean),
          enable: !enable,
          pluginConfig,
        };
        try {
          const { responseSuccess } = await services.UpdatePluginAttachment({
            content: data,
            params: {
              pluginAttachmentId,
            },
          });
          if (responseSuccess) {
            Message.success(
              enable
                ? intl(
                    'apigw.components.plugin-manage.PluginAttachTableProps.TheRuleHasBeenDisabled',
                  )
                : intl('apigw.components.plugin-manage.PluginAttachTableProps.TheRuleIsEnabled'),
            );
            onRefresh && onRefresh();
            resolve();
          }
        } catch (error) {
          reject();
        }
      });
    },
  });
};

export const columns = ({
  setRefreshIndex,
  resourceType,
  gatewayType,
  setCurData,
  setRulePanelType,
  setShowConfig,
}) => {
  return [
    {
      title:
        gatewayType === 'AI' && resourceType === 'HttpApi'
          ? 'Model API'
          : PluginResourceTypesMapper[resourceType],
      dataIndex: 'attachResourceNames',
      cell: (v = [], i, record) => {
        return (
          <PluginAttachExtendName data={v} balloonTitle={PluginResourceTypesMapper[resourceType]} />
        );
      },
      width: 190,
    },
    resourceType === PluginResourceTypes.OperationAndRoute && {
      title: intl('apigw.plugin-manage.plugin-rule-config.PluginAttachTableProps.Api'),
      dataIndex: 'parentResourceInfo',
      cell: (value, i, record) => {
        const { apiInfo = {} } = value || {};
        return value ? (
          <Truncate type="length" threshold={10} align="t">
            {get(apiInfo, 'versionInfo.version')
              ? `${get(apiInfo, 'name', NA)} / ${get(apiInfo, 'versionInfo.version')}`
              : get(apiInfo, 'name', NA)}
          </Truncate>
        ) : (
          NA
        );
      },
      width: 100,
    },
    {
      title: intl('apigw.components.plugin-manage.PluginAttachTableProps.RuleContent'),
      dataIndex: 'pluginConfig',
      cell: (v = '', i, record) => {
        const pluginConfig = base64Decode(v);
        const mode = get(record, 'pluginClassInfo.mode');
        return (
          <Balloon
            trigger={
              <div>
                <MultiLines lines={4} align="t" showTooltip={false}>
                  <pre style={{ wordWrap: 'break-word', whiteSpace: 'pre-wrap' }}>
                    {pluginConfig}
                  </pre>
                </MultiLines>
              </div>
            }
            align="t"
            alignEdge
            triggerType="hover"
            needAdjust
            cache
            style={{ width: 500 }}
          >
            <CodeMirrorEditor
              value={pluginConfig}
              language={mode === 'Lua' ? 'lua' : 'yaml'}
              theme="monokai"
              readOnly
              lineWrapping
              height="300px"
            />
          </Balloon>
        );
      },
      width: 240,
    },
    {
      title: intl('apigw.components.plugin-manage.PluginAttachTableProps.EffectiveStatus'),
      dataIndex: 'enable',
      cell: (v, i, record) => {
        return (
          <Status
            value={v}
            dataSource={[
              {
                label: intl('apigw.components.plugin-manage.PluginAttachTableProps.Enabled'),
                value: true,
                iconType: 'check_fill',
                type: 'success',
              },
              {
                label: intl('apigw.components.plugin-manage.PluginAttachTableProps.NotEnabled'),
                value: false,
                type: 'warning',
              },
            ]}
          />
        );
      },
      width: 100,
    },
    {
      title: intl('apigw.components.plugin-manage.PluginAttachTableProps.Operation'),
      lock: 'right',
      width: 130,
      cell: (v, i, record) => (
        //@ts-ignore
        <Actions threshold={3}>
          <LinkButton
            onClick={() => {
              setCurData(record);
              setRulePanelType('edit');
              setShowConfig(true);
            }}
          >
            {intl('apigw.components.plugin-manage.PluginAttachTableProps.Edit')}
          </LinkButton>
          <LinkButton
            onClick={() =>
              handleChangRuleEnable({
                attachResourceId: get(record, 'attachResourceId'),
                enable: get(record, 'enable'),
                pluginConfig: get(record, 'pluginConfig'),
                pluginAttachmentId: get(record, 'pluginAttachmentId'),
                onRefresh: () => setRefreshIndex(+new Date()),
              })
            }
          >
            {get(record, 'enable')
              ? intl('apigw.components.plugin-manage.PluginAttachTableProps.Disable')
              : intl('apigw.components.plugin-manage.PluginAttachTableProps.Enable')}
          </LinkButton>
          {get(record, 'enable') ? (
            <Balloon
              trigger={
                <Button disabled text>
                  {intl('apigw.components.plugin-manage.PluginAttachTableProps.Delete')}
                </Button>
              }
              align="t"
              alignEdge
              needAdjust
              triggerType="hover"
            >
              {intl(
                'apigw.components.plugin-manage.PluginAttachTableProps.TheRuleConfigurationIsBeing',
              )}
            </Balloon>
          ) : (
            <LinkButton
              onClick={() =>
                handleDeleteRule({
                  pluginAttachmentId: record.pluginAttachmentId,
                  onRefresh: () => {
                    setRefreshIndex(+new Date());
                  },
                })
              }
            >
              {intl('apigw.components.plugin-manage.PluginAttachTableProps.Delete')}
            </LinkButton>
          )}
        </Actions>
      ),
    },
  ].filter(Boolean);
};

export const fetchData = async (params) => {
  const {
    current,
    pageSize,
    resourceType,
    gatewayId,
    pluginId,
    onRefreshRuleScopeCount,
    ...reset
  } = params;
  try {
    const { items = [], totalSize } = await services.ListPluginAttachments({
      params: {
        pageNumber: current,
        pageSize,
        attachResourceTypes:
          resourceType === PluginResourceTypes.OperationAndRoute
            ? 'Operation,GatewayRoute'
            : resourceType,
        // gatewayId,
        pluginId,
        ...reset,
      },
    });
    onRefreshRuleScopeCount(resourceType, totalSize);
    return {
      data: items,
      total: totalSize,
    };
  } catch (error) {
    return { data: [], total: 0 };
  }
};

export const search = {
  defaultDataIndex: 'nameLike',
  defaultSelectedDataIndex: 'nameLike',
  options: [
    {
      label: intl('apigw.components.plugin-manage.PluginAttachTableProps.GatewayId'),
      templateProps: {
        placeholder: intl(
          'apigw.components.plugin-manage.PluginAttachTableProps.EnterTheGatewayIdTo',
        ),
      },
      dataIndex: 'nameLike',
      template: 'input',
    },
  ],
};
