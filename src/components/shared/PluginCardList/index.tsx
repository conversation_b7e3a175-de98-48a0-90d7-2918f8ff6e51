import { intl } from '@ali/cnd';
import React, { useEffect, useState, useRef } from 'react';
import { SideMenu } from './SideMenu';
import { PluginList } from './PluginList';
import { filter, forEach, groupBy, includes, get, isEmpty, size } from 'lodash';
import { PluginCardProps } from './PluginCard';
import services from '~/utils/services';
import './index.less';
import { useScrollHook } from './scrollHook';
import { PLUGIN_TYPE } from '~/track/plugin';
import CachedData from '~/utils/cacheData';

export const PluginTypeMapper = {
  [PLUGIN_TYPE.AI]: 'AI',
  [PLUGIN_TYPE.Auth]: intl('apigw.shared.PluginCardList.Authentication'),
  [PLUGIN_TYPE.FlowControl]: intl('apigw.shared.PluginCardList.TrafficControl'),
  [PLUGIN_TYPE.TransformProtocol]: intl('apigw.shared.PluginCardList.TransportProtocol'),
  [PLUGIN_TYPE.Security]: intl('apigw.shared.PluginCardList.SecurityProtection'),
  [PLUGIN_TYPE.FlowObservation]: intl('apigw.shared.PluginCardList.FlowObservation'),
  [PLUGIN_TYPE.Other]: intl('apigw.shared.PluginCardList.Custom'),
};

export enum PluginPolicyDirection {
  InBound = 'InBound',
  OutBound = 'OutBound',
  Both = 'Both',
}

export interface PluginCardListProps extends PluginCardProps {
  /**
   * 是否展示顶部操作按钮
   */
  showHeaderActions?: boolean;
  /**
   * 刷新
   * @param refreshAPI:是否刷新API列表
   * @returns
   */
  onRefresh?: (refreshAPI?: any) => void;
  /**
   * 筛选参数
   */
  filterParams?: {};
  /**
   * 入站/出战 方向
   */
  direction?: PluginPolicyDirection;
  gatewayType?: string;
  excludeBuiltinAiProxy?: boolean;
}

export type PluginCardListTypes = {
  [key: string]: Array<any>;
};

export const getPluginConfig = (sceneType) => {
  let defaultPluginConfig = {
    source: ['HigressOfficial', 'HigressCommunity', 'Custom'],
  };
  if (sceneType !== 'plugin') return defaultPluginConfig;

  const pluginConfig = sessionStorage.getItem('pluginConfig');

  if (pluginConfig) {
    try {
      defaultPluginConfig = JSON.parse(pluginConfig);
    } catch (error) {}
  }
  return defaultPluginConfig;
};

export const PluginCardList = (props) => {
  const {
    showHeaderActions,
    filterParams = {},
    direction,
    sceneType,
    sourceProps,
    gatewayType = 'API',
    excludeBuiltinAiProxy = false,
  } = props;
  const [plugins, setPlugins] = useState<PluginCardListTypes>({});
  const [allPlugins, setAllPlugins] = useState([]);
  const [filterPlugins, setFilterPlugins] = useState<PluginCardListTypes>({});
  const [sourcePlugins, setSourcePlugins] = useState<PluginCardListTypes>({});
  const { activeSection, setActiveSection, scrollToSection, sectionRefMap, scrollableRef } =
    useScrollHook({
      headerHeight: 160,
    });
  const [hasPublishing, setHasPublishing] = useState(false);
  const refTime = useRef(null);
  const retryCountRef = useRef(0);

  useEffect(() => {
    getPluginList({ pageSize: 100, pageNumber: 1 });
  }, []);

  useEffect(() => {
    let pluginType = sessionStorage.getItem('pluginType');
    if (pluginType && !isEmpty(plugins)) {
      sessionStorage.removeItem('pluginType');
      setTimeout(() => {
        if (sectionRefMap?.[pluginType]?.current) {
          scrollToSection(pluginType);
          setActiveSection(pluginType);
        }
      }, 100);
    }
  }, [plugins]);

  useEffect(() => {
    return () => {
      clearInterval(refTime.current);
    };
  }, []);

  const clearTime = () => {
    if (refTime.current) clearInterval(refTime.current);
    refTime.current = null;
  };

  useEffect(() => {
    clearTime();
    if (hasPublishing) {
      refTime.current = setInterval(() => {
        retryCountRef.current += 1;
        getPluginList({ pageSize: 100, pageNumber: 1 });
        if (retryCountRef.current > 20) {
          clearTime();
        }
      }, 5000);
    }
  }, [hasPublishing]);

  const setData = (data) => {
    const pluginConfig = getPluginConfig(sceneType);
    const pluginGrouped = groupBy(data, 'type');
    // TODO：暂未去重复的自定义插件 （多版本相同自定义插件）
    const pluginSourceGrouped = groupBy(data, 'source');
    const pluginFilterGrouped = onFilterPlugins(pluginGrouped, pluginConfig);
    const _hasPublishing =
      size(
        filter(data, (item) => {
          return item.publishStatus === 'Publishing';
        }),
      ) > 0;
    setHasPublishing(_hasPublishing);
    setPlugins(pluginGrouped);
    setFilterPlugins(pluginFilterGrouped);
    setSourcePlugins(pluginSourceGrouped);
  };

  const getPluginList = async (params) => {
    const { items = [] } = await services.ListPluginClasses({
      params: {
        ...params,
        ...filterParams,
        direction,
        gatewayType,
        ...(excludeBuiltinAiProxy ? { excludeBuiltinAiProxy } : {}),
      },
    });
    const attachResourceType = get(sourceProps, 'attachResourceType');

    const _items = items.filter(
      (item) => !(attachResourceType === 'HttpApi' && item.name === 'lua'),
    );
    setAllPlugins(_items);
    setData(_items);
  };

  const onFilterPlugins = (plugins, filterParams) => {
    let filteredPlugins = {};
    forEach(plugins, (values, key) => {
      const data = filter(values, (item) => {
        return includes(filterParams.source, item.source);
      });
      //TODO: 没有版本切换功能之前，只展示最新版本的插件
      const pluginsWithLatestVersion = groupBy(data, 'name');
      filteredPlugins[key] = Object.keys(pluginsWithLatestVersion).map((name) => {
        //返回version最大的插件
        return pluginsWithLatestVersion[name][pluginsWithLatestVersion[name].length - 1];
      });
      // filteredPlugins[key] = data;
    });
    return filteredPlugins;
  };

  const onSidebarSearch = (_params) => {
    sceneType === 'plugin' && sessionStorage.setItem('pluginConfig', JSON.stringify(_params));
    setFilterPlugins(onFilterPlugins(plugins, _params));
  };

  const onTopSearch = (p) => {
    getPluginList({ ...p, pageSize: 100 });
  };

  const onTopFilter = (filterValue) => {
    //查找name和alias都包含搜索值的数据，不区分大小写
    let newData = [];
    if (filterValue) {
      const searchValue = filterValue.toLowerCase();
      forEach(allPlugins, (item) => {
        if (
          includes(item.name.toLowerCase(), searchValue) ||
          includes(item.alias.toLowerCase(), searchValue)
        ) {
          newData.push(item);
        }
      });
    } else {
      newData = allPlugins;
    }
    setData(newData);
  };

  return (
    <div className="flex">
      <div className="w-[200px]">
        <SideMenu
          activeSection={activeSection}
          scrollToSection={scrollToSection}
          filterPlugins={filterPlugins}
          sourcePlugins={sourcePlugins}
          sceneType={sceneType}
          onSearch={onSidebarSearch}
        />
      </div>
      <div className="ml-16 flex-1">
        <PluginList
          scrollableRef={scrollableRef}
          sectionRefMap={sectionRefMap}
          showHeaderActions={showHeaderActions}
          data={filterPlugins}
          onPressSearch={onTopSearch}
          onTopFilter={onTopFilter}
          gatewayType={gatewayType}
          {...props}
        />
      </div>
    </div>
  );
};
