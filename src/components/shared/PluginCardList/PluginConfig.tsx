import { intl } from '@ali/cnd';
import { Field, Form, Switch, Button } from '@ali/cnd';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { get, includes, split } from 'lodash';
import { Align } from '~/constants/formLayout';
import { base64Decode } from '~/utils';
import Status from '../Status';
import { YamlOrJsonEditor } from '../YamlOrJsonEditor';
import PluginDocumentPanel from '~/components/plugin-manage/PluginDocumentPanel';
import OperationAndRouteTransfer from '~/components/plugin-manage/plugin-transfer/OperationAndRouteTransfer';
import DomainTransfer from '~/components/plugin-manage/plugin-transfer/DomainTransfer';
import ApiTransfer from '~/components/plugin-manage/plugin-transfer/ApiTransfer';
import PluginEnvSelect from '~/components/plugin-manage/plugin-rule-config/PluginEnvSelect';
import PluginApiSelect from '~/components/plugin-manage/plugin-rule-config/PluginApiSelect';
import ApiVersionPreview from '~/components/consumer-manage/consumer-auth/ApiVersionPreview';
import CodeMirrorEditor from '../CodeMirrorEditor';
import { PLUGIN_ATTACH_TYPE } from '~/constants/plugin';

const formLayout = {
  labelCol: {
    span: 3,
  },
  wrapperCol: {
    span: 21,
  },
  labelTextAlign: 'left' as Align,
};

/**
 * 可挂载插件资源类型
 */
export enum PluginResourceTypes {
  Gateway = 'Gateway',
  Domain = 'GatewayDomain',
  Api = 'HttpApi',
  LLMApi = 'LLMApi',
  GatewayRoute = 'GatewayRoute',
  OperationAndRoute = 'OperationAndRoute',
}
export const PluginResourceTypesMapper = {
  [PluginResourceTypes.Gateway]: intl('mse.selfTroubleshooting.instance.label'),
  [PluginResourceTypes.Domain]: intl('@ali/widget-edas-microgw::widget.defense.domain'),
  [PluginResourceTypes.Api]: 'REST API',
  [PluginResourceTypes.OperationAndRoute]: intl(
    'apigw.shared.PluginCardList.PluginConfig.InterfaceRoute',
  ),
  [PluginResourceTypes.LLMApi]: 'Model API',
  [PluginResourceTypes.GatewayRoute]: intl(
    'apigw.shared.PluginCardList.PluginConfig.McpRoutingLevel',
  ),
};

export const PluginResourceTypesScope = {
  [PluginResourceTypes.Gateway]: intl('apigw.shared.PluginCardList.PluginConfig.InstanceLevel'),
  [PluginResourceTypes.Domain]: intl('apigw.shared.PluginCardList.PluginConfig.DomainLevel'),
  [PluginResourceTypes.OperationAndRoute]: intl(
    'apigw.shared.PluginCardList.PluginConfig.InterfaceRouteLevel',
  ),
  [PluginResourceTypes.Api]: intl('apigw.shared.PluginCardList.PluginConfig.ApiLevel'),
  [PluginResourceTypes.LLMApi]: intl('apigw.shared.PluginCardList.PluginConfig.ApiLevel'),
  [PluginResourceTypes.GatewayRoute]: intl('apigw.shared.PluginCardList.PluginConfig.RoutingLevel'),
};

export interface PluginTargetInfo {
  /**
   * 目标名称
   */
  name?: string;
  /**
   * 目标资源ID
   */
  attachResourceId?: string;
  /**
   * 目标资源类型
   */
  attachResourceType?: string;
  attachResourceName?: string;
  attachResourceNames?: string[];
  /**
   * 接口方法，仅Operation具有
   */
  method?: string;
  gatewayId?: string;
  environmentId?: string;
  gatewayVersion?: string;
}

export interface PluginConfigProps {
  /**
   * 插件配置信息 用于回显
   */
  curData?: any;
  /**
   * 生效目标信息，批量配置时资源id为网关Id
   */
  sourceProps?: PluginTargetInfo;
  resourceType?: string;
  gatewayId?: string;
  [key: string]: any;
}

const PluginConfig = forwardRef((props: PluginConfigProps, ref) => {
  const {
    curData,
    resourceType,
    sourceProps,
    type = 'create',
    plugin,
    gatewayType = 'API',
  } = props;
  const [showDocument, setShowDocument] = useState(false);
  const field = Field.useField();
  const { init, setValues, getValue } = field;
  useEffect(() => {
    const _values = {
      enable: get(curData, 'enable', false),
      pluginConfig: base64Decode(
        get(curData, 'pluginConfig', get(curData, 'pluginInfo.pluginConfig', '')),
      ),
      attachResourceIds: split(get(curData, 'attachResourceId', ''), ',').filter(Boolean),
      apiId: get(curData, 'parentResourceInfo.apiInfo.httpApiId', ''),
      environmentInfo: get(curData, 'environmentInfo'),
    } as any;

    if (includes(['Operation', 'GatewayRoute'], get(curData, 'attachResourceType'))) {
      _values.apiType = get(curData, 'attachResourceType') === 'Operation' ? 'Rest' : 'Http';
    }

    if (type === 'create' && !get(_values, 'pluginConfig')) {
      _values.pluginConfig = get(plugin, 'configExample');
    }
    setValues(_values);
  }, [JSON.stringify(curData)]);

  const configuredList = useMemo(() => {
    const result = get(curData, 'resourceInfos', []).map((item) => {
      let label = get(item, 'resourceName');

      if (get(curData, 'attachResourceType') === PLUGIN_ATTACH_TYPE.GatewayRoute) {
        const names = label.split('@') || [];
        if (names.length > 2) {
          //设置为2减少路由本身带@的可能性
          label = `${names[names.length - 1]}`;
        }
      }
      return {
        label,
        value: get(item, 'resourceId'),
        version:
          get(item, 'resourceVersion') ||
          intl('apigw.shared.PluginCardList.PluginConfig.InitializeVersion'),
      };
    }) as any[];
    return result;
  }, [JSON.stringify(curData)]);

  const apiId = useMemo(() => {
    return getValue('apiId');
  }, [JSON.stringify(getValue('apiId'))]);

  useImperativeHandle(ref, () => ({
    getField: () => field,
  }));

  return (
    <>
      <Form field={field} {...formLayout} size="small">
        <Form.Item label={intl('apigw.shared.PluginCardList.SideMenu.EnableStatus')}>
          <div className="align-center">
            <Switch
              {...init('enable', {
                valueName: 'checked',
              })}
              size="small"
            />

            {includes([PluginResourceTypes.Gateway], resourceType) && (
              <div className="ml-4 plugin-config-status">
                <span className="mr-8">
                  {intl('apigw.shared.PluginCardList.PluginConfig.CurrentStatus')}
                </span>
                <Status
                  value={get(curData, 'enable', false)}
                  dataSource={[
                    {
                      label: intl('apigw.shared.PluginCardList.PluginConfig.Effective'),
                      value: true,
                      iconType: 'check_fill',
                      type: 'success',
                    },
                    {
                      label: intl('apigw.shared.PluginCardList.PluginConfig.NotEffective'),
                      value: false,
                      type: 'minus_fill',
                    },
                  ]}
                />
                )
              </div>
            )}

            {includes(
              [
                PluginResourceTypes.Gateway,
                PluginResourceTypes.Api,
                PluginResourceTypes.LLMApi,
                PluginResourceTypes.Domain,
                PluginResourceTypes.OperationAndRoute,
                PluginResourceTypes.GatewayRoute,
              ],

              resourceType,
            ) && (
              <Button text onClick={() => setShowDocument(true)} type="primary" className="ml-8">
                {intl('apigw.shared.PluginCardList.PluginConfig.ViewDocuments')}
              </Button>
            )}
          </div>
        </Form.Item>
        {resourceType && (
          <Form.Item label={intl('apigw.shared.PluginCardList.PluginConfig.EffectiveScope')}>
            <span>{PluginResourceTypesScope[resourceType]}</span>
          </Form.Item>
        )}

        {PluginResourceTypes.OperationAndRoute === resourceType &&
          getValue('apiType') === 'Rest' && (
            <PluginEnvSelect
              type={type}
              field={field}
              gatewayInfo={{
                gatewayId: get(sourceProps, 'gatewayId'),
                name: get(sourceProps, 'name'),
              }}
              gatewayType={gatewayType}
            />
          )}

        {/* 接口/路由规则 API列表 */}
        {resourceType === PluginResourceTypes.OperationAndRoute &&
          (type === 'edit' ? (
            <ApiVersionPreview
              currentApiInfo={get(curData, 'parentResourceInfo.apiInfo', {})}
              apiName={get(curData, 'parentResourceInfo.apiInfo.name', '')}
              {...{
                labelCol: {
                  span: 3,
                },
              }}
            />
          ) : (
            <PluginApiSelect
              {...{
                gatewayId: get(sourceProps, 'gatewayId'),
                type,
                field,
                gatewayType,
                resourceType: get(sourceProps, 'attachResourceType'),
              }}
            />
          ))}

        {resourceType === PluginResourceTypes.GatewayRoute && (
          <PluginApiSelect
            {...{
              gatewayId: get(sourceProps, 'gatewayId'),
              type,
              field,
              gatewayType,
              resourceType: get(sourceProps, 'attachResourceType'),
            }}
          />
        )}

        {/* 接口/路由规则、API规则 环境列表 (只有rest api 才显示) */}
        {/* 所属实例 */}
        {includes([PluginResourceTypes.Api, PluginResourceTypes.LLMApi], resourceType) && (
          <PluginEnvSelect
            type={type}
            field={field}
            gatewayInfo={{
              gatewayId: get(sourceProps, 'gatewayId'),
              name: get(sourceProps, 'name'),
            }}
            gatewayType={gatewayType}
          />
        )}

        {includes(
          [
            PluginResourceTypes.OperationAndRoute,
            PluginResourceTypes.Domain,
            PluginResourceTypes.Api,
            PluginResourceTypes.LLMApi,
            PluginResourceTypes.GatewayRoute,
          ],
          resourceType,
        ) && (
          <Form.Item
            label={intl('apigw.shared.PluginCardList.PluginConfig.EffectiveTarget')}
            required
          >
            {includes(
              [PluginResourceTypes.OperationAndRoute, PluginResourceTypes.GatewayRoute],
              resourceType,
            ) && (
              <OperationAndRouteTransfer
                {...(init('attachResourceIds', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'apigw.shared.PluginCardList.PluginConfig.TheEffectiveTargetCannotBe',
                      ),
                    },
                  ],
                }) as any)}
                type={type}
                apiId={apiId}
                environmentId={get(getValue('environmentInfo'), 'environmentId')}
                apiType={getValue('apiType')}
                configuredList={configuredList}
                pluginAttachmentId={get(curData, 'pluginAttachmentId')}
                pluginId={get(plugin, 'pluginId')}
                gatewayId={get(sourceProps, 'gatewayId')}
                resourceType={resourceType}
              />
            )}

            {resourceType === PluginResourceTypes.Domain && (
              <DomainTransfer
                {...init('attachResourceIds', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'apigw.shared.PluginCardList.PluginConfig.TheEffectiveTargetCannotBe',
                      ),
                    },
                  ],
                })}
                configuredDomain={configuredList}
                gatewayType={gatewayType}
                gatewayId={get(sourceProps, 'gatewayId')}
              />
            )}

            {includes([PluginResourceTypes.Api, PluginResourceTypes.LLMApi], resourceType) && (
              <ApiTransfer
                {...init('attachResourceIds', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'apigw.shared.PluginCardList.PluginConfig.TheEffectiveTargetCannotBe',
                      ),
                    },
                  ],
                })}
                configuredApi={configuredList}
                gatewayId={get(sourceProps, 'gatewayId')}
                environmentId={get(getValue('environmentInfo'), 'environmentId')}
                pluginAttachmentId={get(curData, 'pluginAttachmentId')}
                pluginId={get(plugin, 'pluginId')}
                type={type}
                gatewayType={gatewayType}
              />
            )}
          </Form.Item>
        )}

        <Form.Item label={intl('apigw.shared.PluginCardList.PluginConfig.PlugInRules')}>
          {get(plugin, 'mode', get(plugin, 'pluginClassInfo.mode')) === 'Lua' ? (
            <CodeMirrorEditor language="lua" {...(init('pluginConfig') as any)} />
          ) : (
            <YamlOrJsonEditor {...(init('pluginConfig') as any)} theme={'monokai'} />
          )}
        </Form.Item>
      </Form>

      {showDocument && (
        <PluginDocumentPanel
          visible={showDocument}
          onClose={() => {
            setShowDocument(false);
          }}
          plugin={plugin}
        />
      )}
    </>
  );
});

export default PluginConfig;
