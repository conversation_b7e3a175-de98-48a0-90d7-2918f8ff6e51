import { Button, Icon, Select, intl, Balloon } from '@ali/cnd';
import React, { useState } from 'react';
import CustomInput from './CustomInput';
import { union } from 'lodash';
import './index.less';

interface CustomSelectProps {
  value?: any;
  onChange?: (value: any) => void;
  dataSource?: any[];
  isSlidePanelOpen?: boolean;
  [key: string]: any;
}

const CustomSelect = (props: CustomSelectProps) => {
  const footertext = intl('apigw.shared.PluginCardList.Custom');
  const { value, onChange, dataSource = [], isSlidePanelOpen = false, footerBtnText = footertext, ...restProps } = props;
  const [customData, setCustomData] = useState([]);
  const [isAddCustom, setIsAddCustom] = useState(false);
  const [visible, setVisible] = useState(false);
  const onSubmit = (value) => {
    setCustomData(union([value], customData));
    onChange(value);
    setIsAddCustom(false);
  };

  const menuProps = {
    footer: (
      <div className="custom-select-action-footer">
        {!isAddCustom ? (
          <Button type="primary" text onClick={() => setIsAddCustom(true)}>
            <Icon type="add" className="mr-8" />
            {footerBtnText}
          </Button>
        ) : (
          <CustomInput onSubmit={onSubmit} />
        )}
      </div>
    ),
  };

  return (
    <Select
      value={value}
      visible={visible}
      onVisibleChange={(v: boolean, type?: string) => {
        if (isSlidePanelOpen && type === 'docClick' && !v) return;
        setVisible(v);
      }}
      onChange={onChange}
      itemRender={props?.renderInstanceItem}
      className="full-width"
      popupClassName={`custom-select`}
      dataSource={[...customData, ...dataSource]}
      menuProps={menuProps}
      // @ts-ignore
      popupAutoFocus={true}
      {...restProps}
    />
  );
};

export default CustomSelect;
