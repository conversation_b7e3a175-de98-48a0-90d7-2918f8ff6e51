import React, { useState, useRef, useEffect } from 'react';
import services from '~/utils/services';
import {
  Actions,
  Button,
  Copy,
  Icon,
  intl,
  Link,
  Truncate,
  Tag,
  Balloon,
  LinkButton,
  LinkMore,
  Select,
  useParams,
} from '@ali/cnd';
import { NA } from '~/constants';
import { first, get, isEmpty, size, map, filter, includes, find } from 'lodash';
import { ApiTypeInfo, CREATE_API_TYPE } from '../createApi';
import ExtendBalloon from '~/components/shared/ExtendBalloon';
import DeleteApiAction from './DeleteApiAction';
import { API_PUBLISH_STATUS, AI_API_BACKENDSCENE } from '~/constants/apiManage';
import ExternalLink from '~/components/shared/ExternalLink';
import { AGENT_PROTOCOLS } from '../createApi/create-actions/components/ProtocolForm';
import {
  CREATE_API_MODEL_CATEGORY,
  ModelProtocolInfo,
} from '../createApi/create-actions/components/ProtocolSelect/options';
import { SCENE_LIST } from '../createApi/create-actions/CreateModelApiSidePanel/SceneDialog';
import PubLishStatusText from '~/components/ai-gateway/components/PublishStatus';

const { Group: TagGroup } = Tag;

export const CustomDomain = ({ domain }) => {
  const [showButton, setShowButton] = useState(false);
  const divRef = useRef(null);

  useEffect(() => {
    if (divRef.current && divRef.current.parentElement) {
      const parentWidth = divRef.current.parentElement.offsetWidth;
      const childWidth = divRef.current.offsetWidth;
      setShowButton(childWidth > parentWidth);
    }
  }, [JSON.stringify(domain)]);
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <div
        style={{
          overflow: 'hidden',
          display: 'inline-flex',
          width: size(domain) == 1 ? '100%' : 'calc(100% - 80px)',
        }}
      >
        <div ref={divRef}>
          <TagGroup
            style={{
              // height: '24px',
              width: 'auto',
              display: 'inline-flex',
            }}
          >
            {map(domain, (item) => {
              return (
                <Tag type="normal" key={item.domainId} style={{ margin: ' 0 2px' }}>
                  <Copy
                    showIcon
                    text={item.name}
                    icon={
                      <Icon
                        type="copy"
                        className="color-333"
                        size="small"
                        style={{ fontSize: 12 }}
                      />
                    }
                    truncateProps={{
                      type: 'width',
                      threshold: size(domain) == 1 ? 176 : showButton ? 120 : 210,
                      popupStyle: { wordBreak: 'break-all' },
                    }}
                  >
                    {item.name}({item.protocol})
                  </Copy>
                </Tag>
              );
            })}
          </TagGroup>
        </div>
      </div>
      {size(domain) > 1 && showButton && (
        <Balloon
          align="tl"
          title={
            <span>
              {intl('apigw.api-manage.apiList.ApiListTableProps.DomainName')} ({domain.length})
            </span>
          }
          trigger={
            <Button text type="primary" className="ml-6">
              {intl('apigw.api-manage.apiList.ApiListTableProps.ViewAll')}({domain.length})
            </Button>
          }
          closable={false}
          // @ts-ignore
          style={{ width: 400 }}
        >
          <TagGroup>
            {map(domain, (item) => {
              return (
                <Tag type="normal" key={item.domainId} style={{ margin: '2px' }}>
                  <Copy
                    showIcon
                    text={item.name}
                    icon={
                      <Icon
                        type="copy"
                        className="color-333"
                        size="small"
                        style={{ fontSize: 12 }}
                      />
                    }
                  >
                    {item.name}({item.protocol})
                  </Copy>
                </Tag>
              );
            })}
          </TagGroup>
        </Balloon>
      )}
    </div>
  );
};

// export const ApiVersion = ({ record, versionList, value }) => {
//   const [versionApiId, setVersionApiId] = useState(versionList.length ? versionList[0].value : '');
//   const { id: gatewayId } = useParams<any>();
//   const jumpUrl = gatewayId ? `/${window.regionId}/gateway/${gatewayId}` : `/${window.regionId}/api-manage`;
//   const typePaths = {
//     [CREATE_API_TYPE.HTTP]: `${jumpUrl}/api-http/${versionApiId}/router?region=${window.regionId}`,
//     [CREATE_API_TYPE.REST]: `${jumpUrl}/api-rest/${value}/${versionApiId}?region=${window.regionId}&tabKey=InterfaceList`,
//     [CREATE_API_TYPE.HTTP_INGRESS]: `${jumpUrl}/api-ingress/${versionApiId}/router?region=${window.regionId}`,
//     [CREATE_API_TYPE.WEB_SOCKET]: `${jumpUrl}/api-websocket/${versionApiId}/router?region=${window.regionId}`,
//     [CREATE_API_TYPE.AI]: `${jumpUrl}/api-ai/${versionApiId}?region=${window.regionId}`,
//   };
//   return (
//     <>
//     {/* @ts-ignore */}
//      <Link to={typePaths[record.type]}>
//       <Copy text={value} showIconTooltip truncateProps={{ type: 'width', threshold: 160 }}>
//         {value}
//       </Copy>
//     </Link>
//     {record.versionEnabled && <Select
//       size="small"
//       value={versionApiId}
//       className='api-manage-list-version-select'
//       onChange={(value) => {
//         setVersionApiId(value);
//       }}
//       dataSource={versionList}
//       />}
//     </>
//   );
// };

export const apiUrl = ({
  gatewayId,
  apiId,
  restApiName,
  type,
  apiFromOut,
  gatewayInfo,
  inGatewayId,
  isOutLink,
}: any) => {
  const jumpUrl = apiFromOut
    ? `/${window.regionId}/api-manage`
    : gatewayId
      ? `/${window.regionId}/gateway/${gatewayId}`
      : `/${window.regionId}/api-manage`;
  const typePaths = {
    [CREATE_API_TYPE.HTTP]: `${jumpUrl}/api-http/${apiId}/router?${
      apiFromOut || inGatewayId ? `&inGatewayId=${gatewayId || inGatewayId}` : ''
    }&region=${window.regionId}`,
    [CREATE_API_TYPE.REST]: `${jumpUrl}/api-rest/${restApiName}/${apiId}?${
      apiFromOut || inGatewayId ? `&inGatewayId=${gatewayId || inGatewayId}` : ''
    }&region=${window.regionId}&tabKey=InterfaceList`,
    [CREATE_API_TYPE.HTTP_INGRESS]: `${jumpUrl}/api-ingress/${apiId}/router?region=${window.regionId}`,
    [CREATE_API_TYPE.WEB_SOCKET]: `${jumpUrl}/api-websocket/${apiId}/router?${
      apiFromOut || inGatewayId ? `&inGatewayId=${gatewayId || inGatewayId}` : ''
    }&region=${window.regionId}`,
    [CREATE_API_TYPE.AI]: `${jumpUrl}/api-ai/${apiId}?region=${window.regionId}`,
    [CREATE_API_TYPE.LLM]: `/${window.regionId}/ai-gateway/${get(gatewayInfo, 'gatewayId', gatewayId)}/model-api/${apiId}?region=${window.regionId}`,
    [CREATE_API_TYPE.Agent]: `/${window.regionId}/ai-gateway/${get(gatewayInfo, 'gatewayId', gatewayId)}/agent-api/${apiId}?region=${window.regionId}`,
  };
  return isOutLink ? `/#${typePaths[type]}` : typePaths[type];
};

export const ApiOutJunp = ({
  apiId,
  gatewayId,
  type = 'edit',
  restApiName,
  apiFromOut,
  inGatewayId,
  apiType,
}) => {
  return (
    <span>
      <span>{intl('apigw.api-manage.apiList.ApiListTableProps.ThisApiIsCreatedWith')}</span>
      <ExternalLink
        label={intl('apigw.api-manage.apiList.ApiListTableProps.ListOfRealExceptionApis')}
        url={apiUrl({
          gatewayId,
          apiId,
          restApiName,
          type: apiType,
          apiFromOut,
          inGatewayId,
          isOutLink: true,
        })}
        icon={false}
      />

      <span>
        {type === 'edit'
          ? intl('widget.common.edit')
          : intl('@ali/widget-edas-microgw::widget.migrate.action_view')}
      </span>
    </span>
  );
};
export const columns = ({
  setRefreshIndex,
  handleOpenAction,
  tabKey,
  handleOpenAIDebug,
  gatewayInfo,
  gatewayId,
  inGatewayId,
  apiScene,
}) => {
  let ary = [
    {
      key: 'name',
      title: intl('apigw.api-manage.apiList.ApiListTableProps.ApiName'),
      dataIndex: 'name',
      cell: (value, index, record) => {
        // const versionList = map(record.versionedHttpApis, item => {
        //   return {
        //     ...item,
        //     label: get(item, 'versionInfo.version') || intl('apigw..InitialVersion'),
        //     value: item.httpApiId,
        //   };
        // });
        let firstVersions: any = first(record.versionedHttpApis);
        let apiId = firstVersions?.httpApiId;
        const firstDeployConfig: any = first(get(firstVersions, 'deployConfigs') || []) || {};
        const environments = get(firstVersions, 'environments', []);
        const _environment = find(environments, {
          environmentId: firstDeployConfig.environmentId,
        });
        const deployStatus = get(_environment, 'deployStatus');
        const isPageEdit = gatewayId && !record.gatewayId && record.type !== CREATE_API_TYPE.AI;
        return (
          <div className="align-start  ml-8">
            {/* @ts-ignore */}
            <Link
              to={apiUrl({
                gatewayId,
                apiId,
                restApiName: record.name,
                type: record.type,
                apiFromOut: isPageEdit,
                gatewayInfo,
                inGatewayId,
              })}
              // @ts-ignore
              target={isPageEdit ? '_blank' : '_self'}
            >
              <Copy text={value} showIconTooltip truncateProps={{ type: 'width', threshold: 160 }}>
                <ExtendBalloon trigger={value} isShow={isPageEdit}>
                  {isPageEdit && (
                    <ApiOutJunp
                      apiId={apiId}
                      restApiName={record.name}
                      inGatewayId={inGatewayId}
                      type="review"
                      gatewayId={gatewayId}
                      apiType={record.type}
                      apiFromOut={isPageEdit}
                    />
                  )}
                </ExtendBalloon>
                {isPageEdit && (
                  <ExternalLink
                    label={''}
                    url={apiUrl({
                      gatewayId,
                      apiId,
                      restApiName: record.name,
                      type: record.type,
                      apiFromOut: isPageEdit,
                      inGatewayId,
                      isOutLink: true,
                    })}
                  />
                )}
              </Copy>
            </Link>
            {includes(
              [CREATE_API_TYPE.AI, CREATE_API_TYPE.LLM, CREATE_API_TYPE.Agent],
              record.type,
            ) && (
              <PubLishStatusText
                deployStatus={deployStatus}
                deployErrorMessage={get(_environment, 'deployErrorMessage')}
              />
            )}
          </div>
        );
      },
      lock: 'left',
      width: 270,
    },
    {
      key: 'modelCategory',
      title: intl('apigw.api-operations-slide.api-definition-info.BasicTableProps.Type'),
      dataIndex: 'modelCategory',
      cell: (value, index, record) => {
        let modelCategory = get(record, 'versionedHttpApis[0].modelCategory');
        return get(
          find(SCENE_LIST, { type: modelCategory }),
          'title',
          intl('apigw.components.SceneDialog.TextGeneration'),
        );
      },
      width: 150,
      visible: apiScene === 'LLM',
    },
    {
      key: 'type',
      title: intl('apigw.api-manage.apiList.ApiListTableProps.ApiType'),
      dataIndex: 'type',
      cell: (value, index, record) => {
        const typeInfo = ApiTypeInfo[value] || {};
        return isEmpty(typeInfo) ? (
          NA
        ) : (
          <div className="align-center">
            <img src={typeInfo.icon} width={18} alt="" />
            <div className="ml-8">{typeInfo.title}</div>
          </div>
        );
      },
      width: 100,
      visible: tabKey === 'all',
    },
    // {
    //   key: 'aiProtocols',
    //   title: intl('apigw.api-manage.apiList.ApiListTableProps.Agreement'),
    //   dataIndex: 'aiProtocols',
    //   cell: (value, index, record) => {
    //     let aiProtocols = get(record, 'versionedHttpApis[0].aiProtocols') || [];
    //     if (isEmpty(aiProtocols)) return NA;
    //     return (
    //       <div>
    //         {map(aiProtocols, (item) => (
    //           <div>{item}</div>
    //         ))}
    //       </div>
    //     );
    //   },
    //   width: 100,
    //   visible: tabKey === 'ai',
    // },
    {
      key: 'agentProtocols',
      title: intl('apigw.components.AgentService.Agreement'),
      dataIndex: 'agentProtocols',
      cell: (value, index, record) => {
        const agentProtocols = get(record, 'versionedHttpApis[0].agentProtocols') || [];
        return !isEmpty(agentProtocols)
          ? get(find(AGENT_PROTOCOLS, { value: get(agentProtocols, '[0]') }), 'label', NA)
          : NA;
      },
      width: 225,
      visible: includes([CREATE_API_TYPE.Agent], tabKey),
    },
    {
      key: 'customDomainInfos',
      title: intl('apigw.api-manage.apiList.ApiListTableProps.DomainName'),
      dataIndex: 'customDomainInfos',
      cell: (value, index, record) => {
        let customDomainInfos =
          get(record, 'versionedHttpApis[0].deployConfigs[0].customDomainInfos') || [];
        return (
          <div>
            {!customDomainInfos.length && NA}
            {!!customDomainInfos.length && <CustomDomain domain={customDomainInfos} />}
          </div>
        );
      },
      width: 225,
      visible: includes(
        [CREATE_API_TYPE.AI, CREATE_API_TYPE.LLM, CREATE_API_TYPE.Agent, 'ai'],
        tabKey,
      ),
    },
    {
      key: 'subDomains',
      title: (
        <span>
          {intl('apigw.api-manage.apiList.ApiListTableProps.InstanceAccessPortalDebuggingOnly')}

          <Balloon
            align="t"
            trigger={
              <Icon
                type="info"
                className="color-333 ml-2"
                size="small"
                style={{ fontSize: 10, cursor: 'pointer' }}
              />
            }
            closable={false}
            // @ts-ignore
            style={{ width: 400 }}
          >
            <div>
              {apiScene === 'LLM'
                ? intl('apigw.api-manage.apiList.ApiListTableProps.TheInstanceAccessPortalIs.1')
                : intl('apigw.api-manage.apiList.ApiListTableProps.TheInstanceAccessPortalIs')}
            </div>
          </Balloon>
        </span>
      ),

      dataIndex: 'subDomains',
      cell: (value, index, record) => {
        let firstVersions: any = first(record.versionedHttpApis);
        let subDomains = get(firstVersions, 'deployConfigs[0].subDomains', []);
        let domain = map(subDomains, (item) => ({
          domain: item.name,
          domainId: item.domainId,
          protocol: item.protocol,
          networkType:
            item.networkType === 'Internet'
              ? intl('apigw.apiDetails.publishDetail.tableProps.PublicNetwork')
              : intl('apigw.apiDetails.publishDetail.tableProps.PrivateNetwork'),
        }));
        return (
          <TagGroup>
            {map(domain, (item) => {
              return (
                <Tag type="normal" key={item.domainId} style={{ margin: '2px' }}>
                  <Copy
                    showIcon
                    text={item.domain}
                    icon={
                      <Icon
                        type="copy"
                        className="color-333"
                        size="small"
                        style={{ fontSize: 12 }}
                      />
                    }
                    truncateProps={{
                      type: 'width',
                      threshold: 176,
                      popupStyle: { wordBreak: 'break-all' },
                    }}
                  >
                    {item.domain}({`${item.protocol} ${item.networkType}`})
                  </Copy>
                </Tag>
              );
            })}
          </TagGroup>
        );
      },
      width: 225,
      visible: includes([CREATE_API_TYPE.AI, CREATE_API_TYPE.LLM, 'ai'], tabKey),
    },
    {
      key: 'serviceConfigs',
      title: intl('apigw.api-manage.apiList.ApiListTableProps.ModelService'),
      dataIndex: 'serviceConfigs',
      cell: (value, index, record) => {
        let backendScene = get(record, 'versionedHttpApis[0].deployConfigs[0].backendScene');
        let serviceConfigs =
          get(record, 'versionedHttpApis[0].deployConfigs[0].serviceConfigs') || [];
        let _serviceConfigs = map(serviceConfigs, (item) => {
          return (
            <Truncate
              showTooltip
              popupStyle={{ wordBreak: 'break-all' }}
              align="t"
              style={{ width: '100%' }}
              type="width"
              threshold={'auto'}
            >
              {item.modelNamePattern
                ? `${item.modelNamePattern} -> ${item.name}`
                : `${item.name}${
                    backendScene === AI_API_BACKENDSCENE?.MultiServiceByRatio?.value
                      ? `${intl(
                          'apigw.envAndBackendServices.backendServices.columns.Weight',
                        )}${item.weight}`
                      : ''
                  }`}
            </Truncate>
          );
        });
        return !isEmpty(_serviceConfigs) ? <div>{_serviceConfigs}</div> : NA;
      },
      width: 200,
      visible: includes([CREATE_API_TYPE.AI, CREATE_API_TYPE.LLM, 'ai'], tabKey),
    },
    {
      title: intl('@ali/widget-edas-microgw::widget.gateway.action.monitor'),
      key: 'Monitor',
      dataIndex: 'Monitor',
      width: 100,
      cell: (v, i, record) => (
        <Button
          type="primary"
          size="small"
          text
          onClick={() => {
            handleOpenAction(record, 'monitor');
          }}
          disabled={
            record.type == CREATE_API_TYPE.HTTP_INGRESS ||
            (gatewayId && !record.gatewayId && record.type !== CREATE_API_TYPE.AI)
          }
        >
          <ExtendBalloon
            trigger={<Icon type="chart-line" size="xs" />}
            isShow={gatewayId && !record.gatewayId && record.type !== CREATE_API_TYPE.AI}
          >
            {gatewayId && !record.gatewayId && record.type !== CREATE_API_TYPE.AI && (
              <ApiOutJunp
                apiId={first(record.versionedHttpApis)?.httpApiId}
                restApiName={record.name}
                inGatewayId={inGatewayId}
                type="review"
                gatewayId={gatewayId}
                apiType={record.type}
                apiFromOut={true}
              />
            )}
          </ExtendBalloon>
        </Button>
      ),

      visible: tabKey === 'all',
    },
    {
      key: 'versionedHttpApis',
      title: intl('apigw.api-manage.apiList.ApiListTableProps.ApiDescription'),
      dataIndex: 'versionedHttpApis',
      cell: (value, index, record) => {
        return (
          <Truncate
            showTooltip
            popupStyle={{ wordBreak: 'break-all' }}
            align="t"
            type="width"
            threshold={190}
          >
            {get(value, '[0].description') || NA}
          </Truncate>
        );
      },
      width: 200,
      visible: tabKey === 'all',
    },
    {
      key: 'Action',
      title: intl('gateway.env.manage.table.column.title.action'),
      dataIndex: 'Action',
      cell: (value, index, record) => {
        let firstVersions: any = first(record.versionedHttpApis);
        // let deployStatus = get(firstVersions, 'environments[0].deployStatus', '');
        const firstDeployConfig: any = first(get(firstVersions, 'deployConfigs') || []) || {};
        const environments = get(firstVersions, 'environments', []);
        const _environment = find(environments, {
          environmentId: firstDeployConfig.environmentId,
        });
        const deployStatus = get(_environment, 'deployStatus');
        let disabled =
          get(record, 'versionEnabled', false) ||
          (includes([CREATE_API_TYPE.AI, CREATE_API_TYPE.LLM], record.type) &&
            includes(
              [
                API_PUBLISH_STATUS.NOT_PUBLISHED,
                API_PUBLISH_STATUS.PUBLISHING,
                API_PUBLISH_STATUS.BEING_OFFLINE,
              ],

              deployStatus,
            ));
        if (includes([CREATE_API_TYPE.AI, CREATE_API_TYPE.LLM], record.type)) {
          return (
            // @ts-ignore
            <Actions
              threshold={3}
              wrap={true}
              expandTriggerType={'hover'}
              expandTrigger={
                <LinkMore>{intl('apigw.api-manage.apiList.ApiListTableProps.More')}</LinkMore>
              }
            >
              <LinkButton disabled={disabled} onClick={() => handleOpenAction(record, 'edit')}>
                <ExtendBalloon
                  trigger={intl('apigw.api-manage.apiList.ApiListTableProps.Edit')}
                  isShow={get(record, 'versionEnabled', false)}
                >
                  {intl(
                    'apigw.api-manage.apiList.ApiListTableProps.AfterEnablingMultipleVersionsYou',
                  )}
                </ExtendBalloon>
              </LinkButton>
              {((record.type === CREATE_API_TYPE.LLM &&
                get(firstVersions, 'modelCategory', CREATE_API_MODEL_CATEGORY.Text) ===
                  CREATE_API_MODEL_CATEGORY.Text) ||
                record.type === CREATE_API_TYPE.AI) && (
                <LinkButton disabled={disabled} onClick={() => handleOpenAIDebug(record)}>
                  {intl('apigw.api-manage.apiList.ApiListTableProps.Debugging')}
                </LinkButton>
              )}

              <DeleteApiAction
                httpApiId={get(record, 'versionedHttpApis[0].httpApiId')}
                apiType={get(record, 'type')}
                setRefreshIndex={setRefreshIndex}
                disabled={disabled}
              >
                <LinkButton disabled={disabled}>
                  <ExtendBalloon
                    trigger={intl('apigw.api-manage.apiList.ApiListTableProps.Delete')}
                    isShow={get(record, 'versionEnabled', false)}
                  >
                    {intl(
                      'apigw.api-manage.apiList.ApiListTableProps.AfterYouEnableMultipleVersions',
                    )}
                  </ExtendBalloon>
                </LinkButton>
              </DeleteApiAction>
            </Actions>
          );
        }
        return (
          // @ts-ignore
          <Actions
            threshold={3}
            wrap={true}
            expandTriggerType={'hover'}
            expandTrigger={
              <LinkMore>{intl('apigw.api-manage.apiList.ApiListTableProps.More')}</LinkMore>
            }
          >
            <LinkButton
              disabled={
                disabled || firstVersions?.migrated === false || (gatewayId && !record.gatewayId)
              }
              onClick={() => handleOpenAction(record, 'edit')}
            >
              <ExtendBalloon
                trigger={intl('apigw.api-manage.apiList.ApiListTableProps.Edit')}
                isShow={get(record, 'versionEnabled', false) || (gatewayId && !record.gatewayId)}
              >
                {gatewayId && !record.gatewayId ? (
                  <ApiOutJunp
                    apiId={first(record.versionedHttpApis)?.httpApiId}
                    restApiName={record.name}
                    inGatewayId={inGatewayId}
                    type="review"
                    gatewayId={gatewayId}
                    apiType={record.type}
                    apiFromOut={true}
                  />
                ) : (
                  intl(
                    'apigw.api-manage.apiList.ApiListTableProps.AfterEnablingMultipleVersionsYou',
                  )
                )}
              </ExtendBalloon>
            </LinkButton>

            <DeleteApiAction
              httpApiId={get(record, 'versionedHttpApis[0].httpApiId')}
              apiType={get(record, 'type')}
              setRefreshIndex={setRefreshIndex}
              disabled={disabled || (gatewayId && !record.gatewayId)}
            >
              <LinkButton disabled={disabled || (gatewayId && !record.gatewayId)}>
                <ExtendBalloon
                  trigger={intl('apigw.api-manage.apiList.ApiListTableProps.Delete')}
                  isShow={get(record, 'versionEnabled', false) || (gatewayId && !record.gatewayId)}
                >
                  {gatewayId && !record.gatewayId ? (
                    <ApiOutJunp
                      apiId={first(record.versionedHttpApis)?.httpApiId}
                      restApiName={record.name}
                      inGatewayId={inGatewayId}
                      type="review"
                      gatewayId={gatewayId}
                      apiType={record.type}
                      apiFromOut={true}
                    />
                  ) : (
                    intl(
                      'apigw.api-manage.apiList.ApiListTableProps.AfterYouEnableMultipleVersions',
                    )
                  )}
                </ExtendBalloon>
              </LinkButton>
            </DeleteApiAction>
          </Actions>
        );
      },
      lock: 'right',
      width: 130,
    },
  ];

  return filter(ary, (item) => item.visible !== false);
};

export const search = (tabKey) => {
  let apiListType = sessionStorage.getItem('apiListType');
  sessionStorage.removeItem('apiListType');
  return {
    defaultDataIndex: 'keyword',
    defaultSelectedDataIndex: 'keyword',
    options: [
      {
        label: intl('apigw.api-manage.apiList.ApiListTableProps.ApiName'),
        templateProps: {
          placeholder: intl('apigw.api-manage.apiList.ApiListTableProps.EnterTheApiNameTo'),
        },
        dataIndex: 'keyword',
        template: 'input',
      },
      {
        label: intl('apigw.api-manage.apiList.ApiListTableProps.ApiType'),
        templateProps: {
          placeholder: intl('apigw.api-manage.apiList.ApiListTableProps.SelectTheApiTypeTo'),
          dataSource:
            tabKey === 'all'
              ? [
                  { label: 'REST', value: 'Rest' },
                  { label: 'HTTP', value: 'Http' },
                  { label: 'HTTP (Ingress)', value: 'HttpIngress' },
                  { label: 'WebSocket', value: 'Websocket' },
                  { label: 'AI', value: 'AI' },
                ]
              : [{ label: 'AI', value: 'AI' }],
        },
        dataIndex: 'types',
        template: 'select',
        defaultValue: apiListType || '',
      },
    ],
  };
  //TODO 两次返回不同的select导入默认值失效
  if (tabKey === 'all') {
    let apiListType = sessionStorage.getItem('apiListType');
    sessionStorage.removeItem('apiListType');
    return {
      defaultDataIndex: 'keyword',
      defaultSelectedDataIndex: 'keyword',
      options: [
        {
          label: intl('apigw.api-manage.apiList.ApiListTableProps.ApiName'),
          templateProps: {
            placeholder: intl('apigw.api-manage.apiList.ApiListTableProps.EnterTheApiNameTo'),
          },
          dataIndex: 'keyword',
          template: 'input',
        },
        {
          label: intl('apigw.api-manage.apiList.ApiListTableProps.ApiType'),
          templateProps: {
            placeholder: intl('apigw.api-manage.apiList.ApiListTableProps.SelectTheApiTypeTo'),
            dataSource: [
              { label: 'REST', value: 'Rest' },
              { label: 'HTTP', value: 'Http' },
              { label: 'HTTP (Ingress)', value: 'HttpIngress' },
              { label: 'WebSocket', value: 'Websocket' },
              { label: 'AI', value: 'AI' },
            ],
          },
          dataIndex: 'types',
          template: 'select',
          defaultValue: apiListType || '',
        },
      ],
    };
  } else {
    return {
      defaultDataIndex: 'keyword',
      defaultSelectedDataIndex: 'keyword',
      placeholder: intl('apigw.api-manage.apiList.ApiListTableProps.EnterTheApiNameTo'),
    };
  }
};

export const aiGatewaySearch = () => {
  return {
    defaultDataIndex: 'keyword',
    defaultSelectedDataIndex: 'keyword',
    options: [
      {
        label: intl('apigw.api-manage.apiList.ApiListTableProps.ApiName'),
        templateProps: {
          placeholder: intl('apigw.api-manage.apiList.ApiListTableProps.EnterTheApiNameTo'),
        },
        dataIndex: 'keyword',
        template: 'input',
      },
    ],
  };
};
