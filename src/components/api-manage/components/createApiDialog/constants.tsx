import { intl } from '@ali/cnd';
export const OpenAPIExample = `openapi: 3.0.0
info:
  title: Bookstore
  version: "1.0"
  description: API for managing an online bookstore.
servers:
  - url: http://api.example.com/v1
    description: Production server
paths:
  /books:
    get:
      summary: List all books
      operationId: listBooks
      tags:
        - Books
      parameters:
        - in: query
          name: genre
          schema:
            type: string
          description: Genre to filter by
      responses:
        '200':
          description: An array of books
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Book'
    post:
      summary: Create a book
      operationId: createBook
      tags:
        - Books
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Book'
      responses:
        '201':
          description: Book created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Book'
  /books/{bookId}:
    get:
      summary: Get a book by ID
      operationId: getBookById
      tags:
        - Books
      parameters:
        - in: path
          name: bookId
          required: true
          schema:
            type: string
          description: The ID of the book to retrieve
      responses:
        '200':
          description: A single book
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Book'
        '404':
          description: Book not found
components:
  schemas:
    Book:
      type: object
      required:
        - id
        - title
        - author
      properties:
        id:
          type: string
          description: Unique identifier for the book
        title:
          type: string
          description: Title of the book
        author:
          type: string
          description: Author of the book
        genre:
          type: string
          description: Genre of the book
        publishedDate:
          type: string
          format: date
          description: Date when the book was published
      example:
        id: "1"
        title: "The Great Gatsby"
        author: "F. Scott Fitzgerald"
        genre: "Novel"
        publishedDate: "1925-04-10"`;

export const AiExample = [
  {
    api_name: 'userAccountManagement',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingUserAccounts'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.BasicUserInformation'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheBasicUserInformationObject',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveBasicUserInformationIncluding',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewUserRegister'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateBasicUserInformationSuch',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteAUserFromThe'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.UserPreferences'),
        description: intl('apigw.components.createApiDialog.constants.UserPreferencesAreUsedTo'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainsTheUserSCurrent'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheUserSPreferences',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.UserSecuritySettings'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheUserSecuritySettingsObject',
        ),
        actions: [
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.TheUserResetsThePassword',
            ),
          },
          {
            method: 'PUT',
            description: intl('apigw.components.createApiDialog.constants.UpdateTheUserSSecurity'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.UserActivityLog'),
        description: intl('apigw.components.createApiDialog.constants.UserActivityLogObjectsStore'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainUserActivityLogsIncluding',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeletesAUserSSpecific'),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.ThisApiDesignSupportsOauth'),
  },
  {
    api_name: 'orderManagement',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingUserOrders'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Order'),
        description: intl('apigw.components.createApiDialog.constants.TheOrderObjectStoresThe'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTheOrderListRetrieve',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewOrderIncluding',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateOrderInformationSuchAs',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteTheSpecifiedOrderAnd',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.OrderStatus'),
        description: intl('apigw.components.createApiDialog.constants.ManageTheDifferentStatusOf'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainAListOfAll'),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.AddACustomOrderStatus'),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteTheOrderStatusThat',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.PaymentInformation'),
        description: intl(
          'apigw.components.createApiDialog.constants.StorePaymentInformationRelatedTo',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveThePaymentInformationOf',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.SubmitNewPaymentInformationTo',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.TheApiMustHaveWebhook'),
  },
  {
    api_name: 'bookManagement',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingLibraryStorage'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Book'),
        description: intl('apigw.components.createApiDialog.constants.TheBookObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.RetrieveAListOfBooks'),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.AddNewBooksAndImport'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheBookInformationIncluding',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteABookRecordAnd'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Borrowing'),
        description: intl('apigw.components.createApiDialog.constants.TheBorrowingObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.QueryBorrowingRecordsRetrieveThe',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.UsersBorrowBooksAndRecord',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateBorrowingInformationIncludingModifying',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.CancelBorrowingRecordsAndSelect',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Classification'),
        description: intl(
          'apigw.components.createApiDialog.constants.BookClassificationObjectsAreUsed',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.QueryTheBookClassificationList',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddNewCategoriesAndImport',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheClassificationInformationIncluding',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteACategoryRecordAnd',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.SupportOverdueRemindersForBook'),
  },
  {
    api_name: 'accountingLedgerManagement',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingAccountingBooks'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.AccountBook'),
        description: intl('apigw.components.createApiDialog.constants.TheLedgerObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTheLedgerListObtain',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewLedgerRecord'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateExistingLedgerRecordsIncluding',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteSpecificLedgerRecordsAnd',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Account'),
        description: intl('apigw.components.createApiDialog.constants.AccountObjectsAreUsedTo'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainAListOfAccounts'),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.ToCreateANewAccount'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateAccountInformationIncludingAccount',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.ToDeleteASpecificAccount',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Transaction'),
        description: intl('apigw.components.createApiDialog.constants.TheTransactionObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTransactionListsObtainSpecific',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewTransactionRecord',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateExistingTransactionsIncludingModifying',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.ToDeleteASpecificTransaction',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.DataRollbackIsRequiredTo'),
  },
  {
    api_name: 'employeeAttendanceManagement',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingAttendanceRecords'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Employee'),
        description: intl('apigw.components.createApiDialog.constants.EmployeeObjectsAreUsedTo'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveEmployeeListsRetrieveSpecific',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddNewEmployeesAndUpdate',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteEmployeeRecordsAndSelect',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.AttendanceRecord'),
        description: intl('apigw.components.createApiDialog.constants.TheAttendanceRecordObjectIs'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainEmployeeAttendanceRecordsFilter',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.RecordTheAttendanceClockIn',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateAttendanceRecordsSuchAs',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.YouCanDeleteSpecificAttendance',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.LeaveManagement'),
        description: intl(
          'apigw.components.createApiDialog.constants.LeaveManagementObjectsAreUsed',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainEmployeeLeaveApplicationRecords',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewLeaveApplication',
            ),
          },
          {
            method: 'PUT',
            description: intl('apigw.components.createApiDialog.constants.UpdateTheStatusOfThe'),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.CancelTheSubmittedLeaveApplication',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.TheSystemNeedsToIntegrate'),
  },
  {
    api_name: 'expenseReimbursementProcess',
    description: intl('apigw.components.createApiDialog.constants.TheApiUsedToManage'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.ReimbursementApplication'),
        description: intl('apigw.components.createApiDialog.constants.TheObjectOfTheReimbursement'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.SearchTheReimbursementApplicationList',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewReimbursementRequest',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheReimbursementInformationIncluding',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeletesASpecificReimbursementApplication',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.ReimbursementReview'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheReimbursementAuditObjectIs',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheReimbursementApplicationReview',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.SubmitReviewResultsIncludingConsent',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateReviewInformationModifyReview',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.ReimbursementRecord'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheReimbursementRecordObjectIs',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTheReimbursementRecordList',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.MultiLevelAuditAndPermission'),
  },
  {
    api_name: 'inventoryManagement',
    description: intl('apigw.components.createApiDialog.constants.TheApiUsedToManage.1'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Commodity'),
        description: intl('apigw.components.createApiDialog.constants.ProductObjectsAreUsedTo'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.RetrieveAListOfProducts'),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.AddNewProductsAndPut'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateProductInformationIncludingModifying',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.ToDeleteAProductYou'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Inventory'),
        description: intl('apigw.components.createApiDialog.constants.TheInventoryObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheInventoryInformationAnd',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.RecordTheProductInStockroom',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.RevokeSpecificInventoryChangeRecords',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Classification'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheClassificationObjectIsUsed',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTheClassificationListAnd',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateNewProductCategoriesAnd',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteASpecificProductCategory',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.TheBatchOperationFunctionIs'),
  },
  {
    api_name: 'customsClearanceManagement',
    description: intl('apigw.components.createApiDialog.constants.TheApiUsedToManage.2'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Commodity'),
        description: intl('apigw.components.createApiDialog.constants.TheProductObjectStoresBasic'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.SearchTheListOfProducts'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateNewProductRecordsFor',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateProductInformationIncludingPrice',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteProductRecordsThatAre',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.CustomsDeclarationForm'),
        description: intl(
          'apigw.components.createApiDialog.constants.CustomsDeclarationObjectsAreUsed',
        ),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainAListOfCustoms'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewCustomsDeclaration',
            ),
          },
          {
            method: 'PUT',
            description: intl('apigw.components.createApiDialog.constants.UpdateTheStatusOfThe.1'),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.WithdrawOrDeleteCustomsDeclarations',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.CustomsDeclarationHistory'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheCustomsDeclarationHistoryObject',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheCustomsDeclarationHistory',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.CustomsStatus'),
        description: intl('apigw.components.createApiDialog.constants.TheCustomsStatusObjectIs'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheCustomsStatusInformation',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheCustomsStatusInformation',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.TheApiMustHaveMulti'),
  },
  {
    api_name: 'travelItineraryManagement',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingTravelPlans'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.TravelPlan'),
        description: intl('apigw.components.createApiDialog.constants.TheTravelPlanObjectIs'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveAllTravelPlansObtain',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewTravelPlan'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheInformationOfThe',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.IfYouDeleteASpecific'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.TrafficInformation'),
        description: intl('apigw.components.createApiDialog.constants.ItIsUsedToManage'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainAllTrafficInformationOf',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddNewTrafficInformationSuch',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteSpecificTrafficInformationIncluding',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.AccommodationInformation'),
        description: intl(
          'apigw.components.createApiDialog.constants.ManageUsersAccommodationInformationIncluding',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.YouCanObtainAllAccommodation',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddANewAccommodationReservation',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.IfYouCancelASpecific'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.TourismActivities'),
        description: intl(
          'apigw.components.createApiDialog.constants.ManageUsersTravelActivityInformation',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainAllTravelActivityRecords',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddTravelActivityInformationTo',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.YouCanSelectWhetherTo'),
          },
        ],
      },
    ],

    extra_info: intl(
      'apigw.components.createApiDialog.constants.UserAuthenticationAndPermissionManagement',
    ),
  },
  {
    api_name: 'discountPromotionManagement',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingProductDiscounts'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Commodity'),
        description: intl('apigw.components.createApiDialog.constants.TheProductObjectStoresThe'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveProductListsRetrieveSpecific',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewProductAdd'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateProductInformationIncludingModifying.1',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteTheProductRecordAnd',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.DiscountActivity'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheDiscountActivityObjectStores',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainsAListOfActivities',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewDiscountActivity',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheDetailsOfExisting',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteADiscountActivityAnd',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.ApplicableProductsForActivities'),
        description: intl('apigw.components.createApiDialog.constants.ItIsUsedToRecord'),
        actions: [
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddApplicableProductsForA',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.RemoveSpecificProductsFromThe',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.ItIsNecessaryToRecord'),
  },
  {
    api_name: 'rideHailingPlatform',
    description: intl('apigw.components.createApiDialog.constants.ApisForManagingTaxiServices'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.User'),
        description: intl('apigw.components.createApiDialog.constants.TheUserObjectContainsBasic'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.RetrieveTheListOfUsers'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.UserRegistrationUserLoginAnd',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateUserInformationSuchAs',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteAUserAccountAnd'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Driver'),
        description: intl('apigw.components.createApiDialog.constants.TheDriverObjectContainsThe'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTheListOfDrivers',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.DriverRegistrationDriverLoginAnd',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateDriverInformationSuchAs',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteTheDriverAccountAnd',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Travel'),
        description: intl('apigw.components.createApiDialog.constants.TheTravelObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheItineraryListOf',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewTripRequest'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheTravelStatusSuch',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteASpecificTripAnd'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Payment'),
        description: intl('apigw.components.createApiDialog.constants.ThePaymentObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainTheUserSPayment'),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateAPaymentRequestTo'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateThePaymentMethodSuch',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteASpecificPaymentRecord',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.RealTimeLocationTrackingIs'),
  },
  {
    api_name: 'fruitStoreManagement',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingSalesInventory'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Fruit'),
        description: intl('apigw.components.createApiDialog.constants.FruitObjectsAreUsedTo'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveAllFruitListsRetrieve',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.AddNewFruitsAndUpdate'),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteSpecificFruitInformationAnd',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Order'),
        description: intl('apigw.components.createApiDialog.constants.TheOrderObjectStoresThe.1'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainAListOfAll.1'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateNewOrdersSubmitOrder',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheOrderStatusSuch',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.CancelASpecificOrderAnd'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Customer'),
        description: intl('apigw.components.createApiDialog.constants.CustomerObjectsAreUsedTo'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainAListOfCustomers'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.RegisterNewCustomersAndUpdate',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteSpecificCustomerInformation',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.TheApiMustHaveThe'),
  },
  {
    api_name: 'seafoodMarketManagement',
    description: intl('apigw.components.createApiDialog.constants.TheApiUsedToManage.3'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.SeafoodProducts'),
        description: intl(
          'apigw.components.createApiDialog.constants.SeafoodProductObjectsAreUsed',
        ),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.SearchTheListOfSeafood'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddNewSeafoodProductsUpdate',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteASpecificSeafoodProduct',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Order'),
        description: intl('apigw.components.createApiDialog.constants.TheOrderObjectRecordsThe'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainAListOfAll.2'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateNewOrdersUpdateOrder',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.ModifyOrderInformationIncludingModifying',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.CancelTheCreatedOrderAnd',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.User'),
        description: intl('apigw.components.createApiDialog.constants.UserObjectsAreUsedTo'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.RetrieveAListOfUsers'),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.RegisterNewUsersLogOn'),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteAUserAccountAnd.1'),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.PaymentMethodManagementAndOrder'),
  },
  {
    api_name: 'coldChainTransportManagement',
    description: intl('apigw.components.createApiDialog.constants.ApisForManagingColdChain'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Goods'),
        description: intl('apigw.components.createApiDialog.constants.TheCargoObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.RetrieveAListOfGoods'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateNewItemsIncludingThe',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateCargoInformationIncludingModification',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteGoodsRecordsThatAre',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.TransportVehicle'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheTransportVehicleObjectContains',
        ),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainAListOfVehicles'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.RegisterNewVehiclesUpdateVehicle',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheStatusOfTransportation',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteRetiredTransportVehicleRecords',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.TransportationTask'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheTransportationTaskObjectIs',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheListOfTransportation',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewTransportationTask',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheInformationOfTransportation',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.CancelTheTransportationTaskAnd',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.TemperatureMonitoring'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheTemperatureMonitoringObjectIs',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainCurrentTemperatureMonitoringData',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.UploadRealTimeTemperatureData',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.ClearExpiredTemperatureMonitoringRecords',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.TheApiMustHaveA'),
  },
  {
    api_name: 'assetDepreciationManagement',
    description: intl('apigw.components.createApiDialog.constants.TheApiUsedToManage.4'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Equipment'),
        description: intl('apigw.components.createApiDialog.constants.TheDeviceObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTheListOfDevices',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewDeviceRecord'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateDeviceInformationIncludingModifying',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteADeviceRecordAnd'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.DepreciationRecord'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheDepreciationRecordObjectIs',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheDepreciationHistoryCurrent',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.ManuallyCreateADepreciationRecord',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteDepreciationRecordsOfSpecific',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.DepreciationStrategy'),
        description: intl(
          'apigw.components.createApiDialog.constants.DepreciationPolicyObjectsAreUsed',
        ),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainAListOfAll.3'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewDepreciationPolicy',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateExistingDepreciationPoliciesIncluding',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteASpecificDepreciationPolicy',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.ItIsNecessaryToSupport'),
  },
  {
    api_name: 'stockYieldStatistics',
    description: intl('apigw.components.createApiDialog.constants.ApiForStatisticsAndManagement'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Stock'),
        description: intl('apigw.components.createApiDialog.constants.TheStockObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.SearchTheListOfStocks'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddNewStockInformationTo',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteRecordedStockInformation',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.RevenueStatistics'),
        description: intl('apigw.components.createApiDialog.constants.ItIsUsedToCalculate'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheReturnStatisticsOf',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddRevenueRecordsAndUpdate',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteASpecificRevenueStatistics',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.UserConfiguration'),
        description: intl(
          'apigw.components.createApiDialog.constants.StoresUserPersonalizedSettingsSuch',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheUserSInvestment',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateOrUpdateYourInvestment',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeletesAConfigurationSettingFor',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.TheUserSOperationHistory'),
  },
  {
    api_name: 'petFeedingScheduleManagement',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingRegularFeeding'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.FeedingPlan'),
        description: intl('apigw.components.createApiDialog.constants.TheFeedingPlanObjectIs'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveAllFeedingPlansRetrieve',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewFeedingPlan'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheExistingFeedingPlan',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.YouCanDeleteSpecificFeeding',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Pet'),
        description: intl('apigw.components.createApiDialog.constants.PetObjectsAreUsedTo'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveAllPetInformationRetrieve',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.AddTheInformationOfThe'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheInformationOfExisting',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeletePetInformationAndChoose',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.YouMustHaveTheFunction'),
  },
  {
    api_name: 'commercialInsurancePlan',
    description: intl(
      'apigw.components.createApiDialog.constants.ApiForManagingCommercialInsurance',
    ),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.InsurancePlan'),
        description: intl('apigw.components.createApiDialog.constants.TheInsurancePlanObjectIs'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTheListOfInsurance',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewInsurancePlan'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheInformationOfAn',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteInsurancePlansThatAre',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Policyholder'),
        description: intl('apigw.components.createApiDialog.constants.TheApplicantObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainAListOfPolicyholders',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateRecordsForNewPolicyholders',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheInformationOfThe.1',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.RemovePolicyholderRecordsThatAre',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.ClaimApplication'),
        description: intl('apigw.components.createApiDialog.constants.TheClaimObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.SearchTheClaimListSearch',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.SubmitANewClaimApplication',
            ),
          },
          {
            method: 'PUT',
            description: intl('apigw.components.createApiDialog.constants.UpdateTheClaimStatusOr'),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.RevokeUnprocessedClaimsOrDelete',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.MultiLanguageSupportIsRequired'),
  },
  {
    api_name: 'healthCheckSystem',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingHealthCheck'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.PhysicalExaminationUser'),
        description: intl(
          'apigw.components.createApiDialog.constants.ThePhysicalExaminationUserObject',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTheListOfPhysical',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewMedicalExamination',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheInformationOfThe.2',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteThePhysicalExaminationUser',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.PhysicalExaminationItems'),
        description: intl('apigw.components.createApiDialog.constants.TheObjectOfThePhysical'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainTheListOfPhysical'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewPhysicalExamination',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteThePhysicalExaminationItem',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.PhysicalExaminationResults'),
        description: intl(
          'apigw.components.createApiDialog.constants.ThePhysicalExaminationResultObject',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheListOfPhysical.1',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.EnterTheUserSPhysical'),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteThePhysicalExaminationResults',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.MedicalExaminationAppointment'),
        description: intl(
          'apigw.components.createApiDialog.constants.ThePhysicalExaminationAppointmentObject',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheListOfPhysical.2',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewMedicalExamination.1',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.CancelThePhysicalExaminationAppointment',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.TheSystemMustHaveThe'),
  },
  {
    api_name: 'videoOnDemandService',
    description: intl('apigw.components.createApiDialog.constants.ApiForManagingVodServices'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Film'),
        description: intl('apigw.components.createApiDialog.constants.TheMovieObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveMovieListsRetrieveSpecific',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddNewMovieInformationIncluding',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateInformationAboutExistingMovies',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteASpecificMovieAnd'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.User'),
        description: intl('apigw.components.createApiDialog.constants.UserObjectsAreUsedTo.1'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainUserAccountInformationUser',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.UserRegistrationUserLoginAnd.1',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateUserInformationSuchAs.1',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteAUserAccountAnd.2'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Subscription'),
        description: intl('apigw.components.createApiDialog.constants.TheSubscriptionObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainsTheSubscriptionStatusAnd',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.TheUserSelectsASubscription',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.CancelUserSubscriptionsAndProcess',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.YouMustHaveTheFunction.1'),
  },
  {
    api_name: 'cloudGamingService',
    description: intl('apigw.components.createApiDialog.constants.TheApiUsedToManage.5'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Game'),
        description: intl('apigw.components.createApiDialog.constants.GameObjectsAreUsedTo'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.SearchTheGameListSearch'),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewGameAnd'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateGameInformationIncludingModifying',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.ToDeleteAGameRecord'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.UserAccount'),
        description: intl('apigw.components.createApiDialog.constants.UserAccountObjectsAreUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainUserAccountInformationAnd',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateNewUserAccountsRegister',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateUserAccountInformationIncluding',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteTheUserAccountAnd'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.GameRecord'),
        description: intl('apigw.components.createApiDialog.constants.TheGameRecordObjectIs'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainTheUserSGame'),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.RecordTheUserSPlay'),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.YouCanDeleteGameRecords'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Payment'),
        description: intl('apigw.components.createApiDialog.constants.ThePaymentObjectIsUsed.1'),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainTheUserSPayment.1'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.InitiateANewPaymentRequest',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.CancelTheUserSPayment'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Feedback'),
        description: intl('apigw.components.createApiDialog.constants.TheFeedbackObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainUserFeedbackRecordsFor',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.UsersSubmitFeedbackIncludingProblem',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteFeedbackRecordsForSpecific',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.ApiDesignNeedsToSupport'),
  },
  {
    api_name: 'childrenPlaygroundEvent',
    description: intl('apigw.components.createApiDialog.constants.ApisForManagingChildrenS'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.Activity'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheActivityObjectContainsBasic',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveAllActivitiesRetrieveSpecific',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewActivityIncluding',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateActivityInformationIncludingModification',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteSpecifiedActivitiesAndSelect',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.SignUp'),
        description: intl('apigw.components.createApiDialog.constants.TheRegistrationObjectIsUsed'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainAllRegistrationInformationAnd',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.ChildrenSignUpForActivities',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.CancelTheRegistrationAndDelete',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.Awards'),
        description: intl('apigw.components.createApiDialog.constants.TheAwardObjectRecordsThe'),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheAwardSettingsOf',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.SetUpNewAwardsFor'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateTheAwardInformationOf',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteTheAwardSettingsFor',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.YouMustHaveTheFunction.2'),
  },
  {
    api_name: 'customerServiceRecords',
    description: intl('apigw.components.createApiDialog.constants.TheApiUsedToManage.6'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.CustomerServiceRecords'),
        description: intl(
          'apigw.components.createApiDialog.constants.CustomerServiceRecordsAreUsed',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveCustomerServiceRecordsRetrieve',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateANewCustomerService',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateExistingCustomerServiceRecords',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.ToDeleteACustomerService',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.CustomerInformation'),
        description: intl(
          'apigw.components.createApiDialog.constants.StoreBasicCustomerInformationSuch',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainCustomerInformationAndFeedback',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateNewCustomerInformationRecord',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateCustomerInformationSuchAs',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteCustomerInformationOrRelevant',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.CustomerServiceStaff'),
        description: intl(
          'apigw.components.createApiDialog.constants.StoreBasicInformationAboutCustomer',
        ),
        actions: [
          {
            method: 'GET',
            description: intl('apigw.components.createApiDialog.constants.ObtainAListOfCustomer'),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.CreateNewCustomerServicePersonnel',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateCustomerServicePersonnelInformation',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.ToDeleteCustomerServicePersonnel',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.ItIsNecessaryToDesign'),
  },
  {
    api_name: 'maintenanceRecordManagement',
    description: intl('apigw.components.createApiDialog.constants.TheApiUsedToManage.7'),
    resources: [
      {
        name: intl('apigw.components.createApiDialog.constants.MaintenanceRecord'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheMaintenanceRecordObjectIs',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.RetrieveTheListOfMaintenance',
            ),
          },
          {
            method: 'POST',
            description: intl('apigw.components.createApiDialog.constants.CreateANewRepairRecord'),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateExistingMaintenanceRecordsIncluding',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.DeleteSpecificServiceRecordsAnd',
            ),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.MaintenancePersonnel'),
        description: intl(
          'apigw.components.createApiDialog.constants.TheMaintenancePersonnelObjectIs',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainTheListOfMaintenance',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddNewMaintenancePersonnelInformation',
            ),
          },
          {
            method: 'PUT',
            description: intl(
              'apigw.components.createApiDialog.constants.UpdateMaintenancePersonnelInformationIncluding',
            ),
          },
          {
            method: 'DELETE',
            description: intl('apigw.components.createApiDialog.constants.DeleteTheInformationOfA'),
          },
        ],
      },
      {
        name: intl('apigw.components.createApiDialog.constants.MaintenanceClassification'),
        description: intl(
          'apigw.components.createApiDialog.constants.MaintenanceClassificationObjectsAreUsed',
        ),
        actions: [
          {
            method: 'GET',
            description: intl(
              'apigw.components.createApiDialog.constants.ObtainAListOfMaintenance',
            ),
          },
          {
            method: 'POST',
            description: intl(
              'apigw.components.createApiDialog.constants.AddANewMaintenanceClassification',
            ),
          },
          {
            method: 'DELETE',
            description: intl(
              'apigw.components.createApiDialog.constants.ToDeleteASpecificMaintenance',
            ),
          },
        ],
      },
    ],

    extra_info: intl('apigw.components.createApiDialog.constants.TheApiMustSupportFile'),
  },
];
