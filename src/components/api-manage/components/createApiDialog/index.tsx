import React, {
  useEffect,
  useState,
  useCallback,
  useRef,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  intl,
  Field,
  Form,
  Input,
  Select,
  Switch,
  Checkbox,
  Upload,
  Icon,
  Button,
  Message,
  Grid,
  Dialog,
  useHistory,
  useParams,
  Radio,
} from '@ali/cnd';
import yaml from 'js-yaml';
import { validateContent } from '~/utils/validate';
import { OpenAPIExample } from './constants';
import { USE_API_EXAMPLE1 } from '../constants';
import {
  get,
  ceil,
  size,
  noop,
  debounce,
  first,
  map,
  includes,
  isEmpty,
  endsWith,
  toLower,
  filter,
} from 'lodash';
import services from '~/utils/services';
import './index.less';
import { formatDate } from '~/utils';
import CodeMirrorEditor from '~/components/shared/CodeMirrorEditor';
import { utf8Length, utf8ValidateRule } from '~/constants/apiManage';
import ExternalLink from '~/components/shared/ExternalLink';
import { Base64 } from 'js-base64';
import {
  API_CREATE_ERROR,
  API_CREATE_STAGE,
  API_CREATE_STEP,
  trackApiCreateEvent,
  trackApiCreateType,
  trackOpenApiInfo,
} from '~/track';
import AI from './ai';
import ApiPreCheck from './apiPreCheck';
import { CREATE_API_TYPE } from '../../createApi';
import ResourceGroupSelect from '~/components/shared/ResourceGroupSelect';
import OSSBucket from './ossBucket';
import { MultiLinesMessage } from '~/components/shared/MultiLinesMessage';
import { uploadApiOss } from '~/utils/services/uploadOss';
import { SlideDrawer } from '~/components/shared/SlideDrawer';
import DomainSelect from './components/DomainSelect';
import BackendServices from './components/BackendServices';
import { INTERFACE_OR_ROUTER_CONFIG_MESSAGE } from '~/components/api-manage/components/constants';
import CustomCollapsed from '~/components/shared/CustomCollapsed';

export const validateMonacoEditor = (value: any, apiName = '') => {
  let err = '';
  try {
    JSON.parse(value);
    return;
  } catch (e) {}
  try {
    const newValue = yaml.load(value);
    return !/^[a-zA-Z0-9_-]{1,64}$/.test(get(newValue, 'info.title')) && !apiName
      ? intl('apigw.components.createApiDialog.apiName.validate.tips')
      : '';
  } catch (e) {}
  return (err = intl('apigw.components.createApiDialog.TheApiConfigurationIsIncorrect'));
};

export const base64EncodeUnicode = (str) => {
  const res = Base64.encode(str);
  return res;
};

const FormItem = Form.Item;
const { Group: CheckboxGroup } = Checkbox;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

const { Row, Col } = Grid;

const Content = (props) => {
  const field = Field.useField();
  const { init, validate, getValue, setValue, setValues, getValues, reset, setError } = field;
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUpLoadValid, setIsUpLoadValid] = useState(true);
  const [isComplete, setIsComplete] = useState(false);
  const [fileValue, setFileValue] = useState({});
  const [isExample, setIsExample] = useState(false);
  const [fileType, setFileType] = useState('json');
  const [versionEnabledEdit, setVersionEnabledEdit] = useState(true);
  const [openAPIFile, setOpenAPIFile] = useState();
  const [fileSizeOver, setFileSizeOver] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const aiRefs = useRef<any>(null);
  const [preCheckResult, setPreCheckResult] = useState({});
  const [visiblePreCheck, setVisiblePreCheck] = useState(false);
  const [isEnblePreview, setIsEnblePreview] = useState(true);
  const [isGotoSetService, setIsGotoSetService] = useState(false);
  const preCheckRef = useRef(null);
  const backendServicesRef = useRef(null);
  let { apiId: _apiId, id: gatewayId } = useParams<any>(); //当url路径上存在gatewayId时为实例详情内;
  const history = useHistory();

  const customURLVisible =
    window.ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS['api:swagger:customURL:visible'];

  const {
    type = 'create',
    apiType,
    httpApiInfo = {},
    versions,
    /**是否二次导入 */
    isReImport,
    setVisible = noop,
    setRefreshIndex = noop,
    setRefreshGetHttpApi = noop,
    setRefreshVersion = noop,
    setApiCreateSuccessful = noop,
    setRefreshInterfaceList = noop,
  }: any = props;

  const deployConfigs = useMemo(() => {
    return get(httpApiInfo, 'deployConfigs', []);
  }, [JSON.stringify(httpApiInfo?.deployConfigs)]);

  useEffect(() => {
    if (gatewayId) {
      getGatewayInfo();
    }
    if (type == 'edit') {
      const {
        name: apiName,
        description,
        basePath,
        versionInfo: {
          version = '',
          enable: versionEnabled = false,
          scheme: versioningScheme = '',
          headerName: versionHeaderName = '',
          queryName: versionQueryName = '',
        } = {},
        resourceGroupId = '',
        deployConfigs = [],
      }: any = httpApiInfo;

      let customDomainIds = deployConfigs[0]?.customDomainIds || [];

      let values: any = {
        apiName,
        description,
        basePath,
        versionEnabled,
        version,
        versioningScheme,
        resourceGroupId,
        customDomainIds: customDomainIds,
      };
      if (versioningScheme == 'Header') {
        values.addVersioningScheme = versionHeaderName;
      }
      if (versioningScheme == 'Query') {
        values.addVersioningScheme = versionQueryName;
      }
      size(versions) > 1 && setVersionEnabledEdit(false);
      setValues(values);
    }
    return () => {
      field.setValue('specFileUrl', '');
    };
  }, []);

  useEffect(() => {
    setIsEnblePreview(true);
  }, [getValue('url')]);

  const getGatewayInfo = async () => {
    try {
      let res = await services.getGatewayInfo({
        params: { gatewayId: gatewayId },
      });
      res = {
        ...res,
        label: res.name,
        value: res.gatewayId,
        gatewayInfo: { ...res },
        environmentId: res?.environments?.[0]?.environmentId,
      };
      setValue('gatewayInfos', [res]);
    } catch (error) {}
  };

  const uploadToOss = async () => {
    if (!openAPIFile) return;
    const downloadUrl = await uploadApiOss(openAPIFile, field.getValue('apiName'));
    if (downloadUrl) {
      field.setValue('specFileUrl', downloadUrl);
      return true;
    } else {
      return false;
    }
  };
  const isTextSizeOver2MB = (text = '') => {
    const blob = new Blob([text], { type: 'text/plain' });
    const sizeInBytes = blob.size;
    // 2MB = 2 * 1024 * 1024 字节
    const sizeThreshold = 2 * 1024 * 1024;
    // 返回是否超过 2MB
    return sizeInBytes > sizeThreshold;
  };

  const checkDailog = async (values) => {
    let _result: any = await getListHttpApis(values.apiName, values.type);
    let items = get(_result, 'items', []);
    if (size(items) > 0) {
      if (get(first(items), 'versionEnabled') !== !!values.versionEnabled) {
        setIsProcessing(false);
        let apiId = get(first(get(first(items), 'versionedHttpApis')), 'httpApiId');
        const dialog = Dialog.show({
          content: (
            <div>
              <div className="align-center f-w-500">
                <Icon type="error" size="small" className="color-error mr-8" />
                <span style={{ fontSize: 14 }}>
                  {intl('apigw.components.createApiDialog.CurrentApiVersionManagementConflicts')}
                </span>
              </div>
              <div style={{ width: 300, padding: '8px 16px 0px 24px' }}>
                {intl('apigw.components.createApiDialog.AfterTestingItAlreadyExists')}

                <ExternalLink
                  url={`/#/${window.regionId}/api-manage/api-list/${values.apiName}/${apiId}?region=${window.regionId}&tabKey=InterfaceList`}
                  label={values.apiName}
                  icon={false}
                />

                {apiType === 'AI'
                  ? intl('apigw.components.createApiDialog.TheCurrentApiIsNot')
                  : intl('apigw.components.createApiDialog.TheCurrentApiAndThe')}
              </div>
            </div>
          ),

          onCancel: () => {
            dialog.hide();
          },
          footer: (
            <Button
              onClick={() => {
                dialog.hide();
              }}
            >
              {intl('apigw.components.createApiDialog.Cancel')}
            </Button>
          ),
        });
        return 'waring';
      }
    }
  };

  const refreshData = () => {
    setRefreshIndex(Date.now());
    setRefreshVersion(Date.now());
    setRefreshGetHttpApi(Date.now());
    setRefreshInterfaceList(Date.now());
  };

  const handleSubmit = (dryRun) =>
    new Promise((resolve, reject) => {
      validate(async (errors, values: any) => {
        if (errors) {
          trackApiCreateEvent({
            behavior: apiType,
            stage: API_CREATE_STAGE.fail,
            apiCreateError: API_CREATE_ERROR.formValidateError,
          });
          return reject(errors);
        }
        if (apiType === 'openApi' && values.uploadMethod === 'oss') {
          if (
            !(
              endsWith(toLower(values.bucketPath), '.yaml') ||
              endsWith(toLower(values.bucketPath), '.yml') ||
              endsWith(toLower(values.bucketPath), '.json')
            )
          ) {
            setError('bucketPath', intl('apigw.components.createApiDialog.SelectYamlYmlAndJson'));
            trackApiCreateEvent({
              behavior: apiType,
              stage: API_CREATE_STAGE.fail,
              apiCreateError: API_CREATE_ERROR.fileFormatError,
            });
            return;
          }
        }

        let data: any = {
          name: values.apiName,
          type: CREATE_API_TYPE.REST,
          description: values.description,
          basePath: values.basePath,
          versionConfig: {
            enable: values.versionEnabled,
            scheme: values.versioningScheme,
            version: values.version,
          },
        };
        if (values.versionEnabled) {
          if (values.versioningScheme == 'Header') {
            data.versionConfig.headerName = values.addVersioningScheme;
          }
          if (values.versioningScheme == 'Query') {
            data.versionConfig.queryName = values.addVersioningScheme;
          }
        }

        if (apiType !== 'AI') {
          data.resourceGroupId = values.resourceGroupId;
          // data.protocols = values.protocols;
          let deployConfigs = backendServicesRef?.current?.getFormValue?.() || [];
          deployConfigs = map(deployConfigs, (item) => {
            return {
              ...item,
              customDomainIds: values.customDomainIds,
            };
          });
          data.deployConfigs = deployConfigs;
          if (!gatewayId) {
            data.deployConfigs = [
              {
                customDomainIds: values.customDomainIds,
              },
            ];
          }
        }
        if (apiType === 'openApi') {
          if (values.uploadMethod === 'local') {
            if (!values.editorText) {
              trackApiCreateEvent({
                behavior: apiType,
                stage: API_CREATE_STAGE.fail,
                apiCreateError: API_CREATE_ERROR.fileContentNull,
              });
              return;
            }
            //Base64编码
            try {
              data.specContentBase64 = base64EncodeUnicode(values.editorText);
            } catch (error) {
              // Message.error(intl('apigw.components.createApiDialog.TheApiConfigurationIsIncorrect'));
              Message.error(intl('apigw.components.createApiDialog.OpenapiContentIsInvalidPlease'));
              trackApiCreateEvent({
                behavior: apiType,
                stage: API_CREATE_STAGE.fail,
                apiCreateError: API_CREATE_ERROR.fileContentIllegal,
              });
              return;
            }
          }
        }
        // AI 大模型创建，新版改造已经废弃
        if (apiType === 'AI') {
          let isValidate = await aiRefs.current?.validate?.();
          if (!isValidate) return;
          let {
            yamlExample,
            resourceGroupId,
            customDomainIds = [],
            deployConfigs = [],
          }: any = aiRefs.current?.getAiData?.() || {};
          try {
            data.specContentBase64 = base64EncodeUnicode(yamlExample);
          } catch (error) {
            Message.error(intl('apigw.components.createApiDialog.OpenapiContentIsInvalidPlease'));
            return;
          }
          data.resourceGroupId = resourceGroupId;
          let _deployConfigs = map(deployConfigs, (item) => {
            return {
              ...item,
              customDomainIds,
            };
          });
          data.deployConfigs = _deployConfigs;
          setIsProcessing(true);
          if (dryRun) {
            let aiData = aiRefs.current.getAiData();
            let yamlExample = yaml.load(aiData.yamlExample);
            let values: any = {
              apiName: yamlExample?.info?.title || '',
              type: 'Rest',
            };
            if (values?.apiName) {
              let result = await checkDailog(values);
              if (result === 'waring') return;
            }
          }
          try {
            if (dryRun) {
              data.dryRun = true;
            } else {
              let { strategy, targetHttpApiId } = preCheckRef?.current?.parmas || {};
              data.strategy = strategy;
              data.targetHttpApiId = targetHttpApiId;
            }
            let result = await services.ImportHttpApi({
              content: { ...data },
            });
            if (dryRun) {
              setPreCheckResult(result);
              setVisiblePreCheck(true);
              setIsProcessing(false);
              return;
            }
            if (result) {
              let apiName = result?.name;
              let apiId = result?.httpApiId;
              let url = `/${window.regionId}/api-manage/api-rest/${apiName}/${apiId}?region=${window.regionId}&tabKey=InterfaceList`;
              if (gatewayId) {
                url = `/${window.regionId}/gateway/${gatewayId}/api-rest/${apiName}/${apiId}?region=${window.regionId}&tabKey=InterfaceList`;
              }
              history.push(url);
              trackApiCreateEvent({
                behavior: apiType,
                stage: API_CREATE_STAGE.success,
              });
              setVisible(false);
            }
          } catch (error) {
            trackApiCreateEvent({
              behavior: apiType,
              stage: API_CREATE_STAGE.fail,
            });
          }

          setIsProcessing(false);
        } else if (apiType === 'http') {
          setIsProcessing(true);
          if (type == 'create') {
            try {
              let result = await services.CreateHttpApi({
                content: { ...data },
              });
              if (result) {
                let apiName = result?.name;
                let apiId = result?.httpApiId;
                let url = `/${window.regionId}/api-manage/api-rest/${apiName}/${apiId}?region=${window.regionId}&tabKey=InterfaceList`;

                if (gatewayId) {
                  url = `/${window.regionId}/gateway/${gatewayId}/api-rest/${apiName}/${apiId}?region=${window.regionId}&tabKey=InterfaceList`;
                }
                history.push(url);
                trackApiCreateEvent({
                  behavior: apiType,
                  stage: API_CREATE_STAGE.success,
                });
                setVisible(false);
              } else {
                trackApiCreateEvent({
                  behavior: apiType,
                  stage: API_CREATE_STAGE.fail,
                });
              }
            } catch (error) {
              trackApiCreateEvent({
                behavior: apiType,
                stage: API_CREATE_STAGE.fail,
                apiCreateError: API_CREATE_ERROR.submitError,
              });
            }
          } else {
            let apiId = get(httpApiInfo, 'httpApiId');
            try {
              let res = await services.UpdateHttpApi({
                customErrorHandle: (err, data, callback) => {
                  callback();
                },
                params: { httpApiId: apiId },
                content: { ...data },
              });
              if (res?.responseSuccess) {
                refreshData();
                setVisible(false);
              }
            } catch (error) {}
          }
        } else if (apiType === 'openApi') {
          setIsProcessing(true);
          try {
            if (values.uploadMethod === 'local') {
              if (openAPIFile) {
                if (dryRun) {
                  if (values.apiName) {
                    let result = await checkDailog({ ...values, type: 'Rest' });
                    if (result === 'waring') {
                      trackApiCreateEvent({
                        behavior: apiType,
                        stage: API_CREATE_STAGE.fail,
                        apiCreateError: API_CREATE_ERROR.versionConflict,
                      });
                      return;
                    }
                  }
                  trackApiCreateEvent({
                    behavior: API_CREATE_STEP.uploadFile,
                    stage: API_CREATE_STAGE.trigger,
                  });
                  const uploadRes = await uploadToOss();
                  if (!uploadRes) {
                    setIsProcessing(false);
                    Message.error({
                      title: intl('apigw.components.createApiDialog.FileUploadFailed'),
                    });
                    trackApiCreateEvent({
                      behavior: API_CREATE_STEP.uploadFile,
                      stage: API_CREATE_STAGE.fail,
                    });
                    return;
                  }
                  Message.show({
                    title: intl('apigw.components.createApiDialog.FileUploadedSuccessfully'),
                  });
                  trackApiCreateEvent({
                    behavior: API_CREATE_STEP.uploadFile,
                    stage: API_CREATE_STAGE.success,
                  });
                }
                data.specFileUrl = field.getValue('specFileUrl');
                if (data.specFileUrl || isTextSizeOver2MB(values.editorText)) {
                  delete data.specContentBase64;
                }
              }
              if (isTextSizeOver2MB(values.editorText) && !data.specFileUrl) {
                setFileSizeOver(true);
                setIsProcessing(false);
                trackApiCreateEvent({
                  behavior: apiType,
                  stage: API_CREATE_STAGE.fail,
                  apiCreateError: API_CREATE_ERROR.fileSizeOver,
                });
                return;
              }
            } else if (values.uploadMethod === 'oss') {
              data.specOssConfig = {
                regionId: values.region,
                bucketName: values.bucket,
                objectKey: values.bucketPath,
              };
              if (dryRun) {
                if (values.apiName) {
                  let result = await checkDailog({ ...values, type: 'Rest' });
                  if (result === 'waring') {
                    trackApiCreateEvent({
                      behavior: apiType,
                      stage: API_CREATE_STAGE.fail,
                      apiCreateError: API_CREATE_ERROR.versionConflict,
                    });
                    return;
                  }
                }
              }
            } else if (values.uploadMethod === 'customURL') {
              data.specFileUrl = values.url;
            }

            if (dryRun) {
              data.dryRun = true;
            } else {
              let { strategy, targetHttpApiId } = preCheckRef?.current?.parmas || {};
              data.strategy = strategy;
              data.targetHttpApiId = targetHttpApiId;
            }
            let result = await services.ImportHttpApi({
              content: { ...data },
            });
            if (isEmpty(result)) {
              setIsProcessing(false);
              trackApiCreateEvent({
                behavior: apiType,
                stage: API_CREATE_STAGE.fail,
                apiCreateError: API_CREATE_ERROR.preCheckFail,
              });
              return;
            }
            if (dryRun) {
              setPreCheckResult(result);
              setVisiblePreCheck(true);
              setIsProcessing(false);
              trackApiCreateEvent({
                behavior: apiType,
                stage: API_CREATE_STAGE.fail,
                apiCreateError: API_CREATE_ERROR.preCheckFail,
              });
              return;
            }
            if (result) {
              let apiName = result?.name;
              let apiId = result?.httpApiId;
              let url = `/${window.regionId}/api-manage/api-rest/${apiName}/${apiId}?region=${window.regionId}&tabKey=InterfaceList`;
              if (gatewayId) {
                url = `/${window.regionId}/gateway/${gatewayId}/api-rest/${apiName}/${apiId}?region=${window.regionId}&tabKey=InterfaceList`;
              }
              history.push(url);
              refreshData();
              setApiCreateSuccessful && setApiCreateSuccessful({ done: true, type: 'import' });
              trackApiCreateEvent({
                behavior: apiType,
                stage: API_CREATE_STAGE.success,
              });
              setVisible(false);
            } else {
              trackApiCreateEvent({
                behavior: apiType,
                stage: API_CREATE_STAGE.fail,
              });
            }
          } catch (error) {
            trackApiCreateEvent({
              behavior: apiType,
              stage: API_CREATE_STAGE.fail,
              apiCreateError: API_CREATE_ERROR.submitError,
            });
          }
        }
        setIsProcessing(false);
      });
    });

  const beforeUpload = (file) => {
    let name = file.name;
    let dateTime = formatDate(new Date());
    let type = file.type == 'application/json' ? 'json' : 'yaml';
    let size = ceil(file.size / 1024 / 1024, 2);

    setOpenAPIFile(file);
    setFileType(type);

    setFileValue({
      name,
      dateTime,
      type,
      size,
    });

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        const content = event.target.result;
        let bool = validateContent.validate({ content, type });
        setIsUpLoadValid(bool === true);
        let editorValue = content;
        try {
          if (type === 'json') editorValue = JSON.stringify(JSON.parse(content as string), null, 2);
        } catch (e) {
          editorValue = content;
        }

        bool === true ? setValue('editorText', editorValue) : setValue('editorText', '');
        bool === true ? resolve(true) : reject(false);
        setIsComplete(true);
        validate(['editorText']);
        //重新导入不需要修改API名称
        bool === true && !isReImport
          ? field.setValue('apiName', get(JSON.parse(String(content)), 'info.title'))
          : noop();
        if (bool === true) {
          trackOpenApiInfo({
            version:
              get(JSON.parse(String(content)), 'swagger') ??
              get(JSON.parse(String(content)), 'openapi'),
            info: String(content),
            uid: window.ALIYUN_CONSOLE_CONFIG.CURRENT_PK,
            mainUid: window.ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK,
          });
        }
      };
      reader.readAsText(file);
    });
  };

  const onRemove = () => {
    setValue('Upload', '');
    setValue('editorText', '');
    setValue('specFileUrl', '');
    setOpenAPIFile(null);
    setFileValue({});
    setIsComplete(false);
  };

  const handleOpenAPIExample = () => {
    if (isExample) {
      setValue('editorText', '');
    } else {
      setFileType('yaml');
      setValue('editorText', OpenAPIExample);
    }
    setIsExample(!isExample);
  };

  const getListHttpApis = async (value, type) => {
    let data = {
      name: value,
      pageSize: 100,
      pageNumber: 1,
      gatewayId: gatewayId,
      types: type,
    };
    let result: any = await services.ListHttpApis({
      params: { ...data },
    });
    return result;
  };

  const debouncedValidateApiName = useCallback(
    debounce(async (value, type, callback) => {
      let result: any = await getListHttpApis(value, type);
      let items = get(result, 'items', []);
      if (size(items) > 0) {
        if (get(first(items), 'versionEnabled')) {
          let versions = map(get(first(items), 'versionedHttpApis', []), (item) => {
            return get(item, 'versionInfo.version');
          });
          let versionEnabled = getValue('versionEnabled');
          if (versionEnabled) {
            let version = getValue('version');
            includes(versions, version)
              ? callback(intl('apigw.components.createApiDialog.TheApiAlreadyHasThe'))
              : callback();
          } else {
            callback(intl('apigw.components.createApiDialog.VersionManagementAlreadyExistsFor'));
          }
        } else {
          callback(intl('apigw.components.createApiDialog.TheApiNameAlreadyExists'));
        }
      } else {
        callback();
      }
    }, 500),
    [],
  );

  const validateApiName = (rule, value, callback) => {
    if (includes(['http'], apiType)) {
      type === 'edit' && value === get(httpApiInfo, 'name')
        ? callback()
        : value
          ? debouncedValidateApiName(value, 'Rest', callback)
          : callback();
    } else {
      callback();
    }
  };

  const handleVersion = (value) => {
    if (apiType === 'http' && type === 'create') {
      validate(['apiName']);
    }
  };

  const title = () => {
    let title: any = '';
    if (apiType === 'openApi') {
      title = intl('apigw.components.createApiDialog.CreateAFileBasedOn');
    } else if (apiType === 'http') {
      title =
        type == 'edit'
          ? intl('apigw.components.createApiDialog.EditHttpApi')
          : intl('apigw.components.createApiDialog.CreateHttpApi');
    } else {
      title = (
        <div className="align-center">
          <img src="https://img.alicdn.com/imgextra/i3/O1CN019WwdKz1MTjixxhOoQ_!!6000000001436-55-tps-27-26.svg" />
          <span className="ai-create-dialog-title ml-4">
            {intl('apigw.components.createApiDialog.CreateBasedOnAiA')}
          </span>
          <span className="hot">HOT</span>
        </div>
      );
    }
    return title;
  };

  const handlePreview = async () => {
    let url: string = getValue('url');
    await fetch(url)
      .then((response) => {
        console.log(response);
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.text();
      })
      .then((data) => {
        setValue('customURLText', data);
        setIsEnblePreview(true);
      })
      .catch((error) => {
        setIsEnblePreview(false);
        setValue('customURLText', '');
        // console.error('处理错误情况', error);
      });
  };

  const example = () => {
    const {
      basePath = '',
      versioningScheme = '',
      version = '',
      addVersioningScheme = '',
    }: any = getValues();
    let text = USE_API_EXAMPLE1(versioningScheme, basePath, addVersioningScheme, version);
    return (
      <div style={{ color: '#808080', lineHeight: '20px', wordBreak: 'break-all' }}>
        {intl('apigw.api-manage.components.constants.Example')}
        {text}
      </div>
    );
  };

  const cancelDrawer = () => {
    const isInGateway = window.location.href.includes('/gateway/');
    const deployConfigs = backendServicesRef?.current?.getFormValue?.() || [];
    type === 'create' &&
      trackApiCreateEvent({
        behavior: apiType,
        stage: API_CREATE_STAGE.exit,
        path: !!getValue('basePath'),
        //实例外不需要填域名和服务
        domain: isInGateway ? !!getValue('customDomainIds') : null,
        service: isInGateway ? !!get(deployConfigs, '[0].serviceConfigs', []).length : null,
      });
    setVisible(false);
  };

  return (
    <div>
      <SlideDrawer
        title={title()}
        isShowing={true}
        width={apiType === 'AI' ? 1200 : 880}
        id="create-api-concent"
        className={`create-api-slidePanel`}
        onClose={() => {
          cancelDrawer();
        }}
        processingText={
          apiType === 'openApi' || apiType === 'AI'
            ? intl('apigw.components.createApiDialog.PreCheck')
            : intl('button.processing')
        }
        customFooter={
          <>
            <Button
              type="primary"
              className="apigw-form-submit"
              loading={isProcessing}
              onClick={() => {
                handleSubmit(true);
              }}
            >
              {apiType === 'openApi' || apiType === 'AI'
                ? isProcessing
                  ? intl('apigw.components.createApiDialog.PreCheck')
                  : intl('apigw.components.createApiDialog.PrecheckAndCreate')
                : intl('apigw.components.createApiDialog.Confirm')}
            </Button>
            <Button
              onClick={() => {
                cancelDrawer();
              }}
            >
              {intl('mse.common.cancel')}
            </Button>
            {/* {apiType !== 'http' && (
              <span className="mr-8 color-light-black">
                <Icon type="info_fill" size="small" className="color-primary-btn mr-4 ml-8" />
                {intl('apigw.components.createApiDialog.AfterTheApiIsModified')}
              </span>
            )} */}
          </>
        }
      >
        <div>
          {apiType === 'AI' ? (
            <AI ref={aiRefs} type={type} />
          ) : (
            <Form field={field}>
              <CustomCollapsed
                title={intl('@ali/widget-edas-microgw::widget.common.basic')}
                className="mb-16"
                isCollapse={true}
                // isShowTitle={!gatewayId}
                defaultCollapsed={false}
              >
                <FormItem
                  label={intl('apigw.components.createApiDialog.ApiName')}
                  required={apiType === 'openApi' ? false : true}
                  {...formItemLayout}
                  labelTextAlign="left"
                  extra={
                    <span className="mt-4 display-inline color-gray">
                      {intl('apigw.components.createApiDialog.TheNameIsUniqueAnd.1')}
                    </span>
                  }
                >
                  <Input
                    placeholder={
                      apiType === 'openApi'
                        ? intl('apigw.components.createApiDialog.EnterAnApiNameIf')
                        : intl('apigw.components.createApiDialog.EnterAnApiName')
                    }
                    {...init('apiName', {
                      rules: [
                        {
                          required: apiType === 'openApi' ? false : true,
                          message: intl('apigw.components.createApiDialog.TheApiNameCannotBe'),
                          // pattern: /^[a-zA-Z0-9]([a-zA-Z0-9.-]{0,62}[a-zA-Z0-9])?$/,
                          // pattern:
                          //   /^[\u4e00-\u9fa5a-zA-Z0-9]([\u4e00-\u9fa5a-zA-Z0-9 .-]{0,62}[\u4e00-\u9fa5a-zA-Z0-9])?$/,
                          // message:
                          //   '支持中文、英文字母、数字、"-"、"."、空格，大小写不敏感，以英文字母或数字开头及结尾，不超过64个字符',
                          // message: intl(
                          //   'apigw.components.createApiDialog.SupportsLettersDigitsCaseInsensitive',
                          // ),
                        },
                        {
                          pattern: /^[a-zA-Z0-9_-]{1,64}$/,
                          message: intl('apigw.components.createApiDialog.TheApiNameDoesNot'),
                        },
                        {
                          validator: (rule, value, callback) => {
                            if (includes(String(value), '@')) {
                              callback(
                                intl('apigw.components.createApiDialog.TheCharacterIsNotAllowed'),
                              );
                            } else if (includes(String(value), '/')) {
                              callback(
                                intl('apigw.components.createApiDialog.CharactersAreNotAllowed'),
                              );
                            } else {
                              if (!utf8ValidateRule(value, 64)) {
                                callback(
                                  intl('apigw.components.createApiDialog.TheApiNameCannotExceed'),
                                );
                              } else {
                                validateApiName(rule, value, callback);
                              }
                            }
                          },
                        },
                      ],
                    })}
                    maxLength={64}
                    showLimitHint
                    getValueLength={(value) => {
                      return utf8Length(value);
                    }}
                    readOnly={type == 'edit'}
                    disabled={type == 'edit'}
                    trim
                  />
                </FormItem>
                {/* {!gatewayId && apiType == 'http' && (
                 <Form.Item
                   label={intl('apigw.headBtn.publish.ApiProtocol')}
                   required
                   {...formItemLayout}
                   labelTextAlign="left"
                 >
                   <CheckboxGroup
                     dataSource={['HTTP', 'HTTPS']}
                     {...init('protocols', {
                       initValue: ['HTTP'],
                       rules: [
                         {
                           required: true,
                           message: intl(
                             'apigw.components.createApiDialog.TheProtocolCannotBeEmpty',
                           ),
                         },
                       ],
                     })}
                   ></CheckboxGroup>
                 </Form.Item>
                )} */}
                {gatewayId && includes(['http', 'openApi'], apiType) && (
                  <DomainSelect type={type} field={field} httpApiInfo={httpApiInfo} />
                )}
                {apiType == 'http' && (
                  <>
                    <FormItem label="Base Path" required {...formItemLayout} labelTextAlign="left">
                      <Input
                        {...init('basePath', {
                          rules: [
                            {
                              required: true,
                              message: intl(
                                'apigw.components.createApiDialog.BasePathCannotBeEmpty',
                              ),
                            },
                            {
                              pattern: /^\/.{0,63}$/,
                              message: intl(
                                'apigw.components.createApiDialog.BasePathMustStartWith',
                              ),
                            },
                          ],
                        })}
                        placeholder={intl(
                          'apigw.components.createApiDialog.EnterBasepathForExampleBase',
                        )}
                        maxLength={64}
                        showLimitHint
                        trim
                      />
                    </FormItem>
                  </>
                )}

                {apiType == 'openApi' && (
                  <>
                    <FormItem
                      label={intl('apigw.components.createApiDialog.UploadMethod')}
                      {...formItemLayout}
                      labelTextAlign="left"
                    >
                      <RadioGroup
                        {...init('uploadMethod', {
                          initValue: 'local',
                          props: {
                            onChange: (value) => {
                              reset([
                                'Upload',
                                'editorText',
                                'specFileUrl',
                                'region',
                                'bucket',
                                'bucketPath',
                                'url',
                                'customURLText',
                              ]);
                              setOpenAPIFile(null);
                              setFileValue({});
                              setIsComplete(false);
                            },
                          },
                        })}
                      >
                        <Radio
                          value="local"
                          label={intl('apigw.components.createApiDialog.LocalFile')}
                        />

                        <Radio
                          value="oss"
                          label={intl('apigw.components.createApiDialog.ImportOssFiles')}
                        />

                        {customURLVisible && (
                          <Radio
                            value="customURL"
                            label={intl('apigw.components.createApiDialog.CustomUrl')}
                          />
                        )}
                      </RadioGroup>
                    </FormItem>
                    {getValue('uploadMethod') === 'local' && (
                      <Form.Item
                        label={intl('apigw.components.createApiDialog.OpenapiFiles')}
                        required
                        {...formItemLayout}
                        labelTextAlign="left"
                      >
                        <>
                          <div style={{ margin: '6px 0' }}>
                            {intl('apigw.components.createApiDialog.UploadTheOpenApiFile')}
                            {intl('apigw.components.createApiDialog.TheFileSizeIsLimited')}
                          </div>
                          <Form.Item>
                            <Upload.Dragger
                              {...init('Upload', {
                                rules: [
                                  {
                                    required: !getValue('editorText'),
                                    message: intl('apigw.components.createApiDialog.UploadFiles'),
                                  },
                                ],
                              })}
                              style={{
                                margin: '6px 0',
                                display: isComplete ? 'none' : 'block',
                              }}
                              listType={'text'}
                              accept={'.json,.yaml'}
                              limit={1}
                              // @ts-ignore
                              beforeUpload={beforeUpload}
                              onRemove={onRemove}
                              onDragOver={() => {
                                !isDragOver && setIsDragOver(true);
                              }}
                              onDragLeave={() => {
                                setIsDragOver(false);
                              }}
                              onDrop={() => {
                                setIsDragOver(false);
                              }}
                            >
                              <div
                                className={`next-upload-drag ${
                                  isDragOver ? 'next-upload-drag-over' : ''
                                }`}
                                style={{
                                  width: '100%',
                                  display: 'block',
                                  padding: '10px 0px',
                                  height: 'auto',
                                }}
                              >
                                <Icon type="arrow-to-top" />
                                <div className="next-upload-text">
                                  <div>
                                    {intl(
                                      'apigw.components.createApiDialog.DragAndDropUploadedFiles',
                                    )}
                                  </div>
                                  <div>{intl('apigw.components.createApiDialog.Or')}</div>
                                  <Button type="primary" size="small">
                                    {intl('apigw.components.createApiDialog.ViewLocalFiles')}
                                  </Button>
                                </div>
                              </div>
                            </Upload.Dragger>
                          </Form.Item>
                          {isComplete && (
                            <div
                              className="align-center"
                              style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                padding: 8,
                                marginBottom: 8,
                                border:
                                  isComplete && !isUpLoadValid
                                    ? '1px solid #C80000'
                                    : '1px solid #C0C6CC',
                              }}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  justifyContent: 'flex-start',
                                }}
                              >
                                <img src="https://img.alicdn.com/imgextra/i4/O1CN01MqICun23vhSMbztP1_!!6000000007318-55-tps-26-32.svg"></img>
                                <div className="ml-8">
                                  <div>
                                    <span className="color-primary">{get(fileValue, 'name')}</span>
                                    <span>{` ${get(fileValue, 'size')}MB`}</span>
                                  </div>
                                  <div>
                                    {`${intl('apigw.apimanage.uploadTime')}`}{' '}
                                    {get(fileValue, 'dateTime')}
                                  </div>
                                </div>
                              </div>
                              <Icon onClick={onRemove} size="xs" type="closemark" />
                            </div>
                          )}

                          {isComplete && !isUpLoadValid && (
                            <div style={{ margin: '6px 0' }}>
                              <Icon
                                style={{ transform: 'rotate(45deg)', color: '#C80000' }}
                                size="xs"
                                type="plus_fill"
                              />

                              {`${intl('apigw.apimanage.file.type.error', {
                                type: get(fileValue, 'type'),
                              })}`}
                            </div>
                          )}

                          <Form.Item>
                            {fileSizeOver && (
                              <Message type="error">
                                {intl('apigw.components.createApiDialog.TheContentExceedsMAnd')}
                              </Message>
                            )}

                            <CodeMirrorEditor
                              {...(init('editorText', {
                                rules: [
                                  {
                                    required: true,
                                    message: intl('apigw.components.createApiDialog.CannotBeEmpty'),
                                  },
                                  {
                                    validator: (rule, value, callback) => {
                                      let validate = validateMonacoEditor(
                                        value,
                                        getValue('apiName'),
                                      );
                                      validate ? callback(validate) : callback();
                                    },
                                  },
                                ],
                              }) as any)}
                              language={fileType}
                              lineWrapping
                              readOnly={!isEmpty(fileValue)}
                              isLint={false}
                              height="160px"
                            />
                          </Form.Item>
                          {isEmpty(fileValue) && (
                            <Button
                              style={{ marginTop: '6px' }}
                              size="small"
                              type="normal"
                              onClick={handleOpenAPIExample}
                            >
                              {isExample ? (
                                <span>
                                  {intl('apigw.components.createApiDialog.DeleteSampleApis')}

                                  <Icon size="xs" type="closemark" />
                                </span>
                              ) : (
                                <span>
                                  <Icon size="xs" type="document" />
                                  {intl('apigw.components.createApiDialog.UseTheSampleApi')}
                                </span>
                              )}
                            </Button>
                          )}
                        </>
                      </Form.Item>
                    )}

                    {getValue('uploadMethod') === 'oss' && (
                      <OSSBucket field={field} formItemLayout={formItemLayout} />
                    )}

                    {getValue('uploadMethod') === 'customURL' && (
                      <FormItem label={'URL'} required {...formItemLayout} labelTextAlign="left">
                        <div style={{ display: 'flex' }}>
                          <FormItem style={{ flex: 1 }}>
                            <Input
                              {...init('url', {
                                rules: [
                                  {
                                    required: true,
                                    message: intl(
                                      'apigw.components.createApiDialog.TheUrlCannotBeEmpty',
                                    ),
                                  },
                                ],
                              })}
                              placeholder={intl('apigw.components.createApiDialog.EnterAUrl')}
                              trim
                            />
                          </FormItem>
                          <Button
                            className="ml-8"
                            disabled={!getValue('url')}
                            onClick={handlePreview}
                          >
                            <Icon type="view" />
                            {intl('apigw.components.createApiDialog.Preview')}
                          </Button>
                        </div>
                        <CodeMirrorEditor
                          {...(init('customURLText', {}) as any)}
                          language={'yaml'}
                          lineWrapping
                          isLint={false}
                          height="160px"
                          readOnly
                        />

                        {!isEnblePreview && (
                          // <span
                          //   style={{
                          //     // color: '#e00000',
                          //     marginTop: 4,
                          //   }}
                          // >
                          //   当前不支持预览
                          // </span>
                          <MultiLinesMessage
                            lines={[
                              {
                                label: intl(
                                  'apigw.components.createApiDialog.PreviewIsNotCurrentlySupported',
                                ),
                              },
                            ]}
                          />
                        )}
                      </FormItem>
                    )}
                  </>
                )}

                {!(
                  type == 'edit' &&
                  apiType === 'openApi' &&
                  get(httpApiInfo, 'versionInfo.version') === ''
                ) && (
                  <FormItem
                    label={intl('apigw.components.createApiDialog.VersionManagement')}
                    {...formItemLayout}
                    labelTextAlign="left"
                  >
                    <Switch
                      {...init('versionEnabled', {
                        valueName: 'checked',
                        props: {
                          onChange: (v) => {},
                        },
                      })}
                      disabled={(apiType === 'openApi' && type == 'edit') || !versionEnabledEdit}
                    ></Switch>
                    {getValue('versionEnabled') && (
                      <div style={{ background: '#F6F6F6', padding: 16, marginTop: 16 }}>
                        <FormItem
                          label={intl('apigw.components.createApiDialog.Usage')}
                          labelCol={{ span: 5 }}
                          labelTextAlign="left"
                          required
                        >
                          <Select
                            style={{ width: '100%' }}
                            {...init('versioningScheme', {
                              initValue: 'Path',
                            })}
                            disabled={apiType === 'openApi' && type == 'edit'}
                          >
                            <Select.Option value="Header">Header</Select.Option>
                            <Select.Option value="Query">Query</Select.Option>
                            <Select.Option value="Path">Path</Select.Option>
                          </Select>
                        </FormItem>
                        <FormItem
                          label={
                            getValue('versioningScheme') == 'Path'
                              ? intl('apigw.components.createApiDialog.AddPath')
                              : getValue('versioningScheme') == 'Header'
                                ? intl('apigw.components.createApiDialog.AddHeader')
                                : intl('apigw.components.createApiDialog.AddQuery')
                          }
                          labelCol={{ span: 5 }}
                          labelTextAlign="left"
                          required
                        >
                          <Row
                            gutter={8}
                            style={{
                              fontWeight: 500,
                              lineHeight: '20px',
                              color: '#808080',
                              margin: '6px 0px',
                            }}
                          >
                            {getValue('versioningScheme') !== 'Path' && <Col span="12">Key</Col>}
                            <Col span={getValue('versioningScheme') == 'Path' ? 24 : 12}>Value</Col>
                          </Row>
                          <Row gutter={8}>
                            {getValue('versioningScheme') !== 'Path' && (
                              <Col span="12">
                                <FormItem required>
                                  <Input
                                    style={{ width: '100%' }}
                                    {...init('addVersioningScheme', {
                                      rules: [
                                        {
                                          required: true,
                                          message: intl(
                                            'apigw.components.createApiDialog.CannotBeEmpty',
                                          ),
                                        },
                                        {
                                          validator: (rule, value: any, callback) => {
                                            if (getValue('versioningScheme') === 'Header') {
                                              /^x-fc/.test(value)
                                                ? callback(
                                                    intl(
                                                      'apigw.components.createApiDialog.TheHeaderCannotStartWith',
                                                    ),
                                                  )
                                                : callback();
                                            } else {
                                              callback();
                                            }
                                          },
                                        },
                                      ],
                                    })}
                                    disabled={apiType === 'openApi' && type == 'edit'}
                                    trim
                                  />
                                </FormItem>
                              </Col>
                            )}

                            <Col span={getValue('versioningScheme') == 'Path' ? 24 : 12}>
                              <FormItem required>
                                <Input
                                  style={{ width: '100%' }}
                                  {...init('version', {
                                    rules: [
                                      {
                                        required:
                                          getValue('versioningScheme') == 'Path' &&
                                          apiType === 'openApi' &&
                                          type == 'edit'
                                            ? false
                                            : true,
                                        message: intl(
                                          'apigw.components.createApiDialog.CannotBeEmpty',
                                        ),
                                      },
                                      {
                                        pattern: /^[a-zA-Z0-9]([a-zA-Z0-9.-]{0,30}[a-zA-Z0-9])?$/,
                                        message: intl(
                                          'apigw.components.createApiDialog.SupportsLettersDigitsCaseInsensitive.1',
                                        ),
                                      },
                                    ],

                                    props: {
                                      onChange: handleVersion,
                                    },
                                  })}
                                  disabled={apiType === 'openApi' && type == 'edit'}
                                  trim
                                />
                              </FormItem>
                            </Col>
                          </Row>
                        </FormItem>
                        {example()}
                      </div>
                    )}
                  </FormItem>
                )}

                <FormItem
                  label={intl('apigw.components.createApiDialog.Description')}
                  {...formItemLayout}
                  labelTextAlign="left"
                >
                  <Input.TextArea
                    placeholder={intl('apigw.components.createApiDialog.EnterADescription')}
                    {...init('description', {
                      rules: [
                        {
                          validator: (rule, value, callback) => {
                            callback(
                              !utf8ValidateRule(value, 255)
                                ? intl(
                                    'apigw.components.createApiDialog.TheInterfaceDescriptionCannotExceed',
                                  )
                                : '',
                            );
                          },
                        },
                      ],
                    })}
                    maxLength={255}
                    showLimitHint
                    trim
                    getValueLength={(value) => {
                      return utf8Length(value);
                    }}
                  />
                </FormItem>
                <ResourceGroupSelect field={field} isCollapsed type={type} />
              </CustomCollapsed>
              {includes(['http', 'openApi'], apiType) && gatewayId && (
                <BackendServices
                  {...init('backendServices', {
                    rules: [
                      {
                        validator: async (rule, value: any, callback) => {
                          const errors = await backendServicesRef?.current?.validate();
                          if (errors) {
                            callback(errors);
                          } else {
                            callback();
                          }
                        },
                      },
                    ],
                  })}
                  field={field}
                  ref={backendServicesRef}
                  title={intl('apigw.components.createApiDialog.ApiDefaultBackendService')}
                  subTitle={
                    includes(['http'], apiType)
                      ? INTERFACE_OR_ROUTER_CONFIG_MESSAGE['Rest']
                      : INTERFACE_OR_ROUTER_CONFIG_MESSAGE['Http']
                  }
                  tipsMessage={intl(
                    'apigw.components.createApiDialog.YouCanConfigureDifferentDefault',
                  )}
                  type={type}
                  defaultCollapsed={false}
                  isHasTab={false}
                  sourceFrom={includes(['http', 'openApi'], apiType) ? 'restApi' : 'httpApi'}
                  currentLevel={'firstLevel'}
                  gatewayInfos={getValue('gatewayInfos')}
                  deployConfigs={deployConfigs}
                  isGotoSetService={isGotoSetService}
                  setIsGotoSetService={setIsGotoSetService}
                  isCollapse={true}
                />
              )}
            </Form>
          )}
        </div>
      </SlideDrawer>
      {visiblePreCheck && (
        <ApiPreCheck
          ref={preCheckRef}
          preCheckResult={preCheckResult}
          setVisiblePreCheck={setVisiblePreCheck}
          handleSubmit={handleSubmit}
          {...props}
          inGatewayId={!!gatewayId}
        />
      )}
    </div>
  );
};

const CreateApiDialog = forwardRef((props: any, ref) => {
  const {
    type = 'create',
    apiType,
    buttonText,
    httpApiInfo = {},
    versions = [],
    visible: _visible = false,
    setVisible: _setVisible = () => {},
    setRefreshIndex = () => {},
    setRefreshGetHttpApi = () => {},
    setRefreshVersion = () => {},
    setApiCreateSuccessful = () => {},
    setRefreshInterfaceList = () => {},
  }: any = props;

  const [visible, setVisible] = useState(_visible);

  useImperativeHandle(ref, () => ({
    setVisible,
  }));

  return (
    <>
      {buttonText && buttonText}
      {visible && (
        <Content
          {...props}
          setVisible={() => {
            setVisible(false);
            _setVisible && _setVisible(false);
          }}
        />
      )}
    </>
  );
});

export default CreateApiDialog;
