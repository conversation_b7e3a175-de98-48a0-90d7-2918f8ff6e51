import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useContext,
  useMemo,
} from 'react';
import { intl, Form, Field, Select, Radio, Message, Input, LinkButton, Icon } from '@ali/cnd';
import { uniqueId, isEmpty, map, get, filter, noop, includes, find, size } from 'lodash';
import ServiceList from '~/components/api-manage/headBtn/publish/components/envAndBackendServices/backendServices';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/utils/cacheData';
import { MultiLinesMessage } from '~/components/shared/MultiLinesMessage';
import { FORM_FIXED_LAYOUT } from '~/constants/formLayout';
import { useParams } from '@ali/xconsole/hooks';
import { APIContext } from '~/config/context/APIContext';
import { useApiVersion } from '~/components/api-manage/useApiManage';
import AdvancedSetPolicy from './AdvancedSetPolicy';
import MockScene from './MockScene';
import CreateApiDialog from './../../index';
import services from '~/utils/services';

const FormItem = Form.Item;
const { Group: RadioGroup } = Radio;

const SceneAndService = forwardRef((props: any, ref) => {
  const {
    type, // create/edit/review
    field: _field = {},
    sourceFrom,
    currentLevel,
    gatewayInfo = {},
    deployConfig = {},
    secondLevelDeployConfig = {},
    secondLevelResourceData = {},
    sourceType,
    setIsGenerating = noop,
    resourceData,
    editApiConfig,
  } = props;
  const { getValue: _getValue = () => {} } = _field;
  const field = Field.useField();
  const [uniqueIdKey, setUniqueIdKey] = useState(uniqueId());
  const [isHasConfig, setIsHasConfig] = useState(true);
  const [isCloudFlow, setIsCloudFlow] = useState(false);
  const servicesRef = useRef(null);
  const advancedSetPolicyRef = useRef(null);
  const createApiRef = useRef(null);
  const { apiName, id: gatewayId } = useParams<any>(); //当url路径上存在gatewayId时为实例详情内;
  const { httpApiInfo, onRefreshApiInfo } = useContext(APIContext) || {};
  const { apiVersions } = useApiVersion({
    apiName,
    type: get(httpApiInfo, 'type'),
  });

  const { init, validate, getValue, setValue, setValues, resetToDefault, remove } = field;
  const backendScene = getValue('backendScene');
  const isEditMethod = currentLevel == 'secondLevel' && sourceFrom === 'restApi' && gatewayId;

  //当在实例内时,当前实例等于实例内时可以编辑;
  const inInstanceDisabled = gatewayId && gatewayId !== gatewayInfo?.gatewayId;

  useImperativeHandle(ref, () => ({
    validate: async () => {
      return new Promise((resolve) => {
        if (backendScene == 'Mock') {
          remove(['Service']);
        } else {
          remove(['responseContent']);
        }
        field.validate((errors) => {
          resolve(errors);
        });
      });
    },
    getFormValue: getFormValue,

    advancedSetPolicyRefGetValidate: async () => {
      let validate = await advancedSetPolicyRef?.current?.validate();
      return validate;
    },
    advancedSetPolicyRefGetFormValue: () => {
      return advancedSetPolicyRef?.current?.getFormValue();
    },
  }));

  useEffect(() => {
    if (type !== 'review' && currentLevel == 'firstLevel') {
      if (!isEmpty(deployConfig)) {
        setFormValue();
      }
    }
  }, [JSON.stringify(deployConfig)]);

  useEffect(() => {
    if (type === 'review') {
      setIsHasConfig(true);
      if (currentLevel == 'firstLevel') {
        if (!isEmpty(deployConfig?.serviceConfigs) || deployConfig?.backendScene === 'Mock') {
          setFormValue();
        } else {
          setIsHasConfig(false);
        }
      } else {
        if (!isEmpty(secondLevelDeployConfig)) {
          setFormValue();
        } else if (
          !isEmpty(deployConfig?.serviceConfigs) ||
          deployConfig?.backendScene === 'Mock'
        ) {
          setFormValue();
        } else {
          setIsHasConfig(false);
        }
      }
    } else {
      if (currentLevel == 'secondLevel') {
        setFormValue();
      }
    }
  }, [JSON.stringify(deployConfig), JSON.stringify(secondLevelDeployConfig)]);

  useEffect(() => {
    if (
      getValue('configMethod') === 'inherit' &&
      !isEmpty(secondLevelDeployConfig) &&
      secondLevelDeployConfig.backendScene === 'SingleService'
    ) {
      new Promise(async (resolve) => {
        let isCloudFlow = await getServiceInfo(editApiConfig);
        let _isCloudFlow = await getServiceInfo(secondLevelDeployConfig);
        console.log('isCloudFlow', isCloudFlow, _isCloudFlow);
        setIsCloudFlow(isCloudFlow ? false : _isCloudFlow);
        resolve(true);
      });
    } else {
      let cloneCurrentRecord: any = {};
      if (!isEmpty(secondLevelDeployConfig)) {
        cloneCurrentRecord = secondLevelDeployConfig;
      } else {
        cloneCurrentRecord = deployConfig;
      }
      let serviceConfigs = get(cloneCurrentRecord, 'serviceConfigs', []);
      if (size(serviceConfigs) === 1 && cloneCurrentRecord.backendScene === 'SingleService') {
        new Promise(async (resolve) => {
          let isCloudFlow = await getServiceInfo(cloneCurrentRecord);
          console.log('isCloudFlow11111', isCloudFlow);
          setIsCloudFlow(isCloudFlow);
          resolve(true);
        });
      } else {
        setIsCloudFlow(false);
      }
    }
  }, [JSON.stringify(gatewayInfo), getValue('configMethod')]);

  const getServiceInfo = async (obj) => {
    const serviceId = get(obj, 'serviceConfigs[0].serviceId');
    try {
      const res = await services.GetService({
        params: { serviceId },
      });
      let isCloudFlow = get(res, 'sourceType') === 'CloudFlow';
      return isCloudFlow;
    } catch (error) {
      return false;
    }
  };

  const setFormValue = (type?) => {
    if (type === undefined) {
      const { backendScene, serviceConfigs = [] } = deployConfig;
      let _serviceConfigs = map(serviceConfigs, (item) => ({
        ...item,
        uid: uniqueId(),
        default: get(item, 'match.default', false),
      }));
      field.setValues({
        backendScene,
        Service: !isEmpty(_serviceConfigs)
          ? _serviceConfigs
          : [
              {
                uid: uniqueId(),
                weight: includes(['MultiServiceByRatio', 'MultiServiceByTag'], backendScene)
                  ? 100
                  : 0,
              },
            ],

        // responseContent: backendScene === 'Mock' ? get(resourceData, 'mock.responseContent'):undefined,
        // responseCode: backendScene === 'Mock' ? get(resourceData, 'mock.responseCode'):undefined,
      });
      if (isEditMethod) {
        field.setValue('configMethod', isEmpty(secondLevelDeployConfig) ? 'inherit' : 'own');
      }
    } else {
      const { backendScene, serviceConfigs = [] } = secondLevelDeployConfig;
      let _serviceConfigs = map(serviceConfigs, (item) => ({
        ...item,
        uid: uniqueId(),
        default: get(item, 'match.default', false),
      }));
      if (backendScene === 'Mock') {
        field.setValues({
          backendScene,
          // responseContent: get(resourceData, 'mock.responseContent'),
          // responseCode: get(resourceData, 'mock.responseCode'),
        });
      } else {
        field.setValues({
          backendScene,
          Service: !isEmpty(_serviceConfigs)
            ? _serviceConfigs
            : [
                {
                  uid: uniqueId(),
                },
              ],
        });
      }
    }
    setUniqueIdKey(uniqueId());
  };

  const getFormValue = () => {
    let values: any = field.getValues();
    let mock = {};
    let _services = values[`Service`];
    _services = map(_services, (item) => {
      const sourceType = get(item, 'serviceData.sourceType');
      return {
        serviceId: item.serviceId,
        port: item.port || undefined,
        protocol: item.protocol || undefined,
        weight: item.weight,
        match: {
          conditions: item?.match?.conditions || [],
          default: item.default || false,
        },
        version: item.version,
      };
    });
    if (currentLevel === 'firstLevel') {
      _services = filter(_services, (item) => item.serviceId) || [];
    }
    if (
      get(values, 'backendScene') === 'Mock' &&
      get(values, 'responseCode') &&
      get(values, 'responseContent')
    ) {
      mock = {
        enable: true,
        responseCode: values.responseCode,
        responseContent: values.responseContent,
      };
    } else {
      mock = {
        enable: false,
      };
    }

    values = {
      backendScene: values.backendScene,
      serviceConfigs: _services,
      mock,
    };
    if (isEditMethod && getValue('configMethod')) {
      values['configMethod'] = getValue('configMethod');
    }
    return values;
  };

  const dataSourceScene = (enable) => {
    const mocktype = {
      value: 'Mock',
      label: 'Mock',
      des: intl('apigw.headBtn.publish.ToUseSpecificContentAs'),
      disabled: !enable && currentLevel !== 'firstLevel' && sourceFrom !== 'restApi',
    };
    let restApi = [
      {
        label: intl('apigw.headBtn.publish.BasicScenario'),
        children: [
          // {
          //   value: 'Mock',
          //   label: 'Mock',
          //   des: intl('apigw.headBtn.publish.ToUseSpecificContentAs'),
          //   disabled: !enable && currentLevel !== 'firstLevel' && sourceFrom !== 'restApi',
          // },
          {
            value: 'SingleService',
            label: intl('apigw.headBtn.publish.SingleService'),
            des: intl('apigw.headBtn.publish.DistributingRequestsToTheOnly'),
          },
        ],
      },
      {
        label: intl('apigw.headBtn.publish.GrayscaleScene'),
        children: [
          {
            value: 'MultiServiceByRatio',
            label: intl('apigw.headBtn.publish.ProportionalMultiService'),
            des: intl('apigw.headBtn.publish.TheDescriptionOfTheContent'),
          },
          {
            value: 'MultiServiceByContent',
            label: intl('apigw.headBtn.publish.RouteByContentLabel'),
            des: intl('apigw.headBtn.publish.DistributeRequestsToMultipleBackend'),
          },
          {
            value: 'MultiServiceByTag',
            label: intl('apigw.components.envAndBackendServices.LabelRoutingProportional'),
            des: (
              <>
                {intl(
                  'apigw.components.envAndBackendServices.DistributeRequestsToMultipleVersions',
                )}
                <ExternalLink
                  url={''}
                  label={intl('apigw.components.envAndBackendServices.LearnMore')}
                />
              </>
            ),
          },
        ],
      },
    ];

    let httpApi = [
      {
        label: (
          <div style={{ fontWeight: 'bold', color: '#333' }}>
            {intl('mse.microgw.create.router.basic.name')}
          </div>
        ),

        children: [
          {
            value: 'SingleService',
            label: intl('apigw.headBtn.publish.SingleService'),
            des: intl('apigw.headBtn.publish.DistributingRequestsToTheOnly'),
          },
        ],
      },
      {
        label: (
          <div style={{ fontWeight: 'bold', color: '#333' }}>
            {intl('mse.microgw.create.router.gray.name')}
          </div>
        ),

        children: [
          {
            value: 'MultiServiceByRatio',
            label: intl('apigw.headBtn.publish.ProportionalMultiService'),
            des: intl('apigw.headBtn.publish.TheDescriptionOfTheContent'),
          },
          {
            value: 'MultiServiceByTag',
            label: intl('apigw.headBtn.publish.ByLabelLabelRouting'),
            des: (
              <>
                {intl(
                  'apigw.components.envAndBackendServices.DistributeRequestsToMultipleVersions',
                )}
                <ExternalLink
                  url={''}
                  label={intl('apigw.components.envAndBackendServices.LearnMore')}
                />
              </>
            ),
          },
        ],
      },
      {
        label: (
          <div style={{ fontWeight: 'bold', color: '#333' }}>
            {intl('mse.microgw.create.router.other.name')}
          </div>
        ),

        children: [
          currentLevel === 'secondLevel' && {
            value: 'Mock',
            label: 'Mock',
            des: intl('apigw.headBtn.publish.ToUseSpecificContentAs'),
          },
          {
            value: 'Redirect',
            label: intl('@ali/widget-edas-microgw::widget.route.service.redirect'),
            des: intl('mse.microgw.create.router.redirect.desc'),
          },
        ].filter(Boolean),
      },
    ];

    if (sourceFrom == 'restApi' && currentLevel === 'secondLevel') {
      restApi[0].children.push(mocktype);
    }

    return sourceFrom == 'restApi' ? restApi : httpApi;
  };

  const newDataSourceScene = useMemo(() => {
    const mock = _getValue('mock');
    const { enable = false } = mock || {};
    if (!enable && backendScene === 'Mock') {
      setValue('backendScene', 'SingleService');
    }
    return dataSourceScene(enable);
  }, [JSON.stringify(_getValue('mock'))]);

  const configMethodDataSource = [
    {
      label: intl('apigw.components.BackendServices.SceneAndService.ConfigureInterfacesSeparately'),
      sublabel: intl(
        'apigw.components.BackendServices.SceneAndService.InterfacesAreAssociatedWithBackend',
      ),
      value: 'own',
    },
    {
      label: intl('apigw.components.BackendServices.SceneAndService.InheritFromApi'),
      sublabel: intl(
        'apigw.components.BackendServices.SceneAndService.DoNotAssociateServicesSeparately',
      ),
      disabled: !editApiConfig,
      extraLabel: !editApiConfig ? (
        <div>
          {intl('apigw.components.BackendServices.SceneAndService.YouHaveNotConfiguredApi')}
          <LinkButton
            onClick={() => {
              createApiRef.current?.setVisible?.(true);
            }}
          >
            {intl('apigw.components.BackendServices.SceneAndService.GoToConfiguration')}
          </LinkButton>
        </div>
      ) : null,
      value: 'inherit',
    },
  ];

  const itemRenderScene = (item) => {
    return (
      <div style={{ lineHeight: '20px', padding: '8px 0px' }}>
        <div>{item.label}</div>
        <div style={{ color: '#888888' }}>{item.des}</div>
      </div>
    );
  };

  const handleConfigMethodChange = (val: string) => {
    if (getValue('configMethod') === val) {
      return false;
    }
    setValue('configMethod', val);
    if (val === 'own') {
      if (isEmpty(secondLevelDeployConfig)) {
        setValues({
          backendScene: 'SingleService',
          Service: [
            {
              uid: uniqueId(),
            },
          ],
        });
      } else {
        const { backendScene, serviceConfigs = [] } = secondLevelDeployConfig;
        let _serviceConfigs = map(serviceConfigs, (item) => ({
          ...item,
          uid: uniqueId(),
          default: get(item, 'match.default', false),
        }));

        field.setValues({
          backendScene,
          Service: !isEmpty(_serviceConfigs)
            ? _serviceConfigs
            : [
                {
                  uid: uniqueId(),
                },
              ],
        });
      }
    } else {
      const { backendScene, serviceConfigs = [] } = editApiConfig;
      let _serviceConfigs = map(serviceConfigs, (item) => ({
        ...item,
        uid: uniqueId(),
        default: get(item, 'match.default', false),
      }));
      field.setValues({
        backendScene,
        Service: !isEmpty(_serviceConfigs)
          ? _serviceConfigs
          : [
              {
                uid: uniqueId(),
              },
            ],
      });
    }
  };

  return (
    <>
      {type == 'review' && !isHasConfig ? (
        <>
          {currentLevel === 'firstLevel' && (
            <Message
              title={intl(
                'apigw.components.BackendServices.SceneAndService.NoDefaultServiceIsAvailable',
              )}
              closeable={false}
              type="notice"
            >
              {sourceFrom == 'restApi'
                ? ''
                : intl('apigw.components.BackendServices.SceneAndService.YouCanConfigureApiLevel')}
              {sourceFrom !== 'restApi' && (
                <LinkButton
                  onClick={() => {
                    document.getElementById('edit-api-btn')?.click();
                  }}
                >
                  {intl('apigw.components.BackendServices.SceneAndService.GoToConfigureApiLevel')}
                </LinkButton>
              )}
            </Message>
          )}
          {currentLevel === 'secondLevel' && (
            <div
              className="justify-center"
              style={{ background: '#F6F6F6', padding: '8px 0', lineHeight: '20px' }}
            >
              <Icon type="warning" size="xs" className="mr-4" style={{ color: '#AAAAAA' }} />
              {sourceFrom == 'restApi' ? (
                gatewayId ? (
                  intl('apigw.components.BackendServices.SceneAndService.NoBackendServices')
                ) : (
                  intl(
                    'apigw.components.BackendServices.SceneAndService.NoServiceIsAvailablePlease',
                  )
                )
              ) : (
                <>
                  {intl(
                    'apigw.components.BackendServices.SceneAndService.NoBackendServicesAreAvailable',
                  )}
                  <LinkButton
                    onClick={() => {
                      document.getElementById('edit-api-btn')?.click();
                    }}
                  >
                    {intl('apigw.components.BackendServices.SceneAndService.EditApi')}
                  </LinkButton>
                  {intl('apigw.components.BackendServices.SceneAndService.Or')}
                  <LinkButton
                    onClick={() => {
                      if (sourceFrom == 'restApi') {
                        document.getElementById('edit-interface-btn')?.click();
                      } else {
                        document.getElementById('edit-route-btn')?.click();
                      }
                    }}
                  >
                    {sourceFrom == 'restApi'
                      ? intl('apigw.components.BackendServices.SceneAndService.EditInterface')
                      : intl('apigw.components.BackendServices.SceneAndService.EditRoute')}
                  </LinkButton>
                  {intl('apigw.components.BackendServices.SceneAndService.AddBackendServices')}
                </>
              )}
            </div>
          )}
        </>
      ) : (
        <Form isPreview={type == 'review'} field={field} {...FORM_FIXED_LAYOUT}>
          {isEditMethod && (
            <FormItem
              label={intl('apigw.components.BackendServices.SceneAndService.ConfigurationMethod')}
              required={type !== 'review'}
            >
              <RadioGroup
                className="full-width"
                {...init('configMethod', {
                  // initValue: 'own',
                })}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'stretch',
                    justifyContent: 'space-between',
                  }}
                >
                  {configMethodDataSource.map((item, index) => {
                    return (
                      <div
                        style={{
                          width: 'calc( 50% - 4px)',
                          lineHeight: '20px',
                          border: '1px solid #C0C6CC',
                          borderRadius: 2,
                          padding: '12px',
                          background: item.disabled
                            ? '#f5f7f8'
                            : getValue('configMethod') === item.value
                              ? '#EFF3F8'
                              : '#fff',
                        }}
                        onClick={() => {
                          handleConfigMethodChange(item.value);
                        }}
                      >
                        <Radio disabled={item.disabled} value={item.value}>
                          {item.label}
                        </Radio>
                        <div className="pl-22">
                          <div style={{ color: item.disabled ? '#b3b3b3' : '#808080' }}>
                            {item.sublabel}
                          </div>
                          {item.extraLabel && item.extraLabel}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </RadioGroup>
              {type == 'review' && (
                <div>
                  {getValue('configMethod') === 'own'
                    ? configMethodDataSource?.[0]?.label
                    : configMethodDataSource?.[1]?.label}
                </div>
              )}
            </FormItem>
          )}

          <FormItem
            label={intl('apigw.headBtn.publish.UsageScenarios')}
            required={type == 'review' ? false : true}
          >
            <Select
              {...init('backendScene', {
                initValue: 'SingleService',
                props: {
                  onChange: (val) => {
                    if (val === 'Mock' || val === 'Redirect') {
                      remove('Service');
                    } else {
                      setValues({
                        Service: [
                          {
                            uid: uniqueId(),
                            weight: includes(['MultiServiceByRatio', 'MultiServiceByTag'], val)
                              ? 100
                              : 0,
                          },
                        ],
                      });
                      setUniqueIdKey(uniqueId());
                    }
                  },
                },
              })}
              style={{ width: ' 100%' }}
              popupClassName="env-select-list"
              dataSource={newDataSourceScene}
              itemRender={itemRenderScene}
              disabled={
                type === 'review' || inInstanceDisabled || getValue('configMethod') === 'inherit'
              }
            ></Select>
            {type !== 'review' && (
              <>
                {backendScene != 'Mock' && (
                  <MultiLinesMessage
                    lines={[
                      {
                        label: (
                          <>
                            {intl('apigw.components.create-edit-route.InGrayscaleScenariosOnlyThe')}
                            <ExternalLink
                              url={CachedData.confLink('feature:createRouter:scene:detail')}
                              label={intl(
                                'apigw.components.create-edit-route.ServiceGovernanceCapabilities',
                              )}
                            />
                          </>
                        ),
                      },
                    ]}
                  />
                )}
              </>
            )}
          </FormItem>
          {backendScene !== 'Mock' && backendScene !== 'Redirect' && (
            <FormItem
              isPreview={false}
              label={intl('apigw.headBtn.publish.BackendServices')}
              help=""
              required={type == 'review' ? false : true}
            >
              <ServiceList
                key={uniqueIdKey}
                {...init('Service', {
                  // @ts-ignore
                  autoValidate: false,
                  initValue: [
                    {
                      uid: uniqueId(),
                      weight: includes(['MultiServiceByRatio', 'MultiServiceByTag'], backendScene)
                        ? 100
                        : 0,
                    },
                  ],

                  rules: [
                    {
                      validator: async (rule, value: any, callback) => {
                        const errors = await servicesRef?.current?.validate();
                        if (errors) {
                          callback(errors);
                        } else {
                          callback();
                        }
                      },
                    },
                  ],
                } as any)}
                ref={servicesRef}
                field={field}
                type={type}
                backendScene={backendScene}
                gatewayInfo={gatewayInfo}
                disabled={
                  type === 'review' || inInstanceDisabled || getValue('configMethod') === 'inherit'
                }
                sourceFrom={sourceFrom}
                isCloudFlow={isCloudFlow}
                configMethod={getValue('configMethod')}
              />
            </FormItem>
          )}
          {backendScene == 'Mock' && sourceFrom === 'restApi' && currentLevel === 'secondLevel' && (
            <MockScene
              {...{
                field,
                readOnly: type === 'review',
                required: true,
                response:
                  sourceFrom == 'restApi' && currentLevel === 'secondLevel'
                    ? get(resourceData, 'response')
                    : _getValue('response'),
                mock:
                  type == 'review' || (sourceFrom == 'restApi' && currentLevel === 'secondLevel')
                    ? {
                        responseContent: get(resourceData, 'mock.responseContent'),
                        responseCode: get(resourceData, 'mock.responseCode'),
                      }
                    : _getValue('mock'),
                setIsGenerating,
                isHiddenAi: false,
                isShowAi: !(sourceFrom === 'restApi' && currentLevel === 'secondLevel'),
              }}
            />
          )}
        </Form>
      )}
      <CreateApiDialog
        ref={createApiRef}
        type={'edit'}
        apiType={get(httpApiInfo, 'type') === 'Http' ? 'router' : 'http'}
        httpApiInfo={httpApiInfo}
        versions={apiVersions}
        setRefreshGetHttpApi={onRefreshApiInfo}
      />
    </>
  );
});

export default SceneAndService;
