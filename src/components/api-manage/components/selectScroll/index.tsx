import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import { intl, Select, Field, Form } from '@ali/cnd';
import { concat, debounce, uniqueId, includes, filter } from 'lodash';
import PopupContent from './popupContent';

export default forwardRef((props: any, ref) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [selectValue, setSelectValue] = useState(null);
  const [dataSource, setDataSource] = useState(props.dataSource || []);
  const [pageNumber, setPageNumber] = useState(1);
  const [keyword, setKeyword] = useState('');
  const [uid, setUid] = useState(uniqueId());
  const [refresh, setRefresh] = useState();

  const field = Field.useField();
  const { init } = field;
  const {
    value,
    onChange,
    fetchDate = () => { },
    style = { width: '180px' },
    isScroll = false,
    showSearch = false,
    showView = false,
    showRefresh = false,
    showAddFc = false,
    placeholder = intl('apigw.components.selectScroll.PleaseSelect'),
  } = props;

  useImperativeHandle(ref, () => ({
    validate: async () => {
      return new Promise((resolve) => {
        field.validate((errors) => {
          resolve(errors);
        });
      });
    },
  }));

  useEffect(() => {
    setDataSource(props.dataSource)
  }, [JSON.stringify(props?.dataSource)]);

  useEffect(() => {
    setSelectValue(value);
    field.setValue(`uid-${uid}`, value);
  }, [JSON.stringify(value)]);

  const handleSelect = useCallback((item) => {
    setSelectValue(item);
    field.setValue(`uid-${uid}`, item);
    onChange && onChange(item);
    setVisible(false);
  }, []);

  useEffect(() => {
    if (showSearch) {
      handleRefresh();
      return;
    }
    debouncedSearch(keyword);
  }, [keyword]);

  const debouncedSearch = useCallback(
    debounce((keyword) => {
      setLoading(true);
      fetchDate({ keyword }, ({ data }) => {
        setPageNumber(1);
        setDataSource(data);
        setLoading(false);
      });
    }, 300),
    [],
  );

  const onSearch = (value) => {
    setKeyword(value);
  };

  const scrollLoad = () => {
    if (isScroll) {
      setLoading(true);
      fetchDate({ pageNumber: pageNumber + 1, keyword: keyword }, ({ data }) => {
        setPageNumber(pageNumber + 1);
        setDataSource(concat(dataSource, data));
        setLoading(false);
      });
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    fetchDate({ keyword }, ({ data }) => {
      setPageNumber(1);
      setDataSource(data);
      setLoading(false);
    });
  };

  const popupProps = {
    triggerClickKeycode: [13, 32, 40], // space, enter, down-arrow
  };

  return (
    <Form field={field}>
      <Form.Item help=" " style={{ marginBottom: 0 }}>
        <Select
          {...props}
          style={{ ...style }}
          {...init(`uid-${uid}`, {
            initValue: '',
            rules: [
              {
                required: true,
              },
            ],
          })}
          value={selectValue}
          placeholder={placeholder}
          showSearch={false}
          autoWidth
          visible={visible}
          onVisibleChange={(visible) => {
            setVisible(visible);
          }}
          popupAutoFocus
          popupContent={
            <>
              <PopupContent
                loading={loading}
                showView={showView}
                showRefresh={showRefresh}
                onSearch={onSearch}
                showAddFc={showAddFc}
                dataSource={filter(dataSource, (item) => {
                  return includes(item.label, keyword);
                })}
                value={selectValue}
                handleSelect={handleSelect}
                scrollLoad={scrollLoad}
                handleRefresh={handleRefresh}
              />
            </>
          }
          popupProps={popupProps}
        />
      </Form.Item>
    </Form>
  );
});
