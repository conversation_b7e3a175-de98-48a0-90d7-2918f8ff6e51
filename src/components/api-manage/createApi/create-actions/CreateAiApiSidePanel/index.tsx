import React, { useState, useEffect } from 'react';
import { intl, Field, SlidePanel, useHistory } from '@ali/cnd';
import services from '~/utils/services';
import { CREATE_API_TYPE } from '../../index';
import { API_PUBLISH_STATUS } from '~/constants/apiManage';
import { map, get, isEmpty, cloneDeep, find, uniqueId, includes, filter, first } from 'lodash';
import { API_CREATE_ERROR, API_CREATE_STAGE, API_CREATE_TYPE, trackApiCreateEvent } from '~/track';
import './index.less';
import { modelNameEmpty } from '~/constants';
import ModelServiceForm from './ModelServiceForm';
import BasicInfoForm from './BasicInfoForm';
import CustomCollapsed from '~/components/shared/CustomCollapsed';

const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

const CreateAiApiSidePanel = (props): any => {
  const {
    setRefreshIndex,
    setVisible,
    type = 'create',
    httpApiInfo,
    apiScene = 'AI',
    environmentInfo,
  } = props;
  const [isProcessing, setIsProcessing] = useState(false);
  const [getEnvInfo, setGetEnvInfo] = useState<any>({});
  const [envRefreshIndex, setEnvRefreshIndex] = useState(0);
  const [uniqueIdService, setUniqueIdService] = useState(uniqueId());
  const [uniqueIdFallback, setUniqueIdFallback] = useState(uniqueId());
  const field = Field.useField({ parseName: true });
  const { validate, setValues, setValue } = field;
  const history = useHistory();

  useEffect(() => {
    if (apiScene === CREATE_API_TYPE.LLM) {
      setValue('environmentInfo', environmentInfo);
    }
  }, [apiScene]);

  useEffect(() => {
    if (!isEmpty(httpApiInfo)) {
      const _httpApiInfo: any = cloneDeep(httpApiInfo);
      const aiProtocols = get(_httpApiInfo, 'aiProtocols') || [];
      const firstDeployConfig: any = first(get(_httpApiInfo, 'deployConfigs') || []) || {};
      const firstEnvironments =
        find(get(httpApiInfo, 'environments', []), {
          environmentId: firstDeployConfig.environmentId,
        }) || {};
      const { environmentId, backendScene, customDomainIds, policyConfigs, serviceConfigs } =
        firstDeployConfig;
      let findAiStatistics = find(policyConfigs, { type: 'AiStatistics' }) || {};
      let findAiFallback = find(policyConfigs, { type: 'AiFallback' }) || {};
      let values: any = {
        name: _httpApiInfo?.name,
        aiProtocols,
        backendScene,
        basePath: get(_httpApiInfo, 'basePath', '/'),
        customDomainIds: customDomainIds || [],
        environmentInfo: {
          name: firstEnvironments.name,
          environmentId: firstEnvironments.environmentId,
          value: firstEnvironments.environmentId,
          label: firstEnvironments.name,
          gatewayInfo: firstEnvironments.gatewayInfo,
          deployStatus: firstEnvironments.deployStatus || API_PUBLISH_STATUS.NOT_PUBLISHED,
        },
        serviceConfigs: map(serviceConfigs, (item) => {
          return {
            uid: uniqueId(),
            serviceId: item.serviceId,
            weight: item.weight || 0,
            modelNamePattern: item.modelNamePattern,
            modelName: item.modelName === '' ? modelNameEmpty : item.modelName,
          };
        }),
        enableObservation: findAiStatistics?.enable || false,
        enable: findAiFallback?.enable || false,
        fallbackConfig: map(
          findAiFallback?.aiFallbackConfig?.serviceConfigs || [
            { serviceId: '', targetModelName: '', passThroughModelName: true },
          ],

          (item) => {
            return {
              uid: uniqueId(),
              ...item,
            };
          },
        ),
      };
      setValues(values);
      setUniqueIdService(uniqueId());
      setUniqueIdFallback(uniqueId());
      getEnvironment(environmentId);
    }
  }, [JSON.stringify(httpApiInfo)]);

  const getEnvironment = async (environmentId) => {
    let result = await services.GetEnvironment({
      params: {
        environmentId: environmentId,
        withVpcInfo: true,
      },
    });
    setGetEnvInfo(result);
    setEnvRefreshIndex(Date.now());
  };
  const handleSubmit = () => {
    validate(async (errors, values: any) => {
      setIsProcessing(true);
      if (!errors) {
        const { enableAuth, authConfig } = httpApiInfo;
        let data: any = {
          name: values.name,
          type: apiScene,
          basePath: get(values, 'basePath', '/'),
          enableAuth,
          authConfig,
          resourceGroupId: get(values, 'resourceGroupId'),
          // aiProtocols: values.aiProtocols,
          aiProtocols: ['OpenAI/v1'],
          deployConfigs: [
            {
              gatewayId:
                apiScene === CREATE_API_TYPE.LLM
                  ? get(environmentInfo, 'gatewayInfo.gatewayId')
                  : undefined,
              environmentId: get(values, 'environmentInfo.environmentId'),
              autoDeploy: true,
              customDomainIds: values.customDomainIds,
              backendScene: values.backendScene,
              serviceConfigs: map(values.serviceConfigs, (item) => {
                return {
                  serviceId: item.serviceId,
                  weight: item.weight,
                  modelNamePattern: item.modelNamePattern,
                  modelName: item.modelName === modelNameEmpty ? '' : item.modelName,
                };
              }),
              policyConfigs: [
                {
                  type: 'AiFallback',
                  enable: values.enable || false,
                  aiFallbackConfig: {
                    serviceConfigs: values.enable
                      ? map(values.fallbackConfig, (item) => {
                        return {
                          serviceId: item.serviceId,
                          targetModelName: item.passThroughModelName ? '' : item.targetModelName,
                          passThroughModelName: item.passThroughModelName,
                        };
                      })
                      : [],
                  },
                },
                {
                  type: 'AiStatistics',
                  enable: values.enableObservation || false,
                },
                ...filter(
                  get(httpApiInfo, 'deployConfigs[0].policyConfigs', []),
                  (item) => !includes(['AiFallback', 'AiStatistics'], item.type),
                ),
              ],
            },
          ],
        };

        if (type == 'create') {
          try {
            let result = await services.CreateHttpApi({
              content: { ...data },
            });
            if (result) {
              let apiId = result?.httpApiId;
              if (apiScene === CREATE_API_TYPE.LLM) {
                await new Promise<void>((resolve, reject) => {
                  setTimeout(() => {
                    resolve();
                    setVisible && setVisible();
                    history.push(
                      `/${window.regionId}/ai-gateway/${get(
                        environmentInfo,
                        'gatewayInfo.gatewayId',
                      )}/llm-api/${apiId}?region=${window.regionId}`,
                    );
                  }, 2000);
                });
              } else {
                setVisible && setVisible();
                history.push(
                  `/${window.regionId}/api-manage/api-ai/${apiId}?region=${window.regionId}&tabKey=detail`,
                );
              }
            }
            trackApiCreateEvent({
              behavior: API_CREATE_TYPE.aiAPI,
              stage: API_CREATE_STAGE.success,
            });
          } catch (error) {
            trackApiCreateEvent({
              behavior: API_CREATE_TYPE.aiAPI,
              stage: API_CREATE_STAGE.fail,
              apiCreateError: API_CREATE_ERROR.submitError,
            });
          }
        } else {
          let apiId = get(httpApiInfo, 'httpApiId');
          try {
            const { responseSuccess } = await services.UpdateHttpApi({
              customErrorHandle: (err, data, callback) => {
                callback();
              },
              params: { httpApiId: apiId },
              content: { ...data },
            });
            if (responseSuccess) {
              setRefreshIndex && setRefreshIndex(Date.now());
              setVisible && setVisible(false);
            }
          } catch (error) { }
        }
        setIsProcessing(false);
      } else {
        setIsProcessing(false);
        trackApiCreateEvent({
          behavior: API_CREATE_TYPE.aiAPI,
          stage: API_CREATE_STAGE.fail,
          apiCreateError: API_CREATE_ERROR.formValidateError,
        });
      }
    });
  };

  const handleClose = () => {
    setVisible(false);
    if (type == 'create') {
      trackApiCreateEvent({
        behavior: API_CREATE_TYPE.aiAPI,
        stage: API_CREATE_STAGE.exit,
      });
    }
  };

  return (
    <SlidePanel
      title={
        apiScene === CREATE_API_TYPE.LLM
          ? type == 'create'
            ? intl('apigw.create-actions.CreateAiApiSidePanel.CreateLlmApi')
            : intl('apigw.create-actions.CreateAiApiSidePanel.EditLlmApi')
          : type == 'create'
            ? intl('apigw.create-actions.CreateAiApiSidePanel.CreateAiApi')
            : intl('apigw.create-actions.CreateAiApiSidePanel.EditAiApi')
      }
      isShowing={true}
      width={960}
      onClose={handleClose}
      onCancel={handleClose}
      onOk={() => {
        handleSubmit();
      }}
      isProcessing={isProcessing}
    >
      <div className="CreateAiApiSidePanel">
        <BasicInfoForm
          {...props}
          field={field}
          setUniqueIdService={setUniqueIdService}
          setUniqueIdFallback={setUniqueIdFallback}
          getEnvInfo={getEnvInfo}
          envRefreshIndex={envRefreshIndex}
          setEnvRefreshIndex={setEnvRefreshIndex}
        />
        <CustomCollapsed
          title={intl('apigw.apiAi.detail.ALargeModelService')}
          isCollapse={true}
          defaultCollapsed={false}
        >
          <ModelServiceForm
            type={type}
            field={field}
            aiScene={apiScene}
            uniqueIdService={uniqueIdService}
            uniqueIdFallback={uniqueIdFallback}
            setUniqueIdService={setUniqueIdService}
            setUniqueIdFallback={setUniqueIdFallback}
          />
        </CustomCollapsed>
      </div>
    </SlidePanel>
  );
};

export default CreateAiApiSidePanel;
