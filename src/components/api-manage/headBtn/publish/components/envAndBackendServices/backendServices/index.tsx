import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { intl, Field, Icon, Button, Form, Grid, Loading, Balloon } from '@ali/cnd';
import Item from './Item';
import { columnsSpan } from './columns';
import { uniqueId, filter, includes, cloneDeep, size } from 'lodash';
import { MultiLinesMessage } from '~/components/shared/MultiLinesMessage';

const { Row, Col } = Grid;

const BackendServices = forwardRef((props: any, ref) => {
  const field = Field.useField();
  const { init } = field;
  const {
    type,
    value,
    onChange,
    iconType = 'plus_fill',
    backendScene,
    gatewayInfo = {},
    disabled = false,
    sourceFrom,
    gatewayType,
    isCloudFlow,
    configMethod,
  } = props;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState(value);
  const [defaultCount, setDefaultCount] = useState(0);

  useImperativeHandle(ref, () => ({
    validate: async () => {
      return new Promise((resolve) => {
        field.validate((errors) => {
          if (errors) {
            resolve(errors);
          } else {
            if ('MultiServiceByContent' === backendScene) {
              let defaultCount = 0;
              let filterDefault = filter(dataSource, (item) => {
                return item.default;
              });
              setDefaultCount(size(filterDefault));
              if (size(filterDefault) > 1) {
                resolve('errors');
              } else {
                resolve(errors);
              }
            } else {
              resolve(errors);
            }
          }
        });
      });
    },
  }));

  useEffect(() => {
    onChange(dataSource);
    if ('MultiServiceByContent' === backendScene) {
      let defaultCount = 0;
      let filterDefault = filter(dataSource, (item) => {
        return item.default;
      });
      setDefaultCount(size(filterDefault));
    }
  }, [JSON.stringify(dataSource)]);

  useEffect(() => {
    let _dataSource = cloneDeep(value);
    setDataSource(_dataSource);
  }, [value]);

  const handleAdd = () => {
    let _dataSource = cloneDeep(dataSource);
    _dataSource.push({
      uid: uniqueId(),
      weight: includes(['MultiServiceByRatio', 'MultiServiceByTag'], backendScene) ? 100 : 0,
    });
    setDataSource(_dataSource);
  };

  return (
    <Loading visible={loading} size="medium" style={{ display: 'block' }}>
      <Form field={field}>
        <Row>
          {columnsSpan({ backendScene }).map((item) => {
            return (
              <Col key={item.key} style={{ padding: '4px 4px' }} span={item.span}>
                {item.title}
                {item?.tips && (
                  <Balloon
                    type="primary"
                    align="t"
                    trigger={<Icon type="help" size={14} />}
                    closable={false}
                  >
                    {item.tips}
                  </Balloon>
                )}
              </Col>
            );
          })}
        </Row>
        {dataSource.map((item, index) => {
          return (
            <Item
              type={type}
              currentIndex={index}
              key={item.uid}
              dataItem={item}
              field={field}
              dataSource={dataSource}
              setDataSource={setDataSource}
              backendScene={backendScene}
              gatewayInfo={gatewayInfo}
              gatewayType={gatewayType}
              disabled={disabled}
              sourceFrom={sourceFrom}
              isCloudFlow={isCloudFlow}
            />
          );
        })}

        {includes(['MultiServiceByContent'], backendScene) && defaultCount > 1 ? (
          <div style={{ color: '#C80000' }}>{intl('apigw.headBtn.publish.UpToOneDefault')}</div>
        ) : null}

        {isCloudFlow && (backendScene !== 'SingleService' || configMethod === 'inherit') && (
          <MultiLinesMessage
            lines={[
              {
                label: (
                  <div>
                    {intl(
                      'apigw.headBtn.publish.HttpRewriteStrategySystemAddedNotDeletedNeedManualDelete',
                    )}
                  </div>
                ),
              },
            ]}
          />
        )}

        {includes(['MultiServiceByTag'], backendScene) && (
          <MultiLinesMessage
            lines={[
              {
                label: intl(
                  'apigw.envAndBackendServices.backendServices.LabelRoutingSupportsContainerService',
                ),
              },
            ]}
          />
        )}

        {!disabled && backendScene !== 'SingleService' && (
          <Button text type="primary" className="mt-16" onClick={handleAdd}>
            <Icon type="plus_fill" />
            {intl('apigw.envAndBackendServices.backendServices.Add')}
          </Button>
        )}
      </Form>
    </Loading>
  );
});

export default BackendServices;
