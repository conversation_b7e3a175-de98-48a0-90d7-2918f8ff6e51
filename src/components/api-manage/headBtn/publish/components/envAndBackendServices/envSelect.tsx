import React, { useEffect, useState, useRef } from 'react';
import { intl, Icon, Truncate, Button, LinkButton, useParams } from '@ali/cnd';
import { concat, filter, find, get, isEmpty, map, noop, uniqBy, partition, includes } from 'lodash';
import services from '~/utils/services';
import EnvManageSlide from '~/components/env-manage/EnvManageSlide';
import ExternalLink from '~/components/shared/ExternalLink';
import ExtendSelect from '~/components/shared/ExtendSelect';
import RefreshButton from '~/components/shared/RefreshButton';
import { API_PUBLISH_STATUS } from '~/constants/apiManage';
import CachedData from '~/utils/cacheData';
import { isGatewayNotReady } from '~/pages/$regionId/gateway/utils/transformer';
import { NA, FEATURE_STATUS_ENV_VISIBLE } from '~/constants';

const Index = ({
  value,
  onChange = noop,
  handleResetServer = noop,
  type,
  sourceFrom,
  deployStatus,
  hideAction = false,
  httpApiInfo = {},
  requestParams = {},
  isGetListApi = true,
  gatewayList,
  scene,
  gatewayType = 'API',
  ...rest
}) => {
  const [envId, setEnvId] = useState('');
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [getEnvInfo, setGetEnvInfo] = useState<any>({});
  const [dataSource, setDataSource] = useState([]);
  const refExtendSelect = useRef(null);
  const [isFirst, setIsFirst] = useState(true);
  const [hasGateway, setHasGateway] = useState([]);
  const [hasDataSource, setHasDataSource] = useState(false);
  const createEnvVisible = window.ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS['create:env:visible'];
  const { id: gatewayId } = useParams<any>();

  useEffect(() => {
    if (value) {
      setEnvId(isGetListApi ? value?.environmentId || '' : value);
    }
  }, [JSON.stringify(value)]);

  useEffect(() => {
    if (isGetListApi) {
      getEnvironment();
      getGatewayList();
    }
  }, []);

  useEffect(() => {
    if (!isGetListApi && gatewayList) {
      setDataSource(gatewayList);
    }
  }, [gatewayList]);

  useEffect(() => {
    if (!isEmpty(dataSource)) {
      if (isEmpty(value) && isFirst) {
        let _data =
          gatewayId && !scene
            ? find(dataSource, (item) => item.gatewayInfo.gatewayId === gatewayId)
            : find(dataSource, { disabled: false }) || {};
        setEnvId(get(_data, 'environmentId', ''));
        onChange(_data);
        setIsFirst(false);
      }
    }
  }, [JSON.stringify(dataSource)]);

  useEffect(() => {
    if (isEmpty(hasGateway)) {
      getGatewayList();
    }
  }, [refreshIndex]);

  const itemRender = (item) => {
    const { name, gatewayInfo = {}, environmentId, gatewayType } = item;
    const { name: gatewayName, gatewayId, vpcInfo = {} } = gatewayInfo;
    const { name: vpcName, vpcId } = vpcInfo;
    const deployStatus = get(item, 'deployStatus');
    const publishStatusAry = {
      Deploying: {
        text: intl('apigw.components.envAndBackendServices.envSelect.Publishing'),
        icon: 'loading',
        color: 'color-333',
      },
      Deployed: {
        text: intl('apigw.components.envAndBackendServices.envSelect.PublishedSuccessfully'),
        icon: 'check_fill',
        color: 'color-success',
      },
      DeployFailed: {
        text: intl('apigw.components.envAndBackendServices.envSelect.PublishingFailed'),
        icon: 'error',
        color: 'color-error',
      },
      DeployedWithChanges: {
        text: intl('apigw.components.envAndBackendServices.envSelect.Published'),
        icon: 'check_fill',
        color: 'color-success',
      },
      Undeploying: {
        text: intl('apigw.components.envAndBackendServices.envSelect.Offline'),
        icon: 'loading',
        color: 'color-333',
      },
      UndeployFailed: {
        text: intl('apigw.components.envAndBackendServices.envSelect.OfflineFailed'),
        icon: 'error',
        color: 'color-error',
      },
    };
    const list = [
      {
        label: FEATURE_STATUS_ENV_VISIBLE
          ? intl('apigw.components.envAndBackendServices.envSelect.Instance.1')
          : '',
        value: gatewayId,
        value1: gatewayName || NA,
        link:
          gatewayType === 'AI'
            ? `/#/${window.regionId}/ai-gateway/${gatewayId}/detail`
            : `/#/${window.regionId}/gateway/${gatewayId}/detail`,
      },
    ];

    FEATURE_STATUS_ENV_VISIBLE &&
      list.unshift({
        label: '',
        value: environmentId,
        value1: name,
        link: `/#/${window.regionId}/env-manage?name=${name}`,
      });

    return (
      <div style={{ padding: '8px 0', lineHeight: 1.8 }}>
        {sourceFrom === 'restApi' &&
          get(item, 'deployStatus') !== API_PUBLISH_STATUS.NOT_PUBLISHED && (
            <div className="align-center">
              <Icon
                className={`${publishStatusAry[deployStatus]?.color}  mr-4 l-h-14`}
                type={`${publishStatusAry[deployStatus]?.icon}`}
                size={'small'}
              />

              <span>{publishStatusAry[deployStatus]?.text}</span>
            </div>
          )}

        <div>
          {map(list, ({ label, value, value1, link }, index) => {
            return (
              <div className="align-center">
                <span style={{ color: '#808080' }}>{label}</span>
                <Truncate type="width" threshold={540} popupStyle={{ wordBreak: 'break-all' }}>
                  <div>
                    <span style={{ color: index === 0 ? '#333333' : '#808080' }}>
                      {`${value1} `}
                    </span>
                    <span className="hide-show-id">
                      <span style={{ color: '#808080' }}>{`/ `}</span>
                      <ExternalLink url={link} label={value} />
                    </span>
                  </div>
                </Truncate>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const valueRender = (item) => {
    if (!item.label) return;
    const { gatewayInfo = {} } = item;
    const { name: gatewayName, gatewayId } = gatewayInfo;
    return (
      <div>
        <span>
          {FEATURE_STATUS_ENV_VISIBLE
            ? `${item?.label} / ${item?.environmentId}`
            : `${gatewayName} / ${gatewayId}`}
        </span>
      </div>
    );
  };

  const getEnvironment = async () => {
    if (value?.environmentId) {
      let result = await services.GetEnvironment({
        params: {
          environmentId: value?.environmentId,
          withVpcInfo: true,
        },
      });
      setGetEnvInfo(result);
      setRefreshIndex(Date.now());
    }
  };

  const getEnvList = async ({ currentPage, searchValue }) => {
    const {
      items = [],
      pageNumber,
      pageSize,
      totalSize,
    } = await services.ListEnvironments({
      params: {
        pageNumber: currentPage,
        pageSize: 200,
        nameLike: searchValue,
        gatewayType,
        ...requestParams,
      },
    });
    let newData = [];
    if (sourceFrom === 'restApi') {
      let apiEnvIdStatus = filter(
        get(httpApiInfo, 'environments', []),
        (item) => item.deployStatus !== API_PUBLISH_STATUS.NOT_PUBLISHED,
      );
      const formatEnvs = map(concat(items, isEmpty(getEnvInfo) ? [] : [getEnvInfo]), (item) => {
        const _apiEnvConfigFind = find(
          apiEnvIdStatus,
          (status) => status.environmentId === item.environmentId,
        );
        let gatewayStatus = get(item, 'gatewayInfo.gatewayStatus', '');
        return {
          ...item,
          label: item.name,
          value: item.environmentId,
          gatewayType,
          deployStatus: get(_apiEnvConfigFind, 'deployStatus', API_PUBLISH_STATUS.NOT_PUBLISHED),
          disabled: isGatewayNotReady(gatewayStatus),
        };
      });
      const [_existEnvConfig, _notExistEnvConfig] = partition(formatEnvs, (env) => {
        return !isEmpty(
          find(apiEnvIdStatus, (status) => status.environmentId === env.environmentId),
        );
      });
      newData = concat(_existEnvConfig, _notExistEnvConfig);
      newData = uniqBy(newData, 'environmentId');
    } else {
      newData = map(concat(items, isEmpty(getEnvInfo) ? [] : [getEnvInfo]), (item) => {
        let gatewayStatus = get(item, 'gatewayInfo.gatewayStatus', '');
        return {
          ...item,
          label: item?.name,
          value: item?.environmentId,
          gatewayType,
          disabled: isGatewayNotReady(gatewayStatus),
        };
      });
      newData = uniqBy(newData, 'environmentId');
    }
    let _dataSource = refExtendSelect?.current?.dataSource || [];
    let _newData = uniqBy(concat(_dataSource, newData), 'environmentId');
    if (!hasDataSource && !isEmpty(_newData)) {
      setHasDataSource(true);
    }
    setDataSource(_newData);

    return {
      data: newData,
      currentPage: pageNumber,
      pageSize,
      total: totalSize,
    };
  };
  const getGatewayList = async () => {
    let { items = [] } = await services.getGatewayList({
      params: {
        pageSize: 10,
        pageNumber: 1,
      },
    });
    setHasGateway(items);
  };

  return (
    <div className="align-center">
      <ExtendSelect
        ref={refExtendSelect}
        value={envId}
        onChange={(value, _, item) => {
          setEnvId(value);
          onChange(item);
          handleResetServer();
        }}
        dataSource={dataSource}
        fetchData={isGetListApi ? getEnvList : null}
        // fetchData={getEnvList}
        className="mr-8 flex-1"
        popupClassName="env-select-list"
        showSearch
        isOnSearch={FEATURE_STATUS_ENV_VISIBLE ? true : false}
        isSearchValue={FEATURE_STATUS_ENV_VISIBLE ? true : false}
        filter={(key, item) => {
          const { name = '', gatewayInfo = {} } = item;
          if (FEATURE_STATUS_ENV_VISIBLE) {
            return includes(name, key);
          } else {
            const { name = '' } = gatewayInfo;
            return includes(name, key);
          }
        }}
        autoWidth={false}
        refreshIndex={refreshIndex}
        popupAutoFocus
        itemRender={itemRender}
        valueRender={!envId ? (item) => item.label : valueRender}
        notFoundContent={
          <div style={{ padding: '0px 16px' }}>
            {hasDataSource ? (
              intl('apigw.components.envAndBackendServices.envSelect.NoOption')
            ) : !isEmpty(hasGateway) ? (
              <div>
                {intl('apigw.components.envAndBackendServices.envSelect.Instance')}
                {map(hasGateway, (item) => {
                  return (
                    <LinkButton
                      style={{ textDecoration: 'none', marginRight: 2 }}
                      onClick={() => {
                        const url =
                          gatewayType === 'AI'
                            ? `${window.location.origin}/#/${window.regionId}/ai-gateway/${item.gatewayId}/detail`
                            : `${window.location.origin}/#/${window.regionId}/gateway/${item.gatewayId}/detail`;
                        window.open(url, '_blank');
                      }}
                    >
                      {`${item.name},`}
                    </LinkButton>
                  );
                })}
                {intl('apigw.components.envAndBackendServices.envSelect.Creating')}
                <Icon type="loading" size={12} />
                {intl(
                  'apigw.components.envAndBackendServices.envSelect.ExpectedMinutesPleaseRefreshAfter',
                )}
              </div>
            ) : (
              intl('apigw.components.envAndBackendServices.envSelect.ThereIsNoGatewayInstance')
            )}
          </div>
        }
        {...rest}
      />

      {!hideAction && (
        <>
          <RefreshButton
            type="normal"
            text={false}
            className="ml-8 mr-8 isOnlyIcon"
            iconSize="xs"
            handler={() => {
              setRefreshIndex(Date.now());
              return new Promise<void>(async (resolve) => {
                setTimeout(() => {
                  resolve();
                }, 800);
              });
            }}
          />

          {(isEmpty(dataSource) || !createEnvVisible) && (
            <LinkButton
              onClick={() => {
                const createUrl = CachedData.confLink('feature:gateway:common:buy', {
                  regionId: CachedData.getCurrentRegionId(),
                });
                window.open(createUrl, '_blank');
              }}
            >
              {intl('@ali/widget-edas-microgw::widget.gateway.create')}
            </LinkButton>
          )}
          {createEnvVisible &&
            !isEmpty(dataSource) &&
            ((sourceFrom == 'httpApi' && deployStatus == 'NotDeployed') ||
              sourceFrom == 'restApi') && (
              <EnvManageSlide
                setRefreshIndex={() => {
                  setRefreshIndex(Date.now());
                }}
                buttonText={intl('gateway.env.manage.create.env')}
                type="create"
                text={true}
              />
            )}
        </>
      )}
    </div>
  );
};
export default Index;
