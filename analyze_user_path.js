const XLSX = require('xlsx');
const { groupBy, sortBy, filter, map, reduce, take, uniqBy } = require('lodash');

// 配置参数
const CONFIG = {
  // 数据文件路径
  dataFile: '/Users/<USER>/Desktop/1038779450683002.xlsx',

  // 采样配置
  sampling: {
    enabled: true,
    ratio: 0.2, // 采样20%的数据进行分析
  },

  // 会话配置
  session: {
    timeoutMinutes: 30, // 30分钟无操作视为会话结束
    minActionsPerSession: 3, // 最少3个操作才算有效会话
  },

  // 关键操作定义
  keyActions: [
    'domain', 'source', 'service', 'router', 'route', 'auth', 'policy',
    'create', 'update', 'delete', 'publish', 'deploy', 'gateway'
  ],

  // 输出限制
  maxSessionsToAnalyze: 50, // 最多分析50个会话
  maxActionsPerSession: 15, // 每个会话最多显示15个操作
};

console.log('=== 大规模用户行为路径分析 ===');
console.log(`数据文件: ${CONFIG.dataFile}`);

// 读取文件
const workbook = XLSX.readFile(CONFIG.dataFile);
const worksheet = workbook.Sheets[workbook.SheetNames[0]];
const rawData = XLSX.utils.sheet_to_json(worksheet);

console.log(`原始数据量: ${rawData.length} 条记录`);

/**
 * 数据预处理函数
 */
function preprocessData(rawData) {
  console.log('开始数据预处理...');

  // 1. 数据清洗 - 过滤无效记录
  const cleanedData = filter(rawData, (record) => {
    // 必须有时间戳
    if (!record['客户端时间']) return false;

    // 必须有用户标识或会话标识
    if (!record['用户ID'] && !record['会话ID'] && !record['设备ID']) return false;

    // 必须有页面或接口信息
    if (!record['页面URL（含参数）'] && !record['接口地址']) return false;

    return true;
  });

  console.log(`数据清洗: ${rawData.length} -> ${cleanedData.length} 条`);

  // 2. 关键操作过滤
  const keyActionData = filter(cleanedData, (record) => {
    const pageUrl = (record['页面URL（含参数）'] || '').toLowerCase();
    const apiName = (record['接口地址'] || '').toLowerCase();
    const pageTitle = (record['页面标题'] || '').toLowerCase();

    // 检查是否包含关键操作
    return CONFIG.keyActions.some(keyword =>
      pageUrl.includes(keyword) ||
      apiName.includes(keyword) ||
      pageTitle.includes(keyword)
    );
  });

  console.log(`关键操作过滤: ${cleanedData.length} -> ${keyActionData.length} 条`);

  // 3. 数据采样
  let sampledData = keyActionData;
  if (CONFIG.sampling.enabled && keyActionData.length > 1000) {
    // 按用户分组进行分层采样
    const userGroups = groupBy(keyActionData, record =>
      record['用户ID'] || record['会话ID'] || record['设备ID']
    );

    sampledData = [];
    Object.values(userGroups).forEach(userActions => {
      if (userActions.length >= CONFIG.session.minActionsPerSession) {
        const sampleSize = Math.max(
          Math.floor(userActions.length * CONFIG.sampling.ratio),
          CONFIG.session.minActionsPerSession
        );
        const sampled = userActions
          .sort(() => 0.5 - Math.random())
          .slice(0, sampleSize);
        sampledData.push(...sampled);
      }
    });

    console.log(`数据采样: ${keyActionData.length} -> ${sampledData.length} 条`);
  }

  return sampledData;
}

// 数据预处理
const processedData = preprocessData(rawData);
console.log(`预处理后数据量: ${processedData.length} 条记录`);

// 按时间排序
const sortedData = processedData.sort((a, b) => a['客户端时间'] - b['客户端时间']);

/**
 * 会话识别函数
 */
function identifyUserSessions(data) {
  console.log('\n=== 用户会话识别 ===');

  // 按用户分组
  const userGroups = groupBy(data, record =>
    record['用户ID'] || record['会话ID'] || record['设备ID'] || 'anonymous'
  );

  const allSessions = [];
  const timeoutMs = CONFIG.session.timeoutMinutes * 60 * 1000;

  Object.entries(userGroups).forEach(([userId, userActions]) => {
    if (userActions.length < CONFIG.session.minActionsPerSession) return;

    // 按时间排序
    const sortedActions = sortBy(userActions, '客户端时间');

    // 分割会话
    let currentSession = null;

    sortedActions.forEach((action, index) => {
      const actionTime = action['客户端时间'] * 24 * 60 * 60 * 1000; // 转换为毫秒

      if (!currentSession ||
          (actionTime - currentSession.lastActionTime > timeoutMs)) {

        // 保存上一个会话
        if (currentSession && currentSession.actions.length >= CONFIG.session.minActionsPerSession) {
          allSessions.push(currentSession);
        }

        // 开始新会话
        currentSession = {
          sessionId: `${userId}_${Date.now()}_${index}`,
          userId,
          startTime: actionTime,
          lastActionTime: actionTime,
          actions: [action],
          duration: 0
        };
      } else {
        // 继续当前会话
        currentSession.actions.push(action);
        currentSession.lastActionTime = actionTime;
        currentSession.duration = actionTime - currentSession.startTime;
      }
    });

    // 保存最后一个会话
    if (currentSession && currentSession.actions.length >= CONFIG.session.minActionsPerSession) {
      allSessions.push(currentSession);
    }
  });

  console.log(`识别出 ${allSessions.length} 个用户会话`);
  console.log(`平均每个会话 ${(allSessions.reduce((sum, s) => sum + s.actions.length, 0) / allSessions.length).toFixed(1)} 个操作`);

  return allSessions;
}

/**
 * 操作类型识别
 */
function identifyActionType(record) {
  const pageUrl = (record['页面URL（含参数）'] || '').toLowerCase();
  const apiName = (record['接口地址'] || '').toLowerCase();
  const pageTitle = (record['页面标题'] || '').toLowerCase();

  const text = `${pageUrl} ${apiName} ${pageTitle}`;

  // 网关核心操作识别
  if (text.includes('domain') || text.includes('域名')) return 'DOMAIN_OPERATION';
  if (text.includes('source') || text.includes('来源')) return 'SOURCE_OPERATION';
  if (text.includes('service') || text.includes('服务')) return 'SERVICE_OPERATION';
  if (text.includes('router') || text.includes('route') || text.includes('路由')) return 'ROUTER_OPERATION';
  if (text.includes('auth') || text.includes('鉴权') || text.includes('认证')) return 'AUTH_OPERATION';
  if (text.includes('policy') || text.includes('策略')) return 'POLICY_OPERATION';
  if (text.includes('gateway') || text.includes('网关')) return 'GATEWAY_OPERATION';
  if (text.includes('create') || text.includes('新建') || text.includes('创建')) return 'CREATE_OPERATION';
  if (text.includes('publish') || text.includes('deploy') || text.includes('发布')) return 'PUBLISH_OPERATION';

  return 'OTHER_OPERATION';
}

// 识别用户会话
const userSessions = identifyUserSessions(sortedData);

// 选择最有价值的会话进行分析
const topSessions = userSessions
  .sort((a, b) => {
    // 按操作数量和会话时长排序
    const scoreA = a.actions.length * 10 + Math.min(a.duration / 60000, 30); // 时长分钟数，最多30分
    const scoreB = b.actions.length * 10 + Math.min(b.duration / 60000, 30);
    return scoreB - scoreA;
  })
  .slice(0, CONFIG.maxSessionsToAnalyze);

/**
 * 分析用户行为路径
 */
function analyzeUserPaths(sessions) {
  console.log('\n=== 用户行为路径分析 ===');

  const pathPatterns = {};
  const actionSequences = {};
  const dropOffPoints = {};

  sessions.forEach((session, sessionIndex) => {
    console.log(`\n📱 会话 ${sessionIndex + 1}: ${session.userId}`);
    console.log(`   时长: ${Math.round(session.duration / 60000)} 分钟`);
    console.log(`   操作数: ${session.actions.length}`);

    // 提取操作序列
    const actionTypes = session.actions
      .slice(0, CONFIG.maxActionsPerSession)
      .map(action => identifyActionType(action));

    console.log('   操作路径:');
    actionTypes.forEach((actionType, index) => {
      const action = session.actions[index];
      const timeStr = formatTime(action['客户端时间']);
      console.log(`     ${index + 1}. [${timeStr}] ${actionType}`);

      if (action['页面标题']) {
        console.log(`        页面: ${action['页面标题']}`);
      }
      if (action['接口地址']) {
        console.log(`        接口: ${action['接口地址']}`);
      }
    });

    // 统计路径模式
    const pathKey = actionTypes.slice(0, 5).join(' -> '); // 只取前5步
    pathPatterns[pathKey] = (pathPatterns[pathKey] || 0) + 1;

    // 统计操作序列
    for (let i = 0; i < actionTypes.length - 1; i++) {
      const sequence = `${actionTypes[i]} -> ${actionTypes[i + 1]}`;
      actionSequences[sequence] = (actionSequences[sequence] || 0) + 1;
    }
  });

  return { pathPatterns, actionSequences, dropOffPoints };
}

/**
 * 格式化时间显示
 */
function formatTime(excelTime) {
  const timeNum = Math.floor(excelTime * 24 * 60 * 60);
  const hours = Math.floor(timeNum / 3600) % 24;
  const minutes = Math.floor((timeNum % 3600) / 60);
  const seconds = timeNum % 60;
  return String(hours).padStart(2, '0') + ':' +
         String(minutes).padStart(2, '0') + ':' +
         String(seconds).padStart(2, '0');
}

/**
 * 生成推荐策略
 */
function generateRecommendations(pathPatterns, actionSequences) {
  console.log('\n=== 智能推荐策略 ===');

  // 分析最常见的操作序列
  const topSequences = Object.entries(actionSequences)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10);

  console.log('\n🔥 最常见的操作序列:');
  topSequences.forEach(([sequence, count], index) => {
    const probability = (count / topSessions.length * 100).toFixed(1);
    console.log(`${index + 1}. ${sequence} (${count}次, ${probability}%)`);
  });

  // 生成下一步推荐规则
  const recommendations = {};
  topSequences.forEach(([sequence, count]) => {
    const [from, to] = sequence.split(' -> ');
    if (!recommendations[from]) {
      recommendations[from] = [];
    }
    recommendations[from].push({
      nextAction: to,
      probability: count / topSessions.length,
      count: count
    });
  });

  console.log('\n💡 推荐规则:');
  Object.entries(recommendations).forEach(([action, nextActions]) => {
    console.log(`\n当用户完成 "${action}" 后，推荐:`);
    nextActions
      .sort((a, b) => b.probability - a.probability)
      .slice(0, 3)
      .forEach((rec, index) => {
        const percentage = (rec.probability * 100).toFixed(1);
        console.log(`  ${index + 1}. ${rec.nextAction} (${percentage}% 概率)`);
      });
  });

  return recommendations;
}

// 执行分析
const analysisResults = analyzeUserPaths(topSessions);
const recommendations = generateRecommendations(analysisResults.pathPatterns, analysisResults.actionSequences);

/**
 * 生成最终总结和AI分析建议
 */
function generateFinalSummary() {
  console.log('\n=== 📊 数据分析总结 ===');

  // 基础统计
  const totalUsers = new Set(processedData.map(record =>
    record['用户ID'] || record['会话ID'] || record['设备ID']
  )).size;

  const avgSessionLength = userSessions.reduce((sum, session) => sum + session.actions.length, 0) / userSessions.length;
  const avgSessionDuration = userSessions.reduce((sum, session) => sum + session.duration, 0) / userSessions.length / 60000; // 分钟

  console.log(`📈 数据概览:`);
  console.log(`   - 总用户数: ${totalUsers}`);
  console.log(`   - 有效会话数: ${userSessions.length}`);
  console.log(`   - 平均会话长度: ${avgSessionLength.toFixed(1)} 个操作`);
  console.log(`   - 平均会话时长: ${avgSessionDuration.toFixed(1)} 分钟`);

  // 最常见的操作类型
  const actionTypeCounts = {};
  processedData.forEach(record => {
    const actionType = identifyActionType(record);
    actionTypeCounts[actionType] = (actionTypeCounts[actionType] || 0) + 1;
  });

  console.log(`\n🎯 最常见的操作类型:`);
  Object.entries(actionTypeCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 8)
    .forEach(([action, count], index) => {
      const percentage = (count / processedData.length * 100).toFixed(1);
      console.log(`   ${index + 1}. ${action}: ${count}次 (${percentage}%)`);
    });

  // 关键发现
  console.log(`\n🔍 关键发现:`);

  // 分析最常见的操作序列
  const topSequence = Object.entries(analysisResults.actionSequences)
    .sort(([,a], [,b]) => b - a)[0];

  if (topSequence) {
    const [sequence, count] = topSequence;
    const probability = (count / topSessions.length * 100).toFixed(1);
    console.log(`   - 最常见的操作序列: "${sequence}" (${probability}% 的用户)`);
  }

  // 分析用户行为模式
  const routerOperations = processedData.filter(record =>
    identifyActionType(record) === 'ROUTER_OPERATION'
  ).length;
  const authOperations = processedData.filter(record =>
    identifyActionType(record) === 'AUTH_OPERATION'
  ).length;

  if (routerOperations > 0 && authOperations > 0) {
    const authAfterRouterRate = (authOperations / routerOperations * 100).toFixed(1);
    console.log(`   - 路由配置后启用鉴权的比例: ${authAfterRouterRate}%`);
  }

  // AI推荐策略建议
  console.log(`\n🤖 AI推荐策略建议:`);
  console.log(`   1. 在用户完成路由配置后，主动推荐启用鉴权策略`);
  console.log(`   2. 为新用户提供"域名→来源→服务→路由"的引导流程`);
  console.log(`   3. 在用户停留时间过长的页面提供智能帮助`);
  console.log(`   4. 基于用户操作历史个性化推荐下一步操作`);

  // 输出适合AI分析的结构化数据
  const aiReadyData = {
    summary: {
      totalUsers,
      totalSessions: userSessions.length,
      avgSessionLength: parseFloat(avgSessionLength.toFixed(1)),
      avgSessionDuration: parseFloat(avgSessionDuration.toFixed(1))
    },
    topActionTypes: Object.entries(actionTypeCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([action, count]) => ({
        action,
        count,
        percentage: parseFloat((count / processedData.length * 100).toFixed(1))
      })),
    topSequences: Object.entries(analysisResults.actionSequences)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([sequence, count]) => ({
        sequence,
        count,
        probability: parseFloat((count / topSessions.length * 100).toFixed(1))
      })),
    recommendations: Object.entries(recommendations).map(([action, nextActions]) => ({
      afterAction: action,
      recommendedNext: nextActions
        .sort((a, b) => b.probability - a.probability)
        .slice(0, 3)
        .map(rec => ({
          action: rec.nextAction,
          probability: parseFloat((rec.probability * 100).toFixed(1))
        }))
    }))
  };

  console.log(`\n📋 结构化数据已生成，可用于AI进一步分析`);
  console.log(`数据大小: ${JSON.stringify(aiReadyData).length} 字符`);

  return aiReadyData;
}

// 生成最终总结
const finalSummary = generateFinalSummary();

console.log('\n=== ✅ 分析完成 ===');
console.log('此数据已经过预处理，适合输入到AI模型进行进一步分析和推荐策略生成。');
