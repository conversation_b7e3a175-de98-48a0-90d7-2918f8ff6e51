/**
 * 大规模用户行为数据预处理策略
 * 处理每天60万条记录的用户行为数据
 */

const fs = require('fs');
const path = require('path');
const { groupBy, sortBy, filter, map, reduce, take, uniqBy } = require('lodash');

class LargeScaleDataProcessor {
  constructor() {
    this.config = {
      // 数据采样配置
      sampling: {
        enabled: true,
        ratio: 0.1, // 采样10%的数据
        method: 'stratified' // 分层采样
      },
      
      // 会话配置
      session: {
        timeoutMinutes: 30,
        minActionsPerSession: 3, // 最少3个操作才算有效会话
        maxSessionDuration: 4 * 60 * 60 * 1000 // 最长4小时
      },
      
      // 关键操作定义
      keyActions: [
        'CREATE_DOMAIN', 'CREATE_SOURCE', 'CREATE_SERVICE', 
        'CREATE_ROUTER', 'ENABLE_AUTH', 'PUBLISH_ROUTER',
        'CONFIG_POLICY', 'CREATE_API', 'DEPLOY_API'
      ],
      
      // 输出限制
      output: {
        maxSessionsPerDay: 1000, // 每天最多分析1000个会话
        maxActionsPerSession: 20, // 每个会话最多20个操作
        summaryOnly: true // 只输出摘要信息
      }
    };
  }

  /**
   * 主处理流程
   */
  async processWeeklyData(dataDirectory) {
    console.log('开始处理7天用户行为数据...');
    
    const results = {
      dailyStats: {},
      weeklyPatterns: {},
      userJourneys: [],
      recommendations: {}
    };

    // 1. 逐天处理数据
    for (let day = 0; day < 7; day++) {
      const dayData = await this.processDayData(dataDirectory, day);
      results.dailyStats[`day_${day}`] = dayData.summary;
      
      // 合并用户旅程数据
      results.userJourneys.push(...dayData.topJourneys);
    }

    // 2. 分析周级别模式
    results.weeklyPatterns = this.analyzeWeeklyPatterns(results.userJourneys);

    // 3. 生成推荐策略
    results.recommendations = this.generateRecommendationStrategy(results.weeklyPatterns);

    // 4. 输出处理后的数据
    await this.saveProcessedData(results);
    
    return results;
  }

  /**
   * 处理单天数据
   */
  async processDayData(dataDirectory, dayIndex) {
    const filePath = path.join(dataDirectory, `day_${dayIndex}.xlsx`);
    
    console.log(`处理第${dayIndex + 1}天数据: ${filePath}`);
    
    // 1. 读取和采样数据
    const rawData = await this.loadAndSampleData(filePath);
    console.log(`原始数据: ${rawData.total}条, 采样后: ${rawData.sampled.length}条`);

    // 2. 数据清洗
    const cleanedData = this.cleanData(rawData.sampled);
    console.log(`清洗后有效数据: ${cleanedData.length}条`);

    // 3. 会话识别
    const sessions = this.identifySessions(cleanedData);
    console.log(`识别出${sessions.length}个用户会话`);

    // 4. 提取关键会话
    const keySessions = this.extractKeySessions(sessions);
    console.log(`关键会话: ${keySessions.length}个`);

    // 5. 生成摘要
    const summary = this.generateDaySummary(keySessions);

    return {
      summary,
      topJourneys: keySessions.slice(0, this.config.output.maxSessionsPerDay / 7) // 每天贡献1/7
    };
  }

  /**
   * 数据采样 - 减少数据量
   */
  async loadAndSampleData(filePath) {
    // 这里假设你有读取Excel的方法
    const allData = await this.readExcelFile(filePath);
    
    if (!this.config.sampling.enabled) {
      return { total: allData.length, sampled: allData };
    }

    let sampledData;
    
    if (this.config.sampling.method === 'stratified') {
      // 分层采样：按用户类型、时间段等分层
      sampledData = this.stratifiedSampling(allData);
    } else {
      // 简单随机采样
      sampledData = this.randomSampling(allData);
    }

    return {
      total: allData.length,
      sampled: sampledData
    };
  }

  /**
   * 分层采样
   */
  stratifiedSampling(data) {
    // 按用户ID分组
    const userGroups = groupBy(data, 'userId');
    const sampledData = [];

    // 从每个用户组中采样
    Object.values(userGroups).forEach(userActions => {
      if (userActions.length >= this.config.session.minActionsPerSession) {
        // 对于活跃用户，采样更多数据
        const sampleSize = Math.max(
          Math.floor(userActions.length * this.config.sampling.ratio),
          this.config.session.minActionsPerSession
        );
        
        const sampled = this.randomSample(userActions, sampleSize);
        sampledData.push(...sampled);
      }
    });

    return sampledData;
  }

  /**
   * 数据清洗
   */
  cleanData(rawData) {
    return filter(rawData, (record) => {
      // 1. 过滤无效记录
      if (!record.timestamp || !record.userId) return false;
      
      // 2. 过滤失败的操作（可选）
      if (record.success === false && !this.isImportantFailure(record)) return false;
      
      // 3. 过滤非关键操作
      if (!this.isKeyAction(record)) return false;
      
      // 4. 过滤异常时间戳
      const timestamp = new Date(record.timestamp);
      if (isNaN(timestamp.getTime())) return false;
      
      return true;
    });
  }

  /**
   * 判断是否为关键操作
   */
  isKeyAction(record) {
    const url = record.pageUrl || '';
    const api = record.apiName || '';
    
    // 基于URL和API判断是否为关键操作
    return this.config.keyActions.some(action => {
      return url.includes(action.toLowerCase()) || 
             api.includes(action) ||
             this.matchesActionPattern(record, action);
    });
  }

  /**
   * 匹配操作模式
   */
  matchesActionPattern(record, action) {
    const patterns = {
      'CREATE_DOMAIN': ['/domain/create', 'CreateDomain'],
      'CREATE_SOURCE': ['/source/create', 'CreateSource'],
      'CREATE_SERVICE': ['/service/create', 'CreateService'],
      'CREATE_ROUTER': ['/router/create', 'CreateRouter'],
      'ENABLE_AUTH': ['/auth/', 'EnableAuth', 'CreatePolicy'],
      'PUBLISH_ROUTER': ['publish', 'deploy', 'Publish']
    };

    const actionPatterns = patterns[action] || [];
    const text = `${record.pageUrl} ${record.apiName}`.toLowerCase();
    
    return actionPatterns.some(pattern => text.includes(pattern.toLowerCase()));
  }

  /**
   * 会话识别 - 按用户和时间窗口分组
   */
  identifySessions(cleanedData) {
    const userGroups = groupBy(cleanedData, 'userId');
    const sessions = [];

    Object.entries(userGroups).forEach(([userId, userActions]) => {
      const sortedActions = sortBy(userActions, 'timestamp');
      const userSessions = this.splitIntoSessions(userId, sortedActions);
      sessions.push(...userSessions);
    });

    return sessions;
  }

  /**
   * 将用户操作分割为会话
   */
  splitIntoSessions(userId, sortedActions) {
    const sessions = [];
    let currentSession = null;
    const timeoutMs = this.config.session.timeoutMinutes * 60 * 1000;

    sortedActions.forEach((action, index) => {
      const actionTime = new Date(action.timestamp).getTime();
      
      if (!currentSession || 
          (actionTime - currentSession.lastActionTime > timeoutMs)) {
        
        // 保存上一个会话
        if (currentSession && currentSession.actions.length >= this.config.session.minActionsPerSession) {
          sessions.push(currentSession);
        }
        
        // 开始新会话
        currentSession = {
          sessionId: `${userId}_${actionTime}`,
          userId,
          startTime: actionTime,
          lastActionTime: actionTime,
          actions: [action],
          duration: 0
        };
      } else {
        // 继续当前会话
        currentSession.actions.push(action);
        currentSession.lastActionTime = actionTime;
        currentSession.duration = actionTime - currentSession.startTime;
      }
    });

    // 保存最后一个会话
    if (currentSession && currentSession.actions.length >= this.config.session.minActionsPerSession) {
      sessions.push(currentSession);
    }

    return sessions;
  }

  /**
   * 提取关键会话
   */
  extractKeySessions(sessions) {
    // 1. 过滤有效会话
    const validSessions = filter(sessions, session => {
      return session.actions.length >= this.config.session.minActionsPerSession &&
             session.duration <= this.config.session.maxSessionDuration;
    });

    // 2. 按重要性排序
    const rankedSessions = sortBy(validSessions, [
      session => -session.actions.length, // 操作数量多的优先
      session => -this.calculateSessionValue(session), // 业务价值高的优先
      session => session.duration // 时长适中的优先
    ]);

    // 3. 取前N个会话
    return take(rankedSessions, this.config.output.maxSessionsPerDay);
  }

  /**
   * 计算会话业务价值
   */
  calculateSessionValue(session) {
    let value = 0;
    
    // 完成关键操作的价值
    const keyActionValues = {
      'CREATE_DOMAIN': 10,
      'CREATE_SERVICE': 15,
      'CREATE_ROUTER': 20,
      'ENABLE_AUTH': 25,
      'PUBLISH_ROUTER': 30
    };

    session.actions.forEach(action => {
      const actionType = this.identifyActionType(action);
      value += keyActionValues[actionType] || 1;
    });

    // 完整流程的额外价值
    if (this.isCompleteFlow(session)) {
      value += 50;
    }

    return value;
  }

  /**
   * 生成日摘要
   */
  generateDaySummary(sessions) {
    const actionCounts = {};
    const flowPatterns = {};
    let totalUsers = new Set();

    sessions.forEach(session => {
      totalUsers.add(session.userId);
      
      // 统计操作频次
      session.actions.forEach(action => {
        const actionType = this.identifyActionType(action);
        actionCounts[actionType] = (actionCounts[actionType] || 0) + 1;
      });

      // 统计流程模式
      const flow = this.extractFlow(session);
      const flowKey = flow.join(' -> ');
      flowPatterns[flowKey] = (flowPatterns[flowKey] || 0) + 1;
    });

    return {
      totalSessions: sessions.length,
      totalUsers: totalUsers.size,
      avgActionsPerSession: sessions.reduce((sum, s) => sum + s.actions.length, 0) / sessions.length,
      topActions: this.getTopN(actionCounts, 10),
      topFlows: this.getTopN(flowPatterns, 5),
      completionRate: this.calculateCompletionRate(sessions)
    };
  }

  /**
   * 获取Top N项
   */
  getTopN(obj, n) {
    return Object.entries(obj)
      .sort(([,a], [,b]) => b - a)
      .slice(0, n)
      .map(([key, value]) => ({ action: key, count: value }));
  }

  /**
   * 保存处理后的数据
   */
  async saveProcessedData(results) {
    const outputDir = './processed_data';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir);
    }

    // 保存摘要数据（适合AI分析）
    const summaryData = {
      overview: {
        totalDays: 7,
        totalSessions: Object.values(results.dailyStats).reduce((sum, day) => sum + day.totalSessions, 0),
        totalUsers: Object.values(results.dailyStats).reduce((sum, day) => sum + day.totalUsers, 0)
      },
      patterns: results.weeklyPatterns,
      recommendations: results.recommendations,
      dailyTrends: results.dailyStats
    };

    fs.writeFileSync(
      path.join(outputDir, 'weekly_summary.json'),
      JSON.stringify(summaryData, null, 2)
    );

    // 保存详细的用户旅程数据（供进一步分析）
    fs.writeFileSync(
      path.join(outputDir, 'user_journeys.json'),
      JSON.stringify(results.userJourneys.slice(0, 100), null, 2) // 只保存前100个
    );

    console.log('处理后的数据已保存到:', outputDir);
    console.log('摘要文件大小:', this.getFileSize(path.join(outputDir, 'weekly_summary.json')));
  }

  /**
   * 获取文件大小
   */
  getFileSize(filePath) {
    const stats = fs.statSync(filePath);
    return `${(stats.size / 1024).toFixed(2)} KB`;
  }

  // ... 其他辅助方法
  randomSample(array, size) {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, size);
  }

  identifyActionType(action) {
    // 实现操作类型识别逻辑
    return 'UNKNOWN_ACTION';
  }

  extractFlow(session) {
    // 提取会话的操作流程
    return session.actions.map(action => this.identifyActionType(action));
  }

  isCompleteFlow(session) {
    // 判断是否为完整的业务流程
    const flow = this.extractFlow(session);
    const completeFlows = [
      ['CREATE_DOMAIN', 'CREATE_SOURCE', 'CREATE_SERVICE', 'CREATE_ROUTER'],
      ['CREATE_SERVICE', 'CREATE_ROUTER', 'ENABLE_AUTH', 'PUBLISH_ROUTER']
    ];
    
    return completeFlows.some(completeFlow => 
      completeFlow.every(step => flow.includes(step))
    );
  }

  calculateCompletionRate(sessions) {
    const completedSessions = sessions.filter(session => this.isCompleteFlow(session));
    return completedSessions.length / sessions.length;
  }

  analyzeWeeklyPatterns(userJourneys) {
    // 分析周级别的用户行为模式
    return {
      commonPaths: [],
      dropOffPoints: [],
      successPatterns: []
    };
  }

  generateRecommendationStrategy(patterns) {
    // 基于模式生成推荐策略
    return {
      nextStepRecommendations: {},
      interventionPoints: [],
      optimizationSuggestions: []
    };
  }

  async readExcelFile(filePath) {
    // 实现Excel文件读取
    // 这里需要根据你的实际文件格式实现
    return [];
  }

  isImportantFailure(record) {
    // 判断是否为重要的失败操作
    return false;
  }
}

module.exports = LargeScaleDataProcessor;

// 使用示例
async function main() {
  const processor = new LargeScaleDataProcessor();
  
  try {
    const results = await processor.processWeeklyData('./raw_data');
    console.log('数据处理完成！');
    console.log('摘要:', results.overview);
  } catch (error) {
    console.error('处理失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}
